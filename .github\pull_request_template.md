<!--

Thanks for sending a pull request! Please make sure you click the link above to
view the contribution guidelines, then fill out the blanks below.
Please, make sure if your contribution is for UE4 version of CARLA you merge against ue4-dev branch. 
if it is for UE5 version of CARLA you merge agaisnt ue5-dev branch

Checklist:

  - [ ] Your branch is up-to-date with the `ue4-dev/ue5-dev` branch and tested with latest changes
  - [ ] Extended the README / documentation, if necessary
  - [ ] Code compiles correctly
  - [ ] All tests passing with `make check` (only Linux and ue4-dev)
  - [ ] If relevant, update CHANGELOG.md with your changes

-->

#### Description

<!-- Please explain the changes you made here as detailed as possible. -->

Fixes #  <!-- If fixes an issue, please add here the issue number. -->

#### Where has this been tested?

  * **Platform(s):** ...
  * **Python version(s):** ...
  * **Unreal Engine version(s):** ...

#### Possible Drawbacks

<!-- What are the possible side-effects or negative impacts of the code change? -->
