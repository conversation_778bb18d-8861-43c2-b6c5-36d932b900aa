// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Header.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "Header.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define builtin_interfaces_msg_Time_max_cdr_typesize 8ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define builtin_interfaces_msg_Time_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;

std_msgs::msg::Header::Header()
{
    m_frame_id ="";
}

std_msgs::msg::Header::~Header()
{
}

std_msgs::msg::Header::Header(
        const Header& x)
{
    m_stamp = x.m_stamp;
    m_frame_id = x.m_frame_id;
}

std_msgs::msg::Header::Header(
        Header&& x) noexcept
{
    m_stamp = std::move(x.m_stamp);
    m_frame_id = std::move(x.m_frame_id);
}

std_msgs::msg::Header& std_msgs::msg::Header::operator =(
        const Header& x)
{
    m_stamp = x.m_stamp;
    m_frame_id = x.m_frame_id;

    return *this;
}

std_msgs::msg::Header& std_msgs::msg::Header::operator =(
        Header&& x) noexcept
{
    m_stamp = std::move(x.m_stamp);
    m_frame_id = std::move(x.m_frame_id);

    return *this;
}

bool std_msgs::msg::Header::operator ==(
        const Header& x) const
{
    return (m_stamp == x.m_stamp && m_frame_id == x.m_frame_id);
}

bool std_msgs::msg::Header::operator !=(
        const Header& x) const
{
    return !(*this == x);
}

size_t std_msgs::msg::Header::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return std_msgs_msg_Header_max_cdr_typesize;
}

size_t std_msgs::msg::Header::getCdrSerializedSize(
        const std_msgs::msg::Header& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += builtin_interfaces::msg::Time::getCdrSerializedSize(data.stamp(), current_alignment);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4) + data.frame_id().size() + 1;

    return current_alignment - initial_alignment;
}

void std_msgs::msg::Header::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_stamp;
    scdr << m_frame_id.c_str();
}

void std_msgs::msg::Header::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_stamp;
    dcdr >> m_frame_id;
}

/*!
 * @brief This function copies the value in member stamp
 * @param _stamp New value to be copied in member stamp
 */
void std_msgs::msg::Header::stamp(
        const builtin_interfaces::msg::Time& _stamp)
{
    m_stamp = _stamp;
}

/*!
 * @brief This function moves the value in member stamp
 * @param _stamp New value to be moved in member stamp
 */
void std_msgs::msg::Header::stamp(
        builtin_interfaces::msg::Time&& _stamp)
{
    m_stamp = std::move(_stamp);
}

/*!
 * @brief This function returns a constant reference to member stamp
 * @return Constant reference to member stamp
 */
const builtin_interfaces::msg::Time& std_msgs::msg::Header::stamp() const
{
    return m_stamp;
}

/*!
 * @brief This function returns a reference to member stamp
 * @return Reference to member stamp
 */
builtin_interfaces::msg::Time& std_msgs::msg::Header::stamp()
{
    return m_stamp;
}
/*!
 * @brief This function copies the value in member frame_id
 * @param _frame_id New value to be copied in member frame_id
 */
void std_msgs::msg::Header::frame_id(
        const std::string& _frame_id)
{
    m_frame_id = _frame_id;
}

/*!
 * @brief This function moves the value in member frame_id
 * @param _frame_id New value to be moved in member frame_id
 */
void std_msgs::msg::Header::frame_id(
        std::string&& _frame_id)
{
    m_frame_id = std::move(_frame_id);
}

/*!
 * @brief This function returns a constant reference to member frame_id
 * @return Constant reference to member frame_id
 */
const std::string& std_msgs::msg::Header::frame_id() const
{
    return m_frame_id;
}

/*!
 * @brief This function returns a reference to member frame_id
 * @return Reference to member frame_id
 */
std::string& std_msgs::msg::Header::frame_id()
{
    return m_frame_id;
}


size_t std_msgs::msg::Header::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return std_msgs_msg_Header_max_key_cdr_typesize;
}

bool std_msgs::msg::Header::isKeyDefined()
{
    return false;
}

void std_msgs::msg::Header::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
