#!/usr/bin/env python3

"""
Hybrid NUREC Manual Driving

This script combines the best of both worlds:
1. Uses OpenDRIVE map from USDZ for proper ground physics and collision
2. Uses NUREC cameras for photorealistic rendering
3. Provides stable manual vehicle control

The approach:
- Load OpenDRIVE map from USDZ file for proper ground collision
- Spawn vehicle on the proper CARLA map
- Use NUREC cameras for visual rendering
- Get stable physics with proper ground contact

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    ESC         : Quit

Usage:
    python3 hybrid_nurec_drive.py --usdz-filename /path/to/scenario.usdz
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time

from typing import Optional

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("HybridNurecDrive")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class HybridNurecDrive:
    """
    Hybrid NUREC driving that combines OpenDRIVE physics with NUREC rendering
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.nurec_scenario = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Initialize pygame for input handling
        pygame.init()
        pygame.display.set_mode((1, 1))
        self.clock = pygame.time.Clock()
        
        # Control parameters
        self.throttle_increment = 0.02
        self.brake_increment = 0.2
        self.steer_increment = 0.015
        self.steer_decay = 0.9
        
        logger.info("Hybrid NUREC drive initialized")
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def setup_nurec_scenario(self):
        """Setup NUREC scenario which handles coordinate alignment automatically"""
        try:
            logger.info(f"Setting up NUREC scenario: {self.args.usdz_filename}")

            # Create NUREC scenario - this handles coordinate alignment automatically
            self.nurec_scenario = NurecScenario(
                self.client,
                self.args.usdz_filename,
                port=self.args.nurec_port,
                move_spectator=True
            )
            self.nurec_scenario.__enter__()

            # Get the world that NUREC is using (it may load OpenDRIVE automatically)
            self.world = self.client.get_world()

            # Enable synchronous mode for stable physics
            settings = self.world.get_settings()
            settings.synchronous_mode = True
            settings.fixed_delta_seconds = 0.05  # 20 FPS
            self.world.apply_settings(settings)

            logger.info("✅ NUREC scenario setup complete with coordinate alignment")
            return True

        except Exception as e:
            logger.error(f"Failed to setup NUREC scenario: {e}")
            return False
    
    def setup_nurec_cameras(self):
        """Setup NUREC cameras for photorealistic rendering"""
        try:
            logger.info("Setting up NUREC cameras for rendering...")

            # Setup NUREC display
            from pygame_display import PygameDisplay
            self.display = PygameDisplay()

            # Add NUREC camera
            self.nurec_scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: self.display.setImage(image, (1, 1), (0, 0)),
                framerate=20,
                resolution_ratio=0.75
            )

            # Start NUREC rendering
            self.nurec_scenario.start_replay()
            logger.info("✅ NUREC cameras setup complete")

            return True

        except Exception as e:
            logger.error(f"Failed to setup NUREC cameras: {e}")
            return False
    
    def setup_vehicle_control(self):
        """Setup vehicle for manual control using NUREC's ego vehicle"""
        try:
            # Use NUREC's ego vehicle instead of spawning a new one
            from constants import EGO_TRACK_ID

            if EGO_TRACK_ID not in self.nurec_scenario.actor_mapping:
                logger.error("Ego vehicle not found in NUREC scenario")
                return False

            # Get the ego vehicle from NUREC
            ego_actor = self.nurec_scenario.actor_mapping[EGO_TRACK_ID]
            self.vehicle = ego_actor.actor_inst
            logger.info(f"✅ Using NUREC ego vehicle: {self.vehicle.id}")

            # Enable physics for manual control
            ego_actor.set_physics(True, self.nurec_scenario.get_sim_time())
            logger.info("✅ Vehicle physics enabled for manual control")

            # Configure vehicle physics
            self.configure_vehicle_physics()

            # Get initial position
            initial_transform = self.vehicle.get_transform()
            logger.info(f"✅ Vehicle position: ({initial_transform.location.x:.1f}, {initial_transform.location.y:.1f}, {initial_transform.location.z:.1f})")

            # Set initial control
            self.vehicle.apply_control(self.control)

            return True

        except Exception as e:
            logger.error(f"Failed to setup vehicle control: {e}")
            return False
    
    def configure_vehicle_physics(self):
        """Configure vehicle physics for stability"""
        try:
            physics_control = self.vehicle.get_physics_control()
            
            # Improve wheel physics
            for wheel in physics_control.wheels:
                wheel.tire_friction = 3.5
                wheel.damping_rate = 1.5
                wheel.max_steer_angle = 35.0
                wheel.radius = max(wheel.radius, 0.3)
            
            # Improve vehicle stability
            physics_control.use_gear_autobox = True
            physics_control.gear_switch_time = 0.5
            physics_control.clutch_strength = 10.0
            physics_control.final_ratio = 4.0
            physics_control.mass = max(physics_control.mass, 1500.0)
            physics_control.center_of_mass.z = -0.5
            
            self.vehicle.apply_physics_control(physics_control)
            logger.info("✅ Vehicle physics configured")
            
        except Exception as e:
            logger.warning(f"Failed to configure vehicle physics: {e}")
    
    def handle_input(self):
        """Handle keyboard input for vehicle control"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_r:
                    self.restart_vehicle()
                elif event.key == pygame.K_p:
                    self.toggle_autopilot()
                elif event.key == pygame.K_q:
                    self.control.gear = 1 if self.control.reverse else -1
                    logger.info(f"Gear: {'Reverse' if self.control.gear < 0 else 'Forward'}")
        
        # Get current key states
        keys = pygame.key.get_pressed()
        
        if not self.autopilot_enabled:
            # Throttle/Brake
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                self.control.throttle = min(self.control.throttle + self.throttle_increment, 1.0)
            else:
                self.control.throttle = max(self.control.throttle - 0.02, 0.0)
            
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                self.control.brake = min(self.control.brake + self.brake_increment, 1.0)
            else:
                self.control.brake = max(self.control.brake - 0.05, 0.0)
            
            # Steering
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                self.control.steer = max(self.control.steer - self.steer_increment, -1.0)
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                self.control.steer = min(self.control.steer + self.steer_increment, 1.0)
            else:
                self.control.steer *= self.steer_decay
                if abs(self.control.steer) < 0.01:
                    self.control.steer = 0.0
            
            # Hand brake
            self.control.hand_brake = keys[pygame.K_SPACE]
            self.control.reverse = self.control.gear < 0
            
            # Apply control
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        return True
    
    def toggle_autopilot(self):
        """Toggle autopilot mode"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        logger.info(f"Autopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def restart_vehicle(self):
        """Restart vehicle at a spawn point"""
        if self.vehicle and self.world:
            try:
                spawn_points = self.world.get_map().get_spawn_points()
                if spawn_points:
                    import random
                    spawn_point = random.choice(spawn_points)
                    self.vehicle.set_transform(spawn_point)
                    self.control = carla.VehicleControl()
                    self.vehicle.apply_control(self.control)
                    logger.info("Vehicle restarted")
            except Exception as e:
                logger.warning(f"Failed to restart vehicle: {e}")
    
    def print_status(self):
        """Print current status"""
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * (velocity.x**2 + velocity.y**2 + velocity.z**2)**0.5
            transform = self.vehicle.get_transform()
            
            status = (
                f"Speed: {speed_kmh:6.1f} km/h | "
                f"Throttle: {self.control.throttle:4.2f} | "
                f"Brake: {self.control.brake:4.2f} | "
                f"Steer: {self.control.steer:5.2f} | "
                f"Autopilot: {'ON ' if self.autopilot_enabled else 'OFF'} | "
                f"Pos: ({transform.location.x:6.1f}, {transform.location.y:6.1f}, {transform.location.z:6.1f})"
            )
            print(f"\r{status}", end="", flush=True)
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False

        if not self.setup_nurec_scenario():
            return False

        if not self.setup_nurec_cameras():
            return False

        if not self.setup_vehicle_control():
            return False
        
        logger.info("🚗 Starting hybrid NUREC driving session...")
        logger.info("Controls: WASD=Drive, P=Autopilot, R=Restart, ESC=Quit")
        logger.info("✨ Enjoy OpenDRIVE physics with NUREC visuals!")
        
        running = True
        status_counter = 0
        
        try:
            while running:
                running = self.handle_input()
                
                # Tick world and NUREC
                if self.world:
                    self.world.tick()
                if self.nurec_scenario:
                    self.nurec_scenario.tick()
                
                # Print status
                status_counter += 1
                if status_counter >= 20:
                    self.print_status()
                    status_counter = 0
                
                self.clock.tick(20)
                
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        except Exception as e:
            print(f"\nError: {e}")
            logger.error(f"Error in main loop: {e}")
        finally:
            print("\nCleaning up...")
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        if self.vehicle:
            try:
                self.vehicle.destroy()
            except:
                pass
        
        if self.world:
            try:
                settings = self.world.get_settings()
                settings.synchronous_mode = False
                self.world.apply_settings(settings)
            except:
                pass
        
        if self.nurec_scenario:
            try:
                self.nurec_scenario.__exit__(None, None, None)
            except:
                pass
        
        pygame.quit()
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Hybrid NUREC Manual Driving")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    hybrid_drive = HybridNurecDrive(args)
    success = hybrid_drive.run()
    
    if success:
        logger.info("Hybrid driving session completed successfully")
    else:
        logger.error("Hybrid driving session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
