#!/usr/bin/env python3

"""
Hybrid NUREC Manual Driving

This script combines the best of both worlds:
1. Uses OpenDRIVE map from USDZ for proper ground physics and collision
2. Uses NUREC cameras for photorealistic rendering
3. Provides stable manual vehicle control

The approach:
- Load OpenDRIVE map from USDZ file for proper ground collision
- Spawn vehicle on the proper CARLA map
- Use NUREC cameras for visual rendering
- Get stable physics with proper ground contact

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    ESC         : Quit

Usage:
    python3 hybrid_nurec_drive.py --usdz-filename /path/to/scenario.usdz
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time
import zipfile
import tempfile
import os
from typing import Optional

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("HybridNurecDrive")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class HybridNurecDrive:
    """
    Hybrid NUREC driving that combines OpenDRIVE physics with NUREC rendering
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.nurec_scenario = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Initialize pygame for input handling
        pygame.init()
        pygame.display.set_mode((1, 1))
        self.clock = pygame.time.Clock()
        
        # Control parameters
        self.throttle_increment = 0.02
        self.brake_increment = 0.2
        self.steer_increment = 0.015
        self.steer_decay = 0.9
        
        logger.info("Hybrid NUREC drive initialized")
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def load_opendrive_world(self):
        """Load OpenDRIVE world from USDZ for proper physics"""
        try:
            logger.info(f"Loading OpenDRIVE map from USDZ: {self.args.usdz_filename}")
            
            with zipfile.ZipFile(self.args.usdz_filename, 'r') as usdz_file:
                # Extract the map.xodr from the .usdz file
                xodr_data = usdz_file.read('map.xodr').decode('utf-8')
                logger.info("Successfully extracted OpenDRIVE data from USDZ")
            
            # Load the world with the OpenDRIVE map for proper ground physics
            vertex_distance = 2.0
            max_road_length = 500.0
            wall_height = 1.0
            extra_lane_width = 0.6
            
            settings = carla.OpendriveGenerationParameters(
                vertex_distance=vertex_distance,
                max_road_length=max_road_length,
                wall_height=wall_height,
                additional_width=extra_lane_width,
                smooth_junctions=True,
                enable_mesh_visibility=True
            )
            
            # Generate the world with proper ground collision
            self.world = self.client.generate_opendrive_world(xodr_data, settings)
            logger.info("✅ OpenDRIVE world loaded with proper ground physics")
            
            # Enable synchronous mode for stable physics
            settings = self.world.get_settings()
            settings.synchronous_mode = True
            settings.fixed_delta_seconds = 0.05  # 20 FPS
            self.world.apply_settings(settings)
            logger.info("✅ Synchronous mode enabled")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load OpenDRIVE world: {e}")
            return False
    
    def setup_nurec_cameras(self):
        """Setup NUREC cameras for photorealistic rendering"""
        try:
            logger.info("Setting up NUREC cameras for rendering...")
            
            # Create NUREC scenario for camera access
            self.nurec_scenario = NurecScenario(
                self.client, 
                self.args.usdz_filename, 
                port=self.args.nurec_port
            )
            self.nurec_scenario.__enter__()
            
            # Setup NUREC display
            from pygame_display import PygameDisplay
            self.display = PygameDisplay()
            
            # Add NUREC camera
            self.nurec_scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: self.display.setImage(image, (1, 1), (0, 0)),
                framerate=20,
                resolution_ratio=0.75
            )
            
            # Start NUREC rendering
            self.nurec_scenario.start_replay()
            logger.info("✅ NUREC cameras setup complete")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup NUREC cameras: {e}")
            return False
    
    def spawn_vehicle(self):
        """Spawn vehicle on the OpenDRIVE map"""
        try:
            # Get spawn points from the OpenDRIVE map
            spawn_points = self.world.get_map().get_spawn_points()
            if not spawn_points:
                logger.error("No spawn points found on the map")
                return False
            
            # Choose a spawn point
            import random
            spawn_point = random.choice(spawn_points)
            logger.info(f"Selected spawn point: ({spawn_point.location.x:.1f}, {spawn_point.location.y:.1f}, {spawn_point.location.z:.1f})")
            
            # Get vehicle blueprint
            blueprint_library = self.world.get_blueprint_library()
            vehicle_bp = blueprint_library.find('vehicle.tesla.model3')
            
            # Spawn the vehicle
            self.vehicle = self.world.spawn_actor(vehicle_bp, spawn_point)
            logger.info(f"✅ Vehicle spawned: {self.vehicle.id}")
            
            # Configure vehicle physics
            self.configure_vehicle_physics()
            
            # Set initial control
            self.vehicle.apply_control(self.control)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to spawn vehicle: {e}")
            return False
    
    def configure_vehicle_physics(self):
        """Configure vehicle physics for stability"""
        try:
            physics_control = self.vehicle.get_physics_control()
            
            # Improve wheel physics
            for wheel in physics_control.wheels:
                wheel.tire_friction = 3.5
                wheel.damping_rate = 1.5
                wheel.max_steer_angle = 35.0
                wheel.radius = max(wheel.radius, 0.3)
            
            # Improve vehicle stability
            physics_control.use_gear_autobox = True
            physics_control.gear_switch_time = 0.5
            physics_control.clutch_strength = 10.0
            physics_control.final_ratio = 4.0
            physics_control.mass = max(physics_control.mass, 1500.0)
            physics_control.center_of_mass.z = -0.5
            
            self.vehicle.apply_physics_control(physics_control)
            logger.info("✅ Vehicle physics configured")
            
        except Exception as e:
            logger.warning(f"Failed to configure vehicle physics: {e}")
    
    def handle_input(self):
        """Handle keyboard input for vehicle control"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_r:
                    self.restart_vehicle()
                elif event.key == pygame.K_p:
                    self.toggle_autopilot()
                elif event.key == pygame.K_q:
                    self.control.gear = 1 if self.control.reverse else -1
                    logger.info(f"Gear: {'Reverse' if self.control.gear < 0 else 'Forward'}")
        
        # Get current key states
        keys = pygame.key.get_pressed()
        
        if not self.autopilot_enabled:
            # Throttle/Brake
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                self.control.throttle = min(self.control.throttle + self.throttle_increment, 1.0)
            else:
                self.control.throttle = max(self.control.throttle - 0.02, 0.0)
            
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                self.control.brake = min(self.control.brake + self.brake_increment, 1.0)
            else:
                self.control.brake = max(self.control.brake - 0.05, 0.0)
            
            # Steering
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                self.control.steer = max(self.control.steer - self.steer_increment, -1.0)
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                self.control.steer = min(self.control.steer + self.steer_increment, 1.0)
            else:
                self.control.steer *= self.steer_decay
                if abs(self.control.steer) < 0.01:
                    self.control.steer = 0.0
            
            # Hand brake
            self.control.hand_brake = keys[pygame.K_SPACE]
            self.control.reverse = self.control.gear < 0
            
            # Apply control
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        return True
    
    def toggle_autopilot(self):
        """Toggle autopilot mode"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        logger.info(f"Autopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def restart_vehicle(self):
        """Restart vehicle at a spawn point"""
        if self.vehicle and self.world:
            try:
                spawn_points = self.world.get_map().get_spawn_points()
                if spawn_points:
                    import random
                    spawn_point = random.choice(spawn_points)
                    self.vehicle.set_transform(spawn_point)
                    self.control = carla.VehicleControl()
                    self.vehicle.apply_control(self.control)
                    logger.info("Vehicle restarted")
            except Exception as e:
                logger.warning(f"Failed to restart vehicle: {e}")
    
    def print_status(self):
        """Print current status"""
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * (velocity.x**2 + velocity.y**2 + velocity.z**2)**0.5
            transform = self.vehicle.get_transform()
            
            status = (
                f"Speed: {speed_kmh:6.1f} km/h | "
                f"Throttle: {self.control.throttle:4.2f} | "
                f"Brake: {self.control.brake:4.2f} | "
                f"Steer: {self.control.steer:5.2f} | "
                f"Autopilot: {'ON ' if self.autopilot_enabled else 'OFF'} | "
                f"Pos: ({transform.location.x:6.1f}, {transform.location.y:6.1f}, {transform.location.z:6.1f})"
            )
            print(f"\r{status}", end="", flush=True)
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False
        
        if not self.load_opendrive_world():
            return False
        
        if not self.setup_nurec_cameras():
            return False
        
        if not self.spawn_vehicle():
            return False
        
        logger.info("🚗 Starting hybrid NUREC driving session...")
        logger.info("Controls: WASD=Drive, P=Autopilot, R=Restart, ESC=Quit")
        logger.info("✨ Enjoy OpenDRIVE physics with NUREC visuals!")
        
        running = True
        status_counter = 0
        
        try:
            while running:
                running = self.handle_input()
                
                # Tick world and NUREC
                if self.world:
                    self.world.tick()
                if self.nurec_scenario:
                    self.nurec_scenario.tick()
                
                # Print status
                status_counter += 1
                if status_counter >= 20:
                    self.print_status()
                    status_counter = 0
                
                self.clock.tick(20)
                
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        except Exception as e:
            print(f"\nError: {e}")
            logger.error(f"Error in main loop: {e}")
        finally:
            print("\nCleaning up...")
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        if self.vehicle:
            try:
                self.vehicle.destroy()
            except:
                pass
        
        if self.world:
            try:
                settings = self.world.get_settings()
                settings.synchronous_mode = False
                self.world.apply_settings(settings)
            except:
                pass
        
        if self.nurec_scenario:
            try:
                self.nurec_scenario.__exit__(None, None, None)
            except:
                pass
        
        pygame.quit()
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Hybrid NUREC Manual Driving")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    hybrid_drive = HybridNurecDrive(args)
    success = hybrid_drive.run()
    
    if success:
        logger.info("Hybrid driving session completed successfully")
    else:
        logger.error("Hybrid driving session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
