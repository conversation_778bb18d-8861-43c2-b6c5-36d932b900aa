// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file AckermannDrive.h
 * This header file contains the declaration of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifndef _FAST_DDS_GENERATED_ACKERMANN_MSGS_MSG_ACKERMANNDRIVE_H_
#define _FAST_DDS_GENERATED_ACKERMANN_MSGS_MSG_ACKERMANNDRIVE_H_


#include <fastrtps/utils/fixed_size_string.hpp>

#include <stdint.h>
#include <array>
#include <string>
#include <vector>
#include <map>
#include <bitset>

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#define eProsima_user_DllExport __declspec( dllexport )
#else
#define eProsima_user_DllExport
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define eProsima_user_DllExport
#endif  // _WIN32

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#if defined(ACKERMANNDRIVE_SOURCE)
#define ACKERMANNDRIVE_DllAPI __declspec( dllexport )
#else
#define ACKERMANNDRIVE_DllAPI __declspec( dllimport )
#endif // ACKERMANNDRIVE_SOURCE
#else
#define ACKERMANNDRIVE_DllAPI
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define ACKERMANNDRIVE_DllAPI
#endif // _WIN32

namespace eprosima {
namespace fastcdr {
class Cdr;
} // namespace fastcdr
} // namespace eprosima


namespace ackermann_msgs {
    namespace msg {
        /*!
         * @brief This class represents the structure AckermannDrive defined by the user in the IDL file.
         * @ingroup AckermannDrive
         */
        class AckermannDrive
        {
        public:

            /*!
             * @brief Default constructor.
             */
            eProsima_user_DllExport AckermannDrive();

            /*!
             * @brief Default destructor.
             */
            eProsima_user_DllExport ~AckermannDrive();

            /*!
             * @brief Copy constructor.
             * @param x Reference to the object ackermann_msgs::msg::AckermannDrive that will be copied.
             */
            eProsima_user_DllExport AckermannDrive(
                    const AckermannDrive& x);

            /*!
             * @brief Move constructor.
             * @param x Reference to the object ackermann_msgs::msg::AckermannDrive that will be copied.
             */
            eProsima_user_DllExport AckermannDrive(
                    AckermannDrive&& x) noexcept;

            /*!
             * @brief Copy assignment.
             * @param x Reference to the object ackermann_msgs::msg::AckermannDrive that will be copied.
             */
            eProsima_user_DllExport AckermannDrive& operator =(
                    const AckermannDrive& x);

            /*!
             * @brief Move assignment.
             * @param x Reference to the object ackermann_msgs::msg::AckermannDrive that will be copied.
             */
            eProsima_user_DllExport AckermannDrive& operator =(
                    AckermannDrive&& x) noexcept;

            /*!
             * @brief Comparison operator.
             * @param x ackermann_msgs::msg::AckermannDrive object to compare.
             */
            eProsima_user_DllExport bool operator ==(
                    const AckermannDrive& x) const;

            /*!
             * @brief Comparison operator.
             * @param x ackermann_msgs::msg::AckermannDrive object to compare.
             */
            eProsima_user_DllExport bool operator !=(
                    const AckermannDrive& x) const;

            /*!
             * @brief This function sets a value in member steering_angle
             * @param _steering_angle New value for member steering_angle
             */
            eProsima_user_DllExport void steering_angle(
                    float _steering_angle);

            /*!
             * @brief This function returns the value of member steering_angle
             * @return Value of member steering_angle
             */
            eProsima_user_DllExport float steering_angle() const;

            /*!
             * @brief This function returns a reference to member steering_angle
             * @return Reference to member steering_angle
             */
            eProsima_user_DllExport float& steering_angle();

            /*!
             * @brief This function sets a value in member steering_angle_velocity
             * @param _steering_angle_velocity New value for member steering_angle_velocity
             */
            eProsima_user_DllExport void steering_angle_velocity(
                    float _steering_angle_velocity);

            /*!
             * @brief This function returns the value of member steering_angle_velocity
             * @return Value of member steering_angle_velocity
             */
            eProsima_user_DllExport float steering_angle_velocity() const;

            /*!
             * @brief This function returns a reference to member steering_angle_velocity
             * @return Reference to member steering_angle_velocity
             */
            eProsima_user_DllExport float& steering_angle_velocity();

            /*!
             * @brief This function sets a value in member speed
             * @param _speed New value for member speed
             */
            eProsima_user_DllExport void speed(
                    float _speed);

            /*!
             * @brief This function returns the value of member speed
             * @return Value of member speed
             */
            eProsima_user_DllExport float speed() const;

            /*!
             * @brief This function returns a reference to member speed
             * @return Reference to member speed
             */
            eProsima_user_DllExport float& speed();

            /*!
             * @brief This function sets a value in member acceleration
             * @param _acceleration New value for member acceleration
             */
            eProsima_user_DllExport void acceleration(
                    float _acceleration);

            /*!
             * @brief This function returns the value of member acceleration
             * @return Value of member acceleration
             */
            eProsima_user_DllExport float acceleration() const;

            /*!
             * @brief This function returns a reference to member acceleration
             * @return Reference to member acceleration
             */
            eProsima_user_DllExport float& acceleration();

            /*!
             * @brief This function sets a value in member jerk
             * @param _jerk New value for member jerk
             */
            eProsima_user_DllExport void jerk(
                    float _jerk);

            /*!
             * @brief This function returns the value of member jerk
             * @return Value of member jerk
             */
            eProsima_user_DllExport float jerk() const;

            /*!
             * @brief This function returns a reference to member jerk
             * @return Reference to member jerk
             */
            eProsima_user_DllExport float& jerk();


            /*!
            * @brief This function returns the maximum serialized size of an object
            * depending on the buffer alignment.
            * @param current_alignment Buffer alignment.
            * @return Maximum serialized size.
            */
            eProsima_user_DllExport static size_t getMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function returns the serialized size of a data depending on the buffer alignment.
             * @param data Data which is calculated its serialized size.
             * @param current_alignment Buffer alignment.
             * @return Serialized size.
             */
            eProsima_user_DllExport static size_t getCdrSerializedSize(
                    const ackermann_msgs::msg::AckermannDrive& data,
                    size_t current_alignment = 0);


            /*!
             * @brief This function serializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serialize(
                    eprosima::fastcdr::Cdr& cdr) const;

            /*!
             * @brief This function deserializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void deserialize(
                    eprosima::fastcdr::Cdr& cdr);



            /*!
             * @brief This function returns the maximum serialized size of the Key of an object
             * depending on the buffer alignment.
             * @param current_alignment Buffer alignment.
             * @return Maximum serialized size.
             */
            eProsima_user_DllExport static size_t getKeyMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function tells you if the Key has been defined for this type
             */
            eProsima_user_DllExport static bool isKeyDefined();

            /*!
             * @brief This function serializes the key members of an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serializeKey(
                    eprosima::fastcdr::Cdr& cdr) const;

        private:

            float m_steering_angle;
            float m_steering_angle_velocity;
            float m_speed;
            float m_acceleration;
            float m_jerk;

        };
    } // namespace msg
} // namespace ackermann_msgs

#endif // _FAST_DDS_GENERATED_ACKERMANN_MSGS_MSG_ACKERMANNDRIVE_H_

