# CARLA-NUREC-Cosmos Transfer Integration Pipeline

This enhanced pipeline integrates CARLA simulation with NUREC neural reconstruction and Cosmos Transfer for generating diverse training datasets from a single baseline scenario.

## Overview

The pipeline leverages Cosmos Transfer to programmatically generate a massive number of visually diverse training examples from a single, baseline CARLA simulation. The workflow includes:

1. **Structured Data Generation in CARLA**: Design and run scenarios in CARLA while recording RGB camera output and ground-truth data channels (depth maps, semantic segmentation)
2. **Input Conditioning for Cosmos Transfer**: Package recorded data and control maps as input to Cosmos Transfer
3. **Prompt-Driven Semantic Control**: Use natural language prompts to specify visual transformations (weather, lighting, time of day, location)
4. **Generative Output**: Cosmos Transfer generates new video sequences with transformed visual styles while preserving structure and motion

## Key Features

- **Multi-Sensor Synchronization**: Captures RGB, depth, and semantic segmentation data simultaneously
- **Environmental Condition Controls**: Programmatically change weather, lighting, and atmospheric conditions
- **Cosmos Transfer Data Preparation**: Exports data in formats suitable for Cosmos Transfer processing
- **Batch Processing**: Process multiple scenarios with different environmental conditions
- **Comprehensive Logging**: Detailed logging and error handling throughout the pipeline
- **Real-time Visualization**: Pygame-based visualization of captured data

## Installation and Setup

### Prerequisites

1. **CARLA Server**: Ensure CARLA server is running
2. **NUREC Integration**: Install NUREC components and dependencies
3. **Python Dependencies**: Install required packages

```bash
pip install numpy pillow pygame pathlib dataclasses
```

### Directory Structure

```
PythonAPI/examples/
├── improved_load_usdz_scene_3.py          # Main enhanced pipeline
├── cosmos_transfer_example.py             # Example usage script
├── COSMOS_TRANSFER_README.md              # This documentation
├── nvidia/nurec/                          # NUREC integration components
│   ├── nurec_integration.py
│   ├── pygame_display.py
│   └── utils.py
└── cosmos_transfer_data/                  # Output directory (created automatically)
    ├── batch_0001_20250118_143022/        # Individual batch directories
    │   ├── metadata.json                  # Batch metadata
    │   ├── prompt.txt                     # Cosmos Transfer prompt
    │   ├── rgb_frames/                    # RGB image sequence
    │   ├── depth_frames/                  # Depth map sequence
    │   └── semantic_frames/               # Semantic segmentation sequence
    └── cosmos_transfer_summary.json       # Overall summary report
```

## Usage Examples

### Basic Usage

Process a single NUREC scenario with default environmental conditions:

```bash
python improved_load_usdz_scene_3.py \
    --usdz-filename path/to/scenario.usdz \
    --enable-multi-sensor \
    --export-cosmos-data
```

### Advanced Usage

Process with custom environmental conditions and settings:

```bash
python improved_load_usdz_scene_3.py \
    --usdz-filename path/to/scenario.usdz \
    --enable-multi-sensor \
    --export-cosmos-data \
    --environmental-conditions CLEAR_DAY HEAVY_RAIN SUNSET NIGHT FOG \
    --sequence-length 60 \
    --output-dir ./my_cosmos_data
```

### Batch Processing

Process multiple scenarios:

```bash
# Process each scenario file individually
for scenario in scenarios/*.usdz; do
    python improved_load_usdz_scene_3.py \
        --usdz-filename "$scenario" \
        --enable-multi-sensor \
        --export-cosmos-data \
        --output-dir "./batch_output/$(basename "$scenario" .usdz)"
done
```

## Environmental Conditions

The pipeline supports the following environmental conditions:

| Condition | Description | Weather Effects |
|-----------|-------------|-----------------|
| `CLEAR_DAY` | Clear sunny day | Bright blue skies, excellent visibility |
| `OVERCAST` | Overcast cloudy sky | Gray clouds, diffused lighting |
| `HEAVY_RAIN` | Heavy rain with wet roads | Stormy atmosphere, wet reflective surfaces |
| `LIGHT_RAIN` | Light rain with mist | Misty atmosphere, slightly wet surfaces |
| `FOG` | Dense fog | Severely reduced visibility, atmospheric haze |
| `SUNSET` | Golden hour sunset | Warm orange lighting, long shadows |
| `SUNRISE` | Early morning sunrise | Soft golden light, fresh atmosphere |
| `NIGHT` | Nighttime scene | Streetlights, headlights, dark surroundings |
| `SNOW` | Snowy winter conditions | Snow-covered roads, cold atmosphere |
| `DESERT` | Dry desert road | Sandy environment, harsh sunlight |
| `URBAN_NIGHT` | Urban nighttime | Neon lights, city glow, bustling atmosphere |
| `STORM` | Stormy weather | Dark threatening clouds, dramatic lighting |

## Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--usdz-filename` | Path to USDZ scenario file | Required |
| `--host` | CARLA server IP address | `127.0.0.1` |
| `--port` | CARLA server port | `2000` |
| `--nurec-port` | NUREC service port | `46435` |
| `--output-dir` | Output directory for data | `cosmos_transfer_data` |
| `--sequence-length` | Length of video sequences | `30` |
| `--environmental-conditions` | List of conditions to process | `CLEAR_DAY HEAVY_RAIN SUNSET NIGHT` |
| `--enable-multi-sensor` | Enable multi-sensor capture | Flag |
| `--export-cosmos-data` | Export Cosmos Transfer data | Flag |

## Output Data Format

### Batch Structure

Each processed environmental condition creates a batch with:

- **RGB Frames**: Original camera images (PNG format)
- **Depth Maps**: Normalized depth information (PNG/NPY format)
- **Semantic Segmentation**: Pixel-wise semantic labels (PNG format)
- **Metadata**: JSON file with batch information
- **Prompt**: Text file with Cosmos Transfer prompt

### Metadata Format

```json
{
  "batch_id": "batch_0001_20250118_143022",
  "sequence_length": 30,
  "environmental_condition": "heavy rain with wet roads",
  "prompt": "Transform this driving scene to heavy rain with wet reflective roads...",
  "timestamp": "2025-01-18T14:30:22.123456",
  "frame_ids": [1001, 1002, 1003, ...],
  "timestamps": [1642518622.123, 1642518622.223, ...]
}
```

## Integration with Cosmos Transfer

### Data Preparation

The pipeline automatically prepares data in the format expected by Cosmos Transfer:

1. **RGB Video Sequence**: Baseline visual content
2. **Control Maps**: Depth and semantic segmentation for structure preservation
3. **Text Prompts**: Natural language descriptions of desired transformations

### Submission Workflow

1. **Collect Data**: Run the pipeline to generate batches
2. **Review Output**: Check generated data quality and completeness
3. **Submit to Cosmos Transfer**: Use the exported data as input to Cosmos Transfer
4. **Process Results**: Receive transformed video sequences from Cosmos Transfer

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure NUREC components are properly installed
2. **CARLA Connection**: Verify CARLA server is running and accessible
3. **Memory Issues**: Reduce sequence length or image resolution for large datasets
4. **Disk Space**: Ensure sufficient storage for output data

### Performance Optimization

- Use lower resolution for faster processing: modify sensor setup in `MultiSensorManager`
- Reduce sequence length for quicker iterations
- Process environmental conditions sequentially to manage memory usage

## API Reference

### Key Classes

- `MultiSensorManager`: Manages synchronized sensor data capture
- `CosmosTransferManager`: Handles data export and batch creation
- `EnvironmentalController`: Controls CARLA weather and lighting conditions
- `EnvironmentalCondition`: Enum of supported environmental conditions

### Data Structures

- `SensorData`: Container for synchronized sensor readings
- `CosmosTransferBatch`: Complete batch ready for Cosmos Transfer processing

## Contributing

To extend the pipeline:

1. Add new environmental conditions to `EnvironmentalCondition` enum
2. Implement corresponding weather presets in `EnvironmentalController`
3. Create custom prompt templates in `CosmosTransferManager`
4. Add new sensor types to `MultiSensorManager` if needed

## License

This project is licensed under the MIT License - see the CARLA project license for details.
