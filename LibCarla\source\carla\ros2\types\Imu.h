// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Imu.h
 * This header file contains the declaration of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifndef _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_IMU_H_
#define _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_IMU_H_

#include "Vector3.h"
#include "Quaternion.h"
#include "Header.h"

#include <fastrtps/utils/fixed_size_string.hpp>

#include <stdint.h>
#include <array>
#include <string>
#include <vector>
#include <map>
#include <bitset>

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#define eProsima_user_DllExport __declspec( dllexport )
#else
#define eProsima_user_DllExport
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define eProsima_user_DllExport
#endif  // _WIN32

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#if defined(Imu_SOURCE)
#define Imu_DllAPI __declspec( dllexport )
#else
#define Imu_DllAPI __declspec( dllimport )
#endif // Imu_SOURCE
#else
#define Imu_DllAPI
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define Imu_DllAPI
#endif // _WIN32

namespace eprosima {
namespace fastcdr {
class Cdr;
} // namespace fastcdr
} // namespace eprosima

namespace sensor_msgs {
    namespace msg {
        typedef std::array<double, 9> sensor_msgs__Imu__double_array_9;
        /*!
         * @brief This class represents the structure Imu defined by the user in the IDL file.
         * @ingroup IMU
         */
        class Imu
        {
        public:

            /*!
             * @brief Default constructor.
             */
            eProsima_user_DllExport Imu();

            /*!
             * @brief Default destructor.
             */
            eProsima_user_DllExport ~Imu();

            /*!
             * @brief Copy constructor.
             * @param x Reference to the object sensor_msgs::msg::Imu that will be copied.
             */
            eProsima_user_DllExport Imu(
                    const Imu& x);

            /*!
             * @brief Move constructor.
             * @param x Reference to the object sensor_msgs::msg::Imu that will be copied.
             */
            eProsima_user_DllExport Imu(
                    Imu&& x) noexcept;

            /*!
             * @brief Copy assignment.
             * @param x Reference to the object sensor_msgs::msg::Imu that will be copied.
             */
            eProsima_user_DllExport Imu& operator =(
                    const Imu& x);

            /*!
             * @brief Move assignment.
             * @param x Reference to the object sensor_msgs::msg::Imu that will be copied.
             */
            eProsima_user_DllExport Imu& operator =(
                    Imu&& x) noexcept;

            /*!
             * @brief Comparison operator.
             * @param x sensor_msgs::msg::Imu object to compare.
             */
            eProsima_user_DllExport bool operator ==(
                    const Imu& x) const;

            /*!
             * @brief Comparison operator.
             * @param x sensor_msgs::msg::Imu object to compare.
             */
            eProsima_user_DllExport bool operator !=(
                    const Imu& x) const;

            /*!
             * @brief This function copies the value in member header
             * @param _header New value to be copied in member header
             */
            eProsima_user_DllExport void header(
                    const std_msgs::msg::Header& _header);

            /*!
             * @brief This function moves the value in member header
             * @param _header New value to be moved in member header
             */
            eProsima_user_DllExport void header(
                    std_msgs::msg::Header&& _header);

            /*!
             * @brief This function returns a constant reference to member header
             * @return Constant reference to member header
             */
            eProsima_user_DllExport const std_msgs::msg::Header& header() const;

            /*!
             * @brief This function returns a reference to member header
             * @return Reference to member header
             */
            eProsima_user_DllExport std_msgs::msg::Header& header();
            /*!
             * @brief This function copies the value in member orientation
             * @param _orientation New value to be copied in member orientation
             */
            eProsima_user_DllExport void orientation(
                    const geometry_msgs::msg::Quaternion& _orientation);

            /*!
             * @brief This function moves the value in member orientation
             * @param _orientation New value to be moved in member orientation
             */
            eProsima_user_DllExport void orientation(
                    geometry_msgs::msg::Quaternion&& _orientation);

            /*!
             * @brief This function returns a constant reference to member orientation
             * @return Constant reference to member orientation
             */
            eProsima_user_DllExport const geometry_msgs::msg::Quaternion& orientation() const;

            /*!
             * @brief This function returns a reference to member orientation
             * @return Reference to member orientation
             */
            eProsima_user_DllExport geometry_msgs::msg::Quaternion& orientation();
            /*!
             * @brief This function copies the value in member orientation_covariance
             * @param _orientation_covariance New value to be copied in member orientation_covariance
             */
            eProsima_user_DllExport void orientation_covariance(
                    const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _orientation_covariance);

            /*!
             * @brief This function moves the value in member orientation_covariance
             * @param _orientation_covariance New value to be moved in member orientation_covariance
             */
            eProsima_user_DllExport void orientation_covariance(
                    sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _orientation_covariance);

            /*!
             * @brief This function returns a constant reference to member orientation_covariance
             * @return Constant reference to member orientation_covariance
             */
            eProsima_user_DllExport const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& orientation_covariance() const;

            /*!
             * @brief This function returns a reference to member orientation_covariance
             * @return Reference to member orientation_covariance
             */
            eProsima_user_DllExport sensor_msgs::msg::sensor_msgs__Imu__double_array_9& orientation_covariance();
            /*!
             * @brief This function copies the value in member angular_velocity
             * @param _angular_velocity New value to be copied in member angular_velocity
             */
            eProsima_user_DllExport void angular_velocity(
                    const geometry_msgs::msg::Vector3& _angular_velocity);

            /*!
             * @brief This function moves the value in member angular_velocity
             * @param _angular_velocity New value to be moved in member angular_velocity
             */
            eProsima_user_DllExport void angular_velocity(
                    geometry_msgs::msg::Vector3&& _angular_velocity);

            /*!
             * @brief This function returns a constant reference to member angular_velocity
             * @return Constant reference to member angular_velocity
             */
            eProsima_user_DllExport const geometry_msgs::msg::Vector3& angular_velocity() const;

            /*!
             * @brief This function returns a reference to member angular_velocity
             * @return Reference to member angular_velocity
             */
            eProsima_user_DllExport geometry_msgs::msg::Vector3& angular_velocity();
            /*!
             * @brief This function copies the value in member angular_velocity_covariance
             * @param _angular_velocity_covariance New value to be copied in member angular_velocity_covariance
             */
            eProsima_user_DllExport void angular_velocity_covariance(
                    const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _angular_velocity_covariance);

            /*!
             * @brief This function moves the value in member angular_velocity_covariance
             * @param _angular_velocity_covariance New value to be moved in member angular_velocity_covariance
             */
            eProsima_user_DllExport void angular_velocity_covariance(
                    sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _angular_velocity_covariance);

            /*!
             * @brief This function returns a constant reference to member angular_velocity_covariance
             * @return Constant reference to member angular_velocity_covariance
             */
            eProsima_user_DllExport const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& angular_velocity_covariance() const;

            /*!
             * @brief This function returns a reference to member angular_velocity_covariance
             * @return Reference to member angular_velocity_covariance
             */
            eProsima_user_DllExport sensor_msgs::msg::sensor_msgs__Imu__double_array_9& angular_velocity_covariance();
            /*!
             * @brief This function copies the value in member linear_acceleration
             * @param _linear_acceleration New value to be copied in member linear_acceleration
             */
            eProsima_user_DllExport void linear_acceleration(
                    const geometry_msgs::msg::Vector3& _linear_acceleration);

            /*!
             * @brief This function moves the value in member linear_acceleration
             * @param _linear_acceleration New value to be moved in member linear_acceleration
             */
            eProsima_user_DllExport void linear_acceleration(
                    geometry_msgs::msg::Vector3&& _linear_acceleration);

            /*!
             * @brief This function returns a constant reference to member linear_acceleration
             * @return Constant reference to member linear_acceleration
             */
            eProsima_user_DllExport const geometry_msgs::msg::Vector3& linear_acceleration() const;

            /*!
             * @brief This function returns a reference to member linear_acceleration
             * @return Reference to member linear_acceleration
             */
            eProsima_user_DllExport geometry_msgs::msg::Vector3& linear_acceleration();
            /*!
             * @brief This function copies the value in member linear_acceleration_covariance
             * @param _linear_acceleration_covariance New value to be copied in member linear_acceleration_covariance
             */
            eProsima_user_DllExport void linear_acceleration_covariance(
                    const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _linear_acceleration_covariance);

            /*!
             * @brief This function moves the value in member linear_acceleration_covariance
             * @param _linear_acceleration_covariance New value to be moved in member linear_acceleration_covariance
             */
            eProsima_user_DllExport void linear_acceleration_covariance(
                    sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _linear_acceleration_covariance);

            /*!
             * @brief This function returns a constant reference to member linear_acceleration_covariance
             * @return Constant reference to member linear_acceleration_covariance
             */
            eProsima_user_DllExport const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& linear_acceleration_covariance() const;

            /*!
             * @brief This function returns a reference to member linear_acceleration_covariance
             * @return Reference to member linear_acceleration_covariance
             */
            eProsima_user_DllExport sensor_msgs::msg::sensor_msgs__Imu__double_array_9& linear_acceleration_covariance();

            /*!
            * @brief This function returns the maximum serialized size of an object
            * depending on the buffer alignment.
            * @param current_alignment Buffer alignment.
            * @return Maximum serialized size.
            */
            eProsima_user_DllExport static size_t getMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function returns the serialized size of a data depending on the buffer alignment.
             * @param data Data which is calculated its serialized size.
             * @param current_alignment Buffer alignment.
             * @return Serialized size.
             */
            eProsima_user_DllExport static size_t getCdrSerializedSize(
                    const sensor_msgs::msg::Imu& data,
                    size_t current_alignment = 0);

            /*!
             * @brief This function serializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serialize(
                    eprosima::fastcdr::Cdr& cdr) const;

            /*!
             * @brief This function deserializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void deserialize(
                    eprosima::fastcdr::Cdr& cdr);

            /*!
             * @brief This function returns the maximum serialized size of the Key of an object
             * depending on the buffer alignment.
             * @param current_alignment Buffer alignment.
             * @return Maximum serialized size.
             */
            eProsima_user_DllExport static size_t getKeyMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function tells you if the Key has been defined for this type
             */
            eProsima_user_DllExport static bool isKeyDefined();

            /*!
             * @brief This function serializes the key members of an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serializeKey(
                    eprosima::fastcdr::Cdr& cdr) const;

        private:
            std_msgs::msg::Header m_header;
            geometry_msgs::msg::Quaternion m_orientation;
            sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_orientation_covariance;
            geometry_msgs::msg::Vector3 m_angular_velocity;
            sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_angular_velocity_covariance;
            geometry_msgs::msg::Vector3 m_linear_acceleration;
            sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_linear_acceleration_covariance;
        };
    } // namespace msg
} // namespace sensor_msgs

#endif // _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_IMU_H_
