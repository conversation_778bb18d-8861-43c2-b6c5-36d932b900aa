// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Clock.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "Clock.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define Time_max_cdr_typesize 0ULL;
#define rosgraph_msg_Clock_max_cdr_typesize 0ULL;
#define Time_max_key_cdr_typesize 0ULL;
#define rosgraph_msg_Clock_max_key_cdr_typesize 0ULL;

rosgraph::msg::Clock::Clock()
{
}

rosgraph::msg::Clock::~Clock()
{
}

rosgraph::msg::Clock::Clock(
        const rosgraph::msg::Clock& x)
{
    m_clock = x.m_clock;
}

rosgraph::msg::Clock::Clock(
        rosgraph::msg::Clock&& x) noexcept
{
    m_clock = x.m_clock;
}

rosgraph::msg::Clock& rosgraph::msg::Clock::operator =(
        const rosgraph::msg::Clock& x)
{
    m_clock = x.m_clock;
    return *this;
}

rosgraph::msg::Clock& rosgraph::msg::Clock::operator =(
        rosgraph::msg::Clock&& x) noexcept
{
    m_clock = x.m_clock;
    return *this;
}

bool rosgraph::msg::Clock::operator ==(
        const Clock& x) const
{
    return (m_clock == x.m_clock);
}

bool rosgraph::msg::Clock::operator !=(
        const Clock& x) const
{
    return !(*this == x);
}

size_t rosgraph::msg::Clock::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return rosgraph_msg_Clock_max_cdr_typesize;
}

size_t rosgraph::msg::Clock::getCdrSerializedSize(
        const rosgraph::msg::Clock& data,
        size_t current_alignment)
{
    (void)data;
    size_t initial_alignment = current_alignment;


    current_alignment += builtin_interfaces::msg::Time::getCdrSerializedSize(data.clock(), current_alignment);

    return current_alignment - initial_alignment;
}

void rosgraph::msg::Clock::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_clock;
}

void rosgraph::msg::Clock::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_clock;
}

/*!
 * @brief This function copies the value in member clock
 * @param _clock New value to be copied in member clock
 */
void rosgraph::msg::Clock::clock(
        const builtin_interfaces::msg::Time& _clock)
{
    m_clock = _clock;
}

/*!
 * @brief This function moves the value in member clock
 * @param _clock New value to be moved in member clock
 */
void rosgraph::msg::Clock::clock(
        builtin_interfaces::msg::Time&& _clock)
{
    m_clock = std::move(_clock);
}

/*!
 * @brief This function returns a constant reference to member clock
 * @return Constant reference to member clock
 */
const builtin_interfaces::msg::Time& rosgraph::msg::Clock::clock() const
{
    return m_clock;
}

/*!
 * @brief This function returns a reference to member clock
 * @return Reference to member clock
 */
builtin_interfaces::msg::Time& rosgraph::msg::Clock::clock()
{
    return m_clock;
}

size_t rosgraph::msg::Clock::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return rosgraph_msg_Clock_max_key_cdr_typesize;
}

bool rosgraph::msg::Clock::isKeyDefined()
{
    return false;
}

void rosgraph::msg::Clock::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
