// Copyright (c) 2025 Computer Vision Center (CVC) at the Universitat Autonoma de Barcelona (UAB).
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#pragma once

#include "carla/ros2/publishers/CarlaRGBCameraPublisher.h"

namespace carla {
namespace ros2 {

  using CarlaOpticalFlowCameraPublisher = CarlaRGBCameraPublisher;

}  // namespace ros2
}  // namespace carla
