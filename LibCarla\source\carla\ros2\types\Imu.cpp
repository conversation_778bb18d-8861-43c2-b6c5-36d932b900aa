// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Imu.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "Imu.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define geometry_msgs_msg_Vector3_max_cdr_typesize 24ULL;
#define std_msgs_msg_Time_max_cdr_typesize 8ULL;

#define geometry_msgs_msg_Quaternion_max_cdr_typesize 32ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define sensor_msgs_msg_Imu_max_cdr_typesize 568ULL;
#define geometry_msgs_msg_Vector3_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Time_max_key_cdr_typesize 0ULL;

#define geometry_msgs_msg_Quaternion_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;
#define sensor_msgs_msg_Imu_max_key_cdr_typesize 0ULL;

sensor_msgs::msg::Imu::Imu()
{
    // std_msgs::msg::Header m_header

    // geometry_msgs::msg::Quaternion m_orientation

    // sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_orientation_covariance
    memset(&m_orientation_covariance, 0, (9) * 8);
    // geometry_msgs::msg::Vector3 m_angular_velocity

    // sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_angular_velocity_covariance
    memset(&m_angular_velocity_covariance, 0, (9) * 8);
    // geometry_msgs::msg::Vector3 m_linear_acceleration

    // sensor_msgs::msg::sensor_msgs__Imu__double_array_9 m_linear_acceleration_covariance
    memset(&m_linear_acceleration_covariance, 0, (9) * 8);
}

sensor_msgs::msg::Imu::~Imu()
{
}

sensor_msgs::msg::Imu::Imu(
        const Imu& x)
{
    m_header = x.m_header;
    m_orientation = x.m_orientation;
    m_orientation_covariance = x.m_orientation_covariance;
    m_angular_velocity = x.m_angular_velocity;
    m_angular_velocity_covariance = x.m_angular_velocity_covariance;
    m_linear_acceleration = x.m_linear_acceleration;
    m_linear_acceleration_covariance = x.m_linear_acceleration_covariance;
}

sensor_msgs::msg::Imu::Imu(
        Imu&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_orientation = std::move(x.m_orientation);
    m_orientation_covariance = std::move(x.m_orientation_covariance);
    m_angular_velocity = std::move(x.m_angular_velocity);
    m_angular_velocity_covariance = std::move(x.m_angular_velocity_covariance);
    m_linear_acceleration = std::move(x.m_linear_acceleration);
    m_linear_acceleration_covariance = std::move(x.m_linear_acceleration_covariance);
}

sensor_msgs::msg::Imu& sensor_msgs::msg::Imu::operator =(
        const Imu& x)
{
    m_header = x.m_header;
    m_orientation = x.m_orientation;
    m_orientation_covariance = x.m_orientation_covariance;
    m_angular_velocity = x.m_angular_velocity;
    m_angular_velocity_covariance = x.m_angular_velocity_covariance;
    m_linear_acceleration = x.m_linear_acceleration;
    m_linear_acceleration_covariance = x.m_linear_acceleration_covariance;

    return *this;
}

sensor_msgs::msg::Imu& sensor_msgs::msg::Imu::operator =(
        Imu&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_orientation = std::move(x.m_orientation);
    m_orientation_covariance = std::move(x.m_orientation_covariance);
    m_angular_velocity = std::move(x.m_angular_velocity);
    m_angular_velocity_covariance = std::move(x.m_angular_velocity_covariance);
    m_linear_acceleration = std::move(x.m_linear_acceleration);
    m_linear_acceleration_covariance = std::move(x.m_linear_acceleration_covariance);

    return *this;
}

bool sensor_msgs::msg::Imu::operator ==(
        const Imu& x) const
{
    return (m_header == x.m_header && m_orientation == x.m_orientation && m_orientation_covariance == x.m_orientation_covariance && m_angular_velocity == x.m_angular_velocity && m_angular_velocity_covariance == x.m_angular_velocity_covariance && m_linear_acceleration == x.m_linear_acceleration && m_linear_acceleration_covariance == x.m_linear_acceleration_covariance);
}

bool sensor_msgs::msg::Imu::operator !=(
        const Imu& x) const
{
    return !(*this == x);
}

size_t sensor_msgs::msg::Imu::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_Imu_max_cdr_typesize;
}

size_t sensor_msgs::msg::Imu::getCdrSerializedSize(
        const sensor_msgs::msg::Imu& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += std_msgs::msg::Header::getCdrSerializedSize(data.header(), current_alignment);
    current_alignment += geometry_msgs::msg::Quaternion::getCdrSerializedSize(data.orientation(), current_alignment);
    current_alignment += ((9) * 8) + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += geometry_msgs::msg::Vector3::getCdrSerializedSize(data.angular_velocity(), current_alignment);
    current_alignment += ((9) * 8) + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += geometry_msgs::msg::Vector3::getCdrSerializedSize(data.linear_acceleration(), current_alignment);
    current_alignment += ((9) * 8) + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);

    return current_alignment - initial_alignment;
}

void sensor_msgs::msg::Imu::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_header;
    scdr << m_orientation;
    scdr << m_orientation_covariance;
    scdr << m_angular_velocity;
    scdr << m_angular_velocity_covariance;
    scdr << m_linear_acceleration;
    scdr << m_linear_acceleration_covariance;
}

void sensor_msgs::msg::Imu::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_header;
    dcdr >> m_orientation;
    dcdr >> m_orientation_covariance;
    dcdr >> m_angular_velocity;
    dcdr >> m_angular_velocity_covariance;
    dcdr >> m_linear_acceleration;
    dcdr >> m_linear_acceleration_covariance;
}

/*!
 * @brief This function copies the value in member header
 * @param _header New value to be copied in member header
 */
void sensor_msgs::msg::Imu::header(
        const std_msgs::msg::Header& _header)
{
    m_header = _header;
}

/*!
 * @brief This function moves the value in member header
 * @param _header New value to be moved in member header
 */
void sensor_msgs::msg::Imu::header(
        std_msgs::msg::Header&& _header)
{
    m_header = std::move(_header);
}

/*!
 * @brief This function returns a constant reference to member header
 * @return Constant reference to member header
 */
const std_msgs::msg::Header& sensor_msgs::msg::Imu::header() const
{
    return m_header;
}

/*!
 * @brief This function returns a reference to member header
 * @return Reference to member header
 */
std_msgs::msg::Header& sensor_msgs::msg::Imu::header()
{
    return m_header;
}

/*!
 * @brief This function copies the value in member orientation
 * @param _orientation New value to be copied in member orientation
 */
void sensor_msgs::msg::Imu::orientation(
        const geometry_msgs::msg::Quaternion& _orientation)
{
    m_orientation = _orientation;
}

/*!
 * @brief This function moves the value in member orientation
 * @param _orientation New value to be moved in member orientation
 */
void sensor_msgs::msg::Imu::orientation(
        geometry_msgs::msg::Quaternion&& _orientation)
{
    m_orientation = std::move(_orientation);
}

/*!
 * @brief This function returns a constant reference to member orientation
 * @return Constant reference to member orientation
 */
const geometry_msgs::msg::Quaternion& sensor_msgs::msg::Imu::orientation() const
{
    return m_orientation;
}

/*!
 * @brief This function returns a reference to member orientation
 * @return Reference to member orientation
 */
geometry_msgs::msg::Quaternion& sensor_msgs::msg::Imu::orientation()
{
    return m_orientation;
}

/*!
 * @brief This function copies the value in member orientation_covariance
 * @param _orientation_covariance New value to be copied in member orientation_covariance
 */
void sensor_msgs::msg::Imu::orientation_covariance(
        const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _orientation_covariance)
{
    m_orientation_covariance = _orientation_covariance;
}

/*!
 * @brief This function moves the value in member orientation_covariance
 * @param _orientation_covariance New value to be moved in member orientation_covariance
 */
void sensor_msgs::msg::Imu::orientation_covariance(
        sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _orientation_covariance)
{
    m_orientation_covariance = std::move(_orientation_covariance);
}

/*!
 * @brief This function returns a constant reference to member orientation_covariance
 * @return Constant reference to member orientation_covariance
 */
const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::orientation_covariance() const
{
    return m_orientation_covariance;
}

/*!
 * @brief This function returns a reference to member orientation_covariance
 * @return Reference to member orientation_covariance
 */
sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::orientation_covariance()
{
    return m_orientation_covariance;
}

/*!
 * @brief This function copies the value in member angular_velocity
 * @param _angular_velocity New value to be copied in member angular_velocity
 */
void sensor_msgs::msg::Imu::angular_velocity(
        const geometry_msgs::msg::Vector3& _angular_velocity)
{
    m_angular_velocity = _angular_velocity;
}

/*!
 * @brief This function moves the value in member angular_velocity
 * @param _angular_velocity New value to be moved in member angular_velocity
 */
void sensor_msgs::msg::Imu::angular_velocity(
        geometry_msgs::msg::Vector3&& _angular_velocity)
{
    m_angular_velocity = std::move(_angular_velocity);
}

/*!
 * @brief This function returns a constant reference to member angular_velocity
 * @return Constant reference to member angular_velocity
 */
const geometry_msgs::msg::Vector3& sensor_msgs::msg::Imu::angular_velocity() const
{
    return m_angular_velocity;
}

/*!
 * @brief This function returns a reference to member angular_velocity
 * @return Reference to member angular_velocity
 */
geometry_msgs::msg::Vector3& sensor_msgs::msg::Imu::angular_velocity()
{
    return m_angular_velocity;
}

/*!
 * @brief This function copies the value in member angular_velocity_covariance
 * @param _angular_velocity_covariance New value to be copied in member angular_velocity_covariance
 */
void sensor_msgs::msg::Imu::angular_velocity_covariance(
        const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _angular_velocity_covariance)
{
    m_angular_velocity_covariance = _angular_velocity_covariance;
}

/*!
 * @brief This function moves the value in member angular_velocity_covariance
 * @param _angular_velocity_covariance New value to be moved in member angular_velocity_covariance
 */
void sensor_msgs::msg::Imu::angular_velocity_covariance(
        sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _angular_velocity_covariance)
{
    m_angular_velocity_covariance = std::move(_angular_velocity_covariance);
}

/*!
 * @brief This function returns a constant reference to member angular_velocity_covariance
 * @return Constant reference to member angular_velocity_covariance
 */
const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::angular_velocity_covariance() const
{
    return m_angular_velocity_covariance;
}

/*!
 * @brief This function returns a reference to member angular_velocity_covariance
 * @return Reference to member angular_velocity_covariance
 */
sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::angular_velocity_covariance()
{
    return m_angular_velocity_covariance;
}

/*!
 * @brief This function copies the value in member linear_acceleration
 * @param _linear_acceleration New value to be copied in member linear_acceleration
 */
void sensor_msgs::msg::Imu::linear_acceleration(
        const geometry_msgs::msg::Vector3& _linear_acceleration)
{
    m_linear_acceleration = _linear_acceleration;
}

/*!
 * @brief This function moves the value in member linear_acceleration
 * @param _linear_acceleration New value to be moved in member linear_acceleration
 */
void sensor_msgs::msg::Imu::linear_acceleration(
        geometry_msgs::msg::Vector3&& _linear_acceleration)
{
    m_linear_acceleration = std::move(_linear_acceleration);
}

/*!
 * @brief This function returns a constant reference to member linear_acceleration
 * @return Constant reference to member linear_acceleration
 */
const geometry_msgs::msg::Vector3& sensor_msgs::msg::Imu::linear_acceleration() const
{
    return m_linear_acceleration;
}

/*!
 * @brief This function returns a reference to member linear_acceleration
 * @return Reference to member linear_acceleration
 */
geometry_msgs::msg::Vector3& sensor_msgs::msg::Imu::linear_acceleration()
{
    return m_linear_acceleration;
}

/*!
 * @brief This function copies the value in member linear_acceleration_covariance
 * @param _linear_acceleration_covariance New value to be copied in member linear_acceleration_covariance
 */
void sensor_msgs::msg::Imu::linear_acceleration_covariance(
        const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& _linear_acceleration_covariance)
{
    m_linear_acceleration_covariance = _linear_acceleration_covariance;
}

/*!
 * @brief This function moves the value in member linear_acceleration_covariance
 * @param _linear_acceleration_covariance New value to be moved in member linear_acceleration_covariance
 */
void sensor_msgs::msg::Imu::linear_acceleration_covariance(
        sensor_msgs::msg::sensor_msgs__Imu__double_array_9&& _linear_acceleration_covariance)
{
    m_linear_acceleration_covariance = std::move(_linear_acceleration_covariance);
}

/*!
 * @brief This function returns a constant reference to member linear_acceleration_covariance
 * @return Constant reference to member linear_acceleration_covariance
 */
const sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::linear_acceleration_covariance() const
{
    return m_linear_acceleration_covariance;
}

/*!
 * @brief This function returns a reference to member linear_acceleration_covariance
 * @return Reference to member linear_acceleration_covariance
 */
sensor_msgs::msg::sensor_msgs__Imu__double_array_9& sensor_msgs::msg::Imu::linear_acceleration_covariance()
{
    return m_linear_acceleration_covariance;
}

size_t sensor_msgs::msg::Imu::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_Imu_max_key_cdr_typesize;
}

bool sensor_msgs::msg::Imu::isKeyDefined()
{
    return false;
}

void sensor_msgs::msg::Imu::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
