#!/usr/bin/env python3

"""
Terminal-Based Manual Control in NUREC Reconstructed Environment

This version completely avoids OpenGL conflicts by using terminal-based input
and only the NUREC display window for visualization.

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    X           : Quit

Usage:
    python3 manual_control_nurec_terminal.py --usdz-filename /path/to/scenario.usdz
"""

import carla
import argparse
import logging
import sys
import threading
import time
import select
import termios
import tty
from typing import Optional

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("ManualControlNurecTerminal")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class TerminalInput:
    """Handle terminal input without blocking"""
    
    def __init__(self):
        self.old_settings = None
        self.running = True
        self.keys_pressed = set()
        
    def __enter__(self):
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        return self
        
    def __exit__(self, type, value, traceback):
        if self.old_settings:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
    
    def get_key(self):
        """Get a single key press without blocking"""
        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            return sys.stdin.read(1)
        return None

class TerminalManualControlNurec:
    """
    Terminal-based manual control that avoids all OpenGL conflicts
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.scenario = None
        self.display = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Input handling
        self.terminal_input = None
        self.keys_pressed = set()
        
        # Control parameters
        self.throttle_increment = 0.02
        self.brake_increment = 0.3
        self.steer_increment = 0.02
        self.steer_decay = 0.85
        
        logger.info("Terminal-based manual control initialized")
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            self.world = self.client.get_world()
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def load_nurec_scenario(self):
        """Load NUREC scenario"""
        try:
            logger.info(f"Loading NUREC scenario: {self.args.usdz_filename}")
            self.scenario = NurecScenario(
                self.client, 
                self.args.usdz_filename, 
                port=self.args.nurec_port
            )
            self.scenario.__enter__()
            logger.info("NUREC scenario loaded successfully")
            
            # Setup NUREC display - this will be the only display window
            self.display = PygameDisplay()
            self.scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: self.display.setImage(image, (1, 1), (0, 0)),
                framerate=30,
                resolution_ratio=0.75
            )
            
            logger.info("NUREC camera setup complete")
            
            return True
        except Exception as e:
            logger.error(f"Failed to load NUREC scenario: {e}")
            return False
    
    def setup_vehicle(self):
        """Setup the ego vehicle for manual control"""
        try:
            # Get the ego vehicle from NUREC scenario
            if EGO_TRACK_ID in self.scenario.actor_mapping:
                self.vehicle = self.scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {self.vehicle.id}")
                
                # Enable physics for manual control
                self.scenario.actor_mapping[EGO_TRACK_ID].set_physics(True, self.scenario.get_sim_time())
                logger.info("Vehicle physics enabled for manual control")
                
                # Set initial control
                self.vehicle.apply_control(self.control)
                
                # Start the scenario to begin rendering
                self.scenario.start_replay()
                logger.info("Scenario replay started - NUREC window should show the environment")
                
                return True
            else:
                logger.error("Ego vehicle not found in scenario")
                return False
        except Exception as e:
            logger.error(f"Failed to setup vehicle: {e}")
            return False
    
    def handle_input(self):
        """Handle terminal input for vehicle control"""
        if not self.terminal_input:
            return True
            
        # Get key press
        key = self.terminal_input.get_key()
        
        if key:
            # Handle single key presses
            if key.lower() == 'x':
                return False
            elif key.lower() == 'p':
                self.toggle_autopilot()
            elif key.lower() == 'r':
                self.restart_vehicle()
            elif key.lower() == 'q':
                # Toggle reverse
                self.control.gear = 1 if self.control.reverse else -1
                print(f"\rGear: {'Reverse' if self.control.gear < 0 else 'Forward'}")
            elif key.lower() in 'wasd':
                # Add to pressed keys
                self.keys_pressed.add(key.lower())
            elif key == ' ':
                self.keys_pressed.add('space')
        
        # Handle continuous key presses
        if not self.autopilot_enabled:
            # Throttle
            if 'w' in self.keys_pressed:
                self.control.throttle = min(self.control.throttle + self.throttle_increment, 1.0)
            else:
                self.control.throttle = max(self.control.throttle - 0.05, 0.0)
            
            # Brake
            if 's' in self.keys_pressed:
                self.control.brake = min(self.control.brake + self.brake_increment, 1.0)
            else:
                self.control.brake = max(self.control.brake - 0.1, 0.0)
            
            # Steering
            if 'a' in self.keys_pressed:
                self.control.steer = max(self.control.steer - self.steer_increment, -1.0)
            elif 'd' in self.keys_pressed:
                self.control.steer = min(self.control.steer + self.steer_increment, 1.0)
            else:
                # Gradual return to center
                self.control.steer *= self.steer_decay
                if abs(self.control.steer) < 0.01:
                    self.control.steer = 0.0
            
            # Hand brake
            self.control.hand_brake = 'space' in self.keys_pressed
            
            # Set reverse flag
            self.control.reverse = self.control.gear < 0
            
            # Apply control to vehicle
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        # Clear keys (they need to be pressed continuously)
        self.keys_pressed.clear()
        
        return True
    
    def toggle_autopilot(self):
        """Toggle autopilot mode"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        print(f"\rAutopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def restart_vehicle(self):
        """Restart vehicle at a spawn point"""
        if self.vehicle and self.world:
            try:
                spawn_points = self.world.get_map().get_spawn_points()
                if spawn_points:
                    import random
                    spawn_point = random.choice(spawn_points)
                    self.vehicle.set_transform(spawn_point)
                    # Reset control
                    self.control = carla.VehicleControl()
                    self.vehicle.apply_control(self.control)
                    print("\rVehicle restarted at new location")
            except Exception as e:
                print(f"\rFailed to restart vehicle: {e}")
    
    def print_status(self):
        """Print current status to console"""
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * (velocity.x**2 + velocity.y**2 + velocity.z**2)**0.5
            
            status = (
                f"Speed: {speed_kmh:6.1f} km/h | "
                f"Throttle: {self.control.throttle:4.2f} | "
                f"Brake: {self.control.brake:4.2f} | "
                f"Steer: {self.control.steer:5.2f} | "
                f"Autopilot: {'ON ' if self.autopilot_enabled else 'OFF'}"
            )
            print(f"\r{status}", end="", flush=True)
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False
        
        if not self.load_nurec_scenario():
            return False
        
        if not self.setup_vehicle():
            return False
        
        print("\n" + "="*80)
        print("MANUAL CONTROL IN NUREC ENVIRONMENT")
        print("="*80)
        print("Controls:")
        print("  W/S     - Throttle/Brake")
        print("  A/D     - Steer left/right")
        print("  Q       - Toggle reverse")
        print("  Space   - Hand brake")
        print("  P       - Toggle autopilot")
        print("  R       - Restart vehicle")
        print("  X       - Quit")
        print("="*80)
        print("Watch the NUREC window for the reconstructed environment")
        print("Vehicle status will be shown below:")
        print()
        
        running = True
        status_counter = 0
        
        try:
            with TerminalInput() as terminal_input:
                self.terminal_input = terminal_input
                
                while running:
                    # Handle input
                    running = self.handle_input()
                    
                    # Tick the scenario and world
                    if self.scenario:
                        self.scenario.tick()
                    if self.world:
                        self.world.tick()
                    
                    # Print status every 30 frames
                    status_counter += 1
                    if status_counter >= 30:
                        self.print_status()
                        status_counter = 0
                    
                    # Small delay to prevent excessive CPU usage
                    time.sleep(0.016)  # ~60 FPS
                    
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        except Exception as e:
            print(f"\nError in main loop: {e}")
            logger.error(f"Error in main loop: {e}")
            handle_exception(e)
        finally:
            print("\nCleaning up...")
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        if self.display:
            self.display.destroy()
        
        if self.scenario:
            try:
                self.scenario.__exit__(None, None, None)
            except:
                pass
        
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Terminal-Based Manual Control in NUREC Environment")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    # Create and run the manual control
    manual_control = TerminalManualControlNurec(args)
    success = manual_control.run()
    
    if success:
        logger.info("Manual control session completed successfully")
    else:
        logger.error("Manual control session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
