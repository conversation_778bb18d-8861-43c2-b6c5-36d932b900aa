#!/usr/bin/env python3

"""
Enhanced Manual Control in NUREC Reconstructed Environment

This script provides advanced manual control features in NUREC environments:
- Manual driving through reconstructed environments
- Multiple camera views (NUREC + CARLA sensors)
- Environmental condition controls
- Data recording capabilities
- Real-time vehicle telemetry

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    TAB         : Cycle camera views
    1-5         : Weather presets
    F           : Toggle free camera
    C           : Toggle data recording
    H           : Show/hide help
    Esc         : Quit

Usage:
    python3 manual_control_nurec_enhanced.py --usdz-filename /path/to/scenario.usdz
    python3 manual_control_nurec_enhanced.py --usdz-filename /path/to/scenario.usdz --record-data
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("ManualControlNurecEnhanced")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class EnhancedManualControlNurec:
    """
    Enhanced manual control with multiple features
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.scenario = None
        self.display = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Camera and sensors
        self.cameras = {}
        self.current_camera = "nurec"
        self.camera_list = ["nurec", "rgb", "depth", "semantic"]
        
        # Recording
        self.recording_enabled = args.record_data
        self.recording_data = []
        self.recording_start_time = None
        
        # Weather presets
        self.weather_presets = [
            carla.WeatherParameters.ClearNoon,
            carla.WeatherParameters.CloudyNoon,
            carla.WeatherParameters.WetNoon,
            carla.WeatherParameters.WetCloudyNoon,
            carla.WeatherParameters.SoftRainNoon,
        ]
        self.current_weather = 0
        
        # UI state
        self.show_help = False
        self.show_telemetry = True
        
        # Pygame setup
        pygame.init()
        self.clock = pygame.time.Clock()
        
        # Main display
        self.display_size = (1200, 800)
        self.main_display = pygame.display.set_mode(self.display_size)
        pygame.display.set_caption("Enhanced Manual Control - NUREC Environment")
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            self.world = self.client.get_world()
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def load_nurec_scenario(self):
        """Load NUREC scenario"""
        try:
            logger.info(f"Loading NUREC scenario: {self.args.usdz_filename}")
            self.scenario = NurecScenario(
                self.client, 
                self.args.usdz_filename, 
                port=self.args.nurec_port
            )
            self.scenario.__enter__()
            logger.info("NUREC scenario loaded successfully")
            
            # Setup NUREC display
            self.display = PygameDisplay()
            self.scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: self.display.setImage(image, (1, 1), (0, 0)),
                framerate=30,
                resolution_ratio=0.75
            )
            
            return True
        except Exception as e:
            logger.error(f"Failed to load NUREC scenario: {e}")
            return False
    
    def setup_vehicle_and_sensors(self):
        """Setup vehicle and additional sensors"""
        try:
            # Get ego vehicle
            if EGO_TRACK_ID in self.scenario.actor_mapping:
                self.vehicle = self.scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {self.vehicle.id}")
                
                # Enable physics
                self.scenario.actor_mapping[EGO_TRACK_ID].set_physics(True, self.scenario.get_sim_time())
                
                # Setup additional CARLA sensors
                self.setup_carla_sensors()
                
                return True
            else:
                logger.error("Ego vehicle not found in scenario")
                return False
        except Exception as e:
            logger.error(f"Failed to setup vehicle: {e}")
            return False
    
    def setup_carla_sensors(self):
        """Setup additional CARLA sensors"""
        if not self.vehicle:
            return
        
        blueprint_library = self.world.get_blueprint_library()
        camera_transform = carla.Transform(carla.Location(x=1.5, z=2.4))
        
        # RGB Camera
        rgb_bp = blueprint_library.find('sensor.camera.rgb')
        rgb_bp.set_attribute('image_size_x', '800')
        rgb_bp.set_attribute('image_size_y', '600')
        rgb_bp.set_attribute('fov', '90')
        
        rgb_sensor = self.world.spawn_actor(rgb_bp, camera_transform, attach_to=self.vehicle)
        rgb_sensor.listen(lambda image: self.process_sensor_data('rgb', image))
        self.cameras['rgb'] = {'sensor': rgb_sensor, 'data': None}
        
        # Depth Camera
        depth_bp = blueprint_library.find('sensor.camera.depth')
        depth_bp.set_attribute('image_size_x', '800')
        depth_bp.set_attribute('image_size_y', '600')
        depth_bp.set_attribute('fov', '90')
        
        depth_sensor = self.world.spawn_actor(depth_bp, camera_transform, attach_to=self.vehicle)
        depth_sensor.listen(lambda image: self.process_sensor_data('depth', image))
        self.cameras['depth'] = {'sensor': depth_sensor, 'data': None}
        
        # Semantic Segmentation Camera
        semantic_bp = blueprint_library.find('sensor.camera.semantic_segmentation')
        semantic_bp.set_attribute('image_size_x', '800')
        semantic_bp.set_attribute('image_size_y', '600')
        semantic_bp.set_attribute('fov', '90')
        
        semantic_sensor = self.world.spawn_actor(semantic_bp, camera_transform, attach_to=self.vehicle)
        semantic_sensor.listen(lambda image: self.process_sensor_data('semantic', image))
        self.cameras['semantic'] = {'sensor': semantic_sensor, 'data': None}
        
        logger.info("CARLA sensors setup complete")
    
    def process_sensor_data(self, sensor_type: str, image):
        """Process sensor data for display and recording"""
        # Convert image to numpy array
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        
        if sensor_type == 'rgb':
            array = array[:, :, :3]  # Remove alpha
            array = array[:, :, ::-1]  # BGR to RGB
        elif sensor_type == 'depth':
            # Convert depth to grayscale
            depth = array[:, :, 0] + array[:, :, 1] * 256 + array[:, :, 2] * 256 * 256
            depth = depth.astype(np.float32) / (256 * 256 * 256 - 1)
            array = (depth * 255).astype(np.uint8)
            array = np.stack([array, array, array], axis=2)
        elif sensor_type == 'semantic':
            # Use red channel for semantic labels
            semantic = array[:, :, 2]
            array = np.stack([semantic, semantic, semantic], axis=2)
        
        # Store processed data
        self.cameras[sensor_type]['data'] = array
        
        # Record data if enabled
        if self.recording_enabled and self.vehicle:
            self.record_frame_data(sensor_type, array, image.timestamp)
    
    def record_frame_data(self, sensor_type: str, image_data: np.ndarray, timestamp: float):
        """Record frame data for later analysis"""
        if self.recording_start_time is None:
            self.recording_start_time = timestamp
        
        # Get vehicle state
        transform = self.vehicle.get_transform()
        velocity = self.vehicle.get_velocity()
        
        frame_data = {
            'timestamp': timestamp,
            'relative_time': timestamp - self.recording_start_time,
            'sensor_type': sensor_type,
            'vehicle_location': [transform.location.x, transform.location.y, transform.location.z],
            'vehicle_rotation': [transform.rotation.pitch, transform.rotation.yaw, transform.rotation.roll],
            'vehicle_velocity': [velocity.x, velocity.y, velocity.z],
            'control': {
                'throttle': self.control.throttle,
                'steer': self.control.steer,
                'brake': self.control.brake,
                'reverse': self.control.reverse
            }
        }
        
        self.recording_data.append(frame_data)
    
    def handle_input(self):
        """Handle keyboard input"""
        keys = pygame.key.get_pressed()
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_h:
                    self.show_help = not self.show_help
                elif event.key == pygame.K_TAB:
                    self.cycle_camera()
                elif event.key == pygame.K_r:
                    self.restart_vehicle()
                elif event.key == pygame.K_p:
                    self.toggle_autopilot()
                elif event.key == pygame.K_q:
                    self.control.gear = 1 if self.control.reverse else -1
                elif event.key == pygame.K_c:
                    self.toggle_recording()
                elif event.key == pygame.K_f:
                    self.show_telemetry = not self.show_telemetry
                elif pygame.K_1 <= event.key <= pygame.K_5:
                    self.change_weather(event.key - pygame.K_1)
        
        # Vehicle control
        if not self.autopilot_enabled:
            # Throttle/Brake
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                self.control.throttle = min(self.control.throttle + 0.01, 1.0)
            else:
                self.control.throttle = 0.0
            
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                self.control.brake = min(self.control.brake + 0.2, 1.0)
            else:
                self.control.brake = 0.0
            
            # Steering
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                self.control.steer = max(self.control.steer - 0.01, -1.0)
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                self.control.steer = min(self.control.steer + 0.01, 1.0)
            else:
                self.control.steer *= 0.9
            
            self.control.hand_brake = keys[pygame.K_SPACE]
            self.control.reverse = self.control.gear < 0
            
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        return True
    
    def cycle_camera(self):
        """Cycle through available cameras"""
        current_index = self.camera_list.index(self.current_camera)
        self.current_camera = self.camera_list[(current_index + 1) % len(self.camera_list)]
        logger.info(f"Switched to {self.current_camera} camera")
    
    def change_weather(self, preset_index: int):
        """Change weather preset"""
        if 0 <= preset_index < len(self.weather_presets):
            self.current_weather = preset_index
            self.world.set_weather(self.weather_presets[preset_index])
            logger.info(f"Changed to weather preset {preset_index + 1}")
    
    def toggle_autopilot(self):
        """Toggle autopilot"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        logger.info(f"Autopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def toggle_recording(self):
        """Toggle data recording"""
        self.recording_enabled = not self.recording_enabled
        if self.recording_enabled:
            self.recording_data.clear()
            self.recording_start_time = None
            logger.info("Recording started")
        else:
            self.save_recording()
            logger.info("Recording stopped and saved")
    
    def save_recording(self):
        """Save recorded data to file"""
        if not self.recording_data:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"manual_control_recording_{timestamp}.json"
        
        recording_info = {
            'scenario_file': self.args.usdz_filename,
            'recording_duration': len(self.recording_data),
            'frames': self.recording_data
        }
        
        with open(filename, 'w') as f:
            json.dump(recording_info, f, indent=2)
        
        logger.info(f"Recording saved to {filename}")
    
    def restart_vehicle(self):
        """Restart vehicle"""
        if self.vehicle and self.world:
            try:
                spawn_points = self.world.get_map().get_spawn_points()
                if spawn_points:
                    import random
                    spawn_point = random.choice(spawn_points)
                    self.vehicle.set_transform(spawn_point)
                    logger.info("Vehicle restarted")
            except Exception as e:
                logger.warning(f"Failed to restart vehicle: {e}")
    
    def render_display(self):
        """Render the main display"""
        self.main_display.fill((0, 0, 0))
        
        # Render current camera view
        if self.current_camera == "nurec" and self.display:
            # NUREC camera is handled by PygameDisplay
            pass
        elif self.current_camera in self.cameras and self.cameras[self.current_camera]['data'] is not None:
            # Render CARLA sensor data
            image_data = self.cameras[self.current_camera]['data']
            image_surface = pygame.surfarray.make_surface(image_data.swapaxes(0, 1))
            
            # Scale to fit display
            scaled_surface = pygame.transform.scale(image_surface, (800, 600))
            self.main_display.blit(scaled_surface, (200, 100))
        
        # Render UI elements
        self.render_ui()
        
        pygame.display.flip()
    
    def render_ui(self):
        """Render UI elements"""
        # Camera info
        camera_text = f"Camera: {self.current_camera.upper()}"
        camera_surface = self.font.render(camera_text, True, (255, 255, 255))
        self.main_display.blit(camera_surface, (10, 10))
        
        # Vehicle telemetry
        if self.show_telemetry and self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * np.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
            
            telemetry_lines = [
                f"Speed: {speed_kmh:.1f} km/h",
                f"Throttle: {self.control.throttle:.2f}",
                f"Brake: {self.control.brake:.2f}",
                f"Steer: {self.control.steer:.2f}",
                f"Autopilot: {'ON' if self.autopilot_enabled else 'OFF'}",
                f"Recording: {'ON' if self.recording_enabled else 'OFF'}",
            ]
            
            y_offset = 50
            for line in telemetry_lines:
                text_surface = self.small_font.render(line, True, (255, 255, 255))
                self.main_display.blit(text_surface, (10, y_offset))
                y_offset += 20
        
        # Help text
        if self.show_help:
            help_lines = [
                "Controls:",
                "W/S - Throttle/Brake",
                "A/D - Steer",
                "Space - Hand brake",
                "P - Autopilot",
                "R - Restart",
                "TAB - Camera",
                "1-5 - Weather",
                "C - Recording",
                "H - Help",
                "ESC - Quit"
            ]
            
            y_offset = 200
            for line in help_lines:
                text_surface = self.small_font.render(line, True, (255, 255, 0))
                self.main_display.blit(text_surface, (10, y_offset))
                y_offset += 18
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False
        
        if not self.load_nurec_scenario():
            return False
        
        if not self.setup_vehicle_and_sensors():
            return False
        
        logger.info("Starting enhanced manual control...")
        logger.info("Press H for help, TAB to cycle cameras")
        
        running = True
        try:
            while running:
                running = self.handle_input()
                
                # Tick simulation
                if self.scenario:
                    self.scenario.tick()
                if self.world:
                    self.world.tick()
                
                # Render display
                self.render_display()
                
                self.clock.tick(60)
                
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            handle_exception(e)
        finally:
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        # Save recording if active
        if self.recording_enabled:
            self.save_recording()
        
        # Destroy sensors
        for camera_info in self.cameras.values():
            if 'sensor' in camera_info and camera_info['sensor']:
                camera_info['sensor'].destroy()
        
        if self.display:
            self.display.destroy()
        
        if self.scenario:
            try:
                self.scenario.__exit__(None, None, None)
            except:
                pass
        
        pygame.quit()
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Enhanced Manual Control in NUREC Environment")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    parser.add_argument('--record-data', action='store_true', help='Enable data recording')
    
    args = parser.parse_args()
    
    manual_control = EnhancedManualControlNurec(args)
    success = manual_control.run()
    
    if success:
        logger.info("Enhanced manual control session completed")
    else:
        logger.error("Enhanced manual control session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
