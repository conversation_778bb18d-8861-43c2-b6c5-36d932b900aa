# Enhanced CARLA-NUREC-Cosmos Transfer Integration Pipeline
# SPDX-FileCopyrightText: © 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# SPDX-License-Identifier: MIT

"""
CARLA-NUREC-Cosmos Transfer Integration Pipeline

This script provides a comprehensive pipeline for integrating CARLA with Cosmos Transfer
to generate diverse training data from NUREC scenarios. The pipeline:

1. Loads NUREC scenarios from USDZ files
2. Captures synchronized multi-sensor data (RGB, depth, semantic segmentation)
3. Records ground-truth control maps for Cosmos Transfer
4. Provides interface for environmental condition changes
5. Exports data in Cosmos Transfer compatible formats
6. Supports prompt-driven semantic control for data generation

Key Features:
- Multi-sensor synchronization and data capture
- Environmental condition controls (weather, lighting, time of day)
- Cosmos Transfer data preparation and export
- Real-time visualization with Pygame
- Comprehensive logging and error handling
"""

import carla
import argparse
import logging
import sys
import os
import json
import numpy as np
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum

# --- Step 1: Configure Detailed Logging ---
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("Nurec_Verbose_Loader")
logger.info("--- Starting NUREC Scenario Loader Script ---")


# --- Step 2: Import NUREC components ---
try:
    logger.info("Attempting to import NurecScenario and handle_exception...")
    from nurec_integration import NurecScenario
    from utils import handle_exception
    # Import PygameDisplay for visualization
    from pygame_display import PygameDisplay
    # Import constants for ego vehicle access
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    logger.error("Please ensure you have run 'pip install -r requirements.txt' and compiled gRPC protos.")
    sys.exit(1)

# --- Step 3: Define Data Structures and Enums ---

class EnvironmentalCondition(Enum):
    """Environmental conditions for Cosmos Transfer prompts"""
    CLEAR_DAY = "clear sunny day"
    OVERCAST = "overcast cloudy sky"
    HEAVY_RAIN = "heavy rain with wet roads"
    LIGHT_RAIN = "light rain with misty atmosphere"
    FOG = "dense fog with low visibility"
    SUNSET = "golden hour sunset lighting"
    SUNRISE = "early morning sunrise"
    NIGHT = "nighttime scene with streetlights"
    SNOW = "snowy winter conditions"
    DESERT = "dry desert road environment"
    URBAN_NIGHT = "urban nighttime with neon lights"
    STORM = "stormy weather with dark clouds"

@dataclass
class SensorData:
    """Container for synchronized sensor data"""
    timestamp: float
    frame_id: int
    rgb_image: Optional[np.ndarray] = None
    depth_image: Optional[np.ndarray] = None
    semantic_image: Optional[np.ndarray] = None
    camera_transform: Optional[carla.Transform] = None
    
@dataclass
class CosmosTransferBatch:
    """Container for Cosmos Transfer input data"""
    rgb_sequence: List[np.ndarray]
    depth_sequence: List[np.ndarray]
    semantic_sequence: List[np.ndarray]
    prompt: str
    metadata: Dict[str, Any]
    output_path: str

# --- Step 4: Multi-Sensor Data Capture System ---

class MultiSensorManager:
    """
    Manages multiple synchronized sensors for Cosmos Transfer data generation.
    Captures RGB, depth, and semantic segmentation data simultaneously.
    """
    
    def __init__(self, world: carla.World, output_dir: str = "cosmos_data"):
        self.world = world
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Sensor storage
        self.rgb_sensor: Optional[carla.Actor] = None
        self.depth_sensor: Optional[carla.Actor] = None
        self.semantic_sensor: Optional[carla.Actor] = None
        
        # Data storage
        self.sensor_data: List[SensorData] = []
        self.current_frame_data: Dict[str, Any] = {}
        self.frame_counter = 0
        
        # Synchronization
        self.expected_sensors = 3  # RGB, depth, semantic
        self.received_count = 0
        
        logger.info(f"MultiSensorManager initialized with output directory: {self.output_dir}")
    
    def setup_sensors(self, vehicle: carla.Actor, 
                     camera_transform: carla.Transform = None,
                     image_size_x: int = 1920,
                     image_size_y: int = 1080,
                     fov: float = 90.0) -> None:
        """Setup RGB, depth, and semantic segmentation sensors"""
        
        if camera_transform is None:
            camera_transform = carla.Transform(
                carla.Location(x=1.5, z=2.4),
                carla.Rotation(pitch=0, yaw=0, roll=0)
            )
        
        blueprint_library = self.world.get_blueprint_library()
        
        # RGB Camera
        rgb_bp = blueprint_library.find('sensor.camera.rgb')
        rgb_bp.set_attribute('image_size_x', str(image_size_x))
        rgb_bp.set_attribute('image_size_y', str(image_size_y))
        rgb_bp.set_attribute('fov', str(fov))
        rgb_bp.set_attribute('sensor_tick', '0.1')  # 10 FPS
        
        self.rgb_sensor = self.world.spawn_actor(rgb_bp, camera_transform, attach_to=vehicle)
        self.rgb_sensor.listen(lambda image: self._on_rgb_received(image))
        
        # Depth Camera
        depth_bp = blueprint_library.find('sensor.camera.depth')
        depth_bp.set_attribute('image_size_x', str(image_size_x))
        depth_bp.set_attribute('image_size_y', str(image_size_y))
        depth_bp.set_attribute('fov', str(fov))
        depth_bp.set_attribute('sensor_tick', '0.1')  # 10 FPS
        
        self.depth_sensor = self.world.spawn_actor(depth_bp, camera_transform, attach_to=vehicle)
        self.depth_sensor.listen(lambda image: self._on_depth_received(image))
        
        # Semantic Segmentation Camera
        semantic_bp = blueprint_library.find('sensor.camera.semantic_segmentation')
        semantic_bp.set_attribute('image_size_x', str(image_size_x))
        semantic_bp.set_attribute('image_size_y', str(image_size_y))
        semantic_bp.set_attribute('fov', str(fov))
        semantic_bp.set_attribute('sensor_tick', '0.1')  # 10 FPS
        
        self.semantic_sensor = self.world.spawn_actor(semantic_bp, camera_transform, attach_to=vehicle)
        self.semantic_sensor.listen(lambda image: self._on_semantic_received(image))
        
        logger.info("All sensors setup complete")
    
    def _on_rgb_received(self, image: carla.Image) -> None:
        """Handle RGB image reception"""
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        array = array[:, :, :3]  # Remove alpha channel
        array = array[:, :, ::-1]  # BGR to RGB
        
        self._store_sensor_data('rgb', image.frame, image.timestamp, array, image.transform)
    
    def _on_depth_received(self, image: carla.Image) -> None:
        """Handle depth image reception"""
        # Convert depth image to normalized depth map
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        # Extract depth from RGB encoding
        depth = array[:, :, 0] + array[:, :, 1] * 256 + array[:, :, 2] * 256 * 256
        depth = depth.astype(np.float32) / (256 * 256 * 256 - 1)  # Normalize to [0, 1]
        depth = depth * 1000.0  # Scale to meters (assuming max depth of 1000m)
        
        self._store_sensor_data('depth', image.frame, image.timestamp, depth, image.transform)
    
    def _on_semantic_received(self, image: carla.Image) -> None:
        """Handle semantic segmentation image reception"""
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        # Use red channel for semantic labels
        semantic = array[:, :, 2]  # Red channel contains semantic labels
        
        self._store_sensor_data('semantic', image.frame, image.timestamp, semantic, image.transform)
    
    def _store_sensor_data(self, sensor_type: str, frame: int, timestamp: float, 
                          data: np.ndarray, transform: carla.Transform) -> None:
        """Store sensor data and check for synchronization"""
        if frame not in self.current_frame_data:
            self.current_frame_data[frame] = {
                'timestamp': timestamp,
                'transform': transform,
                'received_count': 0
            }
        
        self.current_frame_data[frame][sensor_type] = data
        self.current_frame_data[frame]['received_count'] += 1
        
        # Check if all sensors for this frame have been received
        if self.current_frame_data[frame]['received_count'] == self.expected_sensors:
            self._create_synchronized_data(frame)
    
    def _create_synchronized_data(self, frame: int) -> None:
        """Create synchronized sensor data entry"""
        frame_data = self.current_frame_data[frame]
        
        sensor_data = SensorData(
            timestamp=frame_data['timestamp'],
            frame_id=frame,
            rgb_image=frame_data.get('rgb'),
            depth_image=frame_data.get('depth'),
            semantic_image=frame_data.get('semantic'),
            camera_transform=frame_data['transform']
        )
        
        self.sensor_data.append(sensor_data)
        
        # Clean up processed frame data
        del self.current_frame_data[frame]
        
        logger.debug(f"Synchronized data created for frame {frame}")
    
    def get_sensor_data(self) -> List[SensorData]:
        """Get all collected sensor data"""
        return self.sensor_data
    
    def clear_data(self) -> None:
        """Clear all collected sensor data"""
        self.sensor_data.clear()
        self.current_frame_data.clear()
        self.frame_counter = 0
        logger.info("Sensor data cleared")
    
    def destroy(self) -> None:
        """Clean up sensors"""
        if self.rgb_sensor:
            self.rgb_sensor.destroy()
        if self.depth_sensor:
            self.depth_sensor.destroy()
        if self.semantic_sensor:
            self.semantic_sensor.destroy()
        logger.info("All sensors destroyed")

# --- Function to add camera and display ---
def add_camera_and_display(scenario: NurecScenario) -> PygameDisplay:
    """
    Creates a Pygame display and adds a NUREC camera to render to it.
    """
    pygame_display = PygameDisplay()
    
    # Add a NUREC camera. We'll use the 'camera_front_wide_120fov' as an example.
    # The lambda function tells the scenario to send the rendered image to our display.
    scenario.add_camera(
        "camera_front_wide_120fov", 
        lambda image: pygame_display.setImage(image, (1, 1), (0, 0)), 
        framerate=30, 
        resolution_ratio=0.25 # Lower resolution for better performance
    )
    
    logger.info("Pygame display and NUREC camera have been added to the scenario.")
    return pygame_display
