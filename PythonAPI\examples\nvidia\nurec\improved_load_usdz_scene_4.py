# Enhanced CARLA-NUREC-Cosmos Transfer Integration Pipeline
# SPDX-FileCopyrightText: © 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# SPDX-License-Identifier: MIT

"""
CARLA-NUREC-Cosmos Transfer Integration Pipeline

This script provides a comprehensive pipeline for integrating CARLA with Cosmos Transfer
to generate diverse training data from NUREC scenarios. The pipeline:

1. Loads NUREC scenarios from USDZ files
2. Captures synchronized multi-sensor data (RGB, depth, semantic segmentation)
3. Records ground-truth control maps for Cosmos Transfer
4. Provides interface for environmental condition changes
5. Exports data in Cosmos Transfer compatible formats
6. Supports prompt-driven semantic control for data generation

Key Features:
- Multi-sensor synchronization and data capture
- Environmental condition controls (weather, lighting, time of day)
- Cosmos Transfer data preparation and export
- Real-time visualization with Pygame
- Comprehensive logging and error handling
"""

import carla
import argparse
import logging
import sys
import os
import json
import numpy as np
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum

# --- Step 1: Configure Detailed Logging ---
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("Nurec_Verbose_Loader")
logger.info("--- Starting NUREC Scenario Loader Script ---")


# --- Step 2: Import NUREC components ---
try:
    logger.info("Attempting to import NurecScenario and handle_exception...")
    from nurec_integration import NurecScenario
    from utils import handle_exception
    # Import PygameDisplay for visualization
    from pygame_display import PygameDisplay
    # Import constants for ego vehicle access
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    logger.error("Please ensure you have run 'pip install -r requirements.txt' and compiled gRPC protos.")
    sys.exit(1)

# --- Step 3: Define Data Structures and Enums ---

class EnvironmentalCondition(Enum):
    """Environmental conditions for Cosmos Transfer prompts"""
    CLEAR_DAY = "clear sunny day"
    OVERCAST = "overcast cloudy sky"
    HEAVY_RAIN = "heavy rain with wet roads"
    LIGHT_RAIN = "light rain with misty atmosphere"
    FOG = "dense fog with low visibility"
    SUNSET = "golden hour sunset lighting"
    SUNRISE = "early morning sunrise"
    NIGHT = "nighttime scene with streetlights"
    SNOW = "snowy winter conditions"
    DESERT = "dry desert road environment"
    URBAN_NIGHT = "urban nighttime with neon lights"
    STORM = "stormy weather with dark clouds"

@dataclass
class SensorData:
    """Container for synchronized sensor data"""
    timestamp: float
    frame_id: int
    rgb_image: Optional[np.ndarray] = None
    depth_image: Optional[np.ndarray] = None
    semantic_image: Optional[np.ndarray] = None
    camera_transform: Optional[carla.Transform] = None
    
@dataclass
class CosmosTransferBatch:
    """Container for Cosmos Transfer input data"""
    rgb_sequence: List[np.ndarray]
    depth_sequence: List[np.ndarray]
    semantic_sequence: List[np.ndarray]
    prompt: str
    metadata: Dict[str, Any]
    output_path: str

# --- Step 4: Multi-Sensor Data Capture System ---

class MultiSensorManager:
    """
    Manages multiple synchronized sensors for Cosmos Transfer data generation.
    Captures RGB, depth, and semantic segmentation data simultaneously.
    """
    
    def __init__(self, world: carla.World, output_dir: str = "cosmos_data"):
        self.world = world
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Sensor storage
        self.rgb_sensor: Optional[carla.Actor] = None
        self.depth_sensor: Optional[carla.Actor] = None
        self.semantic_sensor: Optional[carla.Actor] = None
        
        # Data storage
        self.sensor_data: List[SensorData] = []
        self.current_frame_data: Dict[str, Any] = {}
        self.frame_counter = 0
        
        # Synchronization
        self.expected_sensors = 3  # RGB, depth, semantic
        self.received_count = 0
        
        logger.info(f"MultiSensorManager initialized with output directory: {self.output_dir}")
    
    def setup_sensors(self, vehicle: carla.Actor, 
                     camera_transform: carla.Transform = None,
                     image_size_x: int = 1920,
                     image_size_y: int = 1080,
                     fov: float = 90.0) -> None:
        """Setup RGB, depth, and semantic segmentation sensors"""
        
        if camera_transform is None:
            camera_transform = carla.Transform(
                carla.Location(x=1.5, z=2.4),
                carla.Rotation(pitch=0, yaw=0, roll=0)
            )
        
        blueprint_library = self.world.get_blueprint_library()
        
        # RGB Camera
        rgb_bp = blueprint_library.find('sensor.camera.rgb')
        rgb_bp.set_attribute('image_size_x', str(image_size_x))
        rgb_bp.set_attribute('image_size_y', str(image_size_y))
        rgb_bp.set_attribute('fov', str(fov))
        rgb_bp.set_attribute('sensor_tick', '0.0')  # Capture every frame

        self.rgb_sensor = self.world.spawn_actor(rgb_bp, camera_transform, attach_to=vehicle)
        self.rgb_sensor.listen(lambda image: self._on_rgb_received(image))
        logger.info(f"RGB sensor spawned: {self.rgb_sensor.id}")

        # Depth Camera
        depth_bp = blueprint_library.find('sensor.camera.depth')
        depth_bp.set_attribute('image_size_x', str(image_size_x))
        depth_bp.set_attribute('image_size_y', str(image_size_y))
        depth_bp.set_attribute('fov', str(fov))
        depth_bp.set_attribute('sensor_tick', '0.0')  # Capture every frame

        self.depth_sensor = self.world.spawn_actor(depth_bp, camera_transform, attach_to=vehicle)
        self.depth_sensor.listen(lambda image: self._on_depth_received(image))
        logger.info(f"Depth sensor spawned: {self.depth_sensor.id}")

        # Semantic Segmentation Camera
        semantic_bp = blueprint_library.find('sensor.camera.semantic_segmentation')
        semantic_bp.set_attribute('image_size_x', str(image_size_x))
        semantic_bp.set_attribute('image_size_y', str(image_size_y))
        semantic_bp.set_attribute('fov', str(fov))
        semantic_bp.set_attribute('sensor_tick', '0.0')  # Capture every frame

        self.semantic_sensor = self.world.spawn_actor(semantic_bp, camera_transform, attach_to=vehicle)
        self.semantic_sensor.listen(lambda image: self._on_semantic_received(image))
        logger.info(f"Semantic sensor spawned: {self.semantic_sensor.id}")
        
        logger.info("All sensors setup complete")
    
    def _on_rgb_received(self, image: carla.Image) -> None:
        """Handle RGB image reception"""
        logger.info(f"RGB image received: frame {image.frame}, timestamp {image.timestamp}")
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        array = array[:, :, :3]  # Remove alpha channel
        array = array[:, :, ::-1]  # BGR to RGB

        # Save a test image to verify sensor is working
        if len(self.sensor_data) == 0:  # Save first image
            test_path = self.output_dir / "test_rgb_first.npy"
            np.save(test_path, array)
            logger.info(f"Saved test RGB image to {test_path}")

        self._store_sensor_data('rgb', image.frame, image.timestamp, array, image.transform)

    def _on_depth_received(self, image: carla.Image) -> None:
        """Handle depth image reception"""
        logger.info(f"Depth image received: frame {image.frame}, timestamp {image.timestamp}")
        # Convert depth image to normalized depth map
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        # Extract depth from RGB encoding
        depth = array[:, :, 0] + array[:, :, 1] * 256 + array[:, :, 2] * 256 * 256
        depth = depth.astype(np.float32) / (256 * 256 * 256 - 1)  # Normalize to [0, 1]
        depth = depth * 1000.0  # Scale to meters (assuming max depth of 1000m)

        self._store_sensor_data('depth', image.frame, image.timestamp, depth, image.transform)

    def _on_semantic_received(self, image: carla.Image) -> None:
        """Handle semantic segmentation image reception"""
        logger.info(f"Semantic image received: frame {image.frame}, timestamp {image.timestamp}")
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))
        # Use red channel for semantic labels
        semantic = array[:, :, 2]  # Red channel contains semantic labels

        self._store_sensor_data('semantic', image.frame, image.timestamp, semantic, image.transform)
    
    def _store_sensor_data(self, sensor_type: str, frame: int, timestamp: float,
                          data: np.ndarray, transform: carla.Transform) -> None:
        """Store sensor data and check for synchronization"""
        logger.debug(f"Storing {sensor_type} data for frame {frame}")

        if frame not in self.current_frame_data:
            self.current_frame_data[frame] = {
                'timestamp': timestamp,
                'transform': transform,
                'received_count': 0
            }

        self.current_frame_data[frame][sensor_type] = data
        self.current_frame_data[frame]['received_count'] += 1

        logger.debug(f"Frame {frame} now has {self.current_frame_data[frame]['received_count']}/{self.expected_sensors} sensors")

        # Check if all sensors for this frame have been received
        if self.current_frame_data[frame]['received_count'] == self.expected_sensors:
            self._create_synchronized_data(frame)
    
    def _create_synchronized_data(self, frame: int) -> None:
        """Create synchronized sensor data entry"""
        frame_data = self.current_frame_data[frame]
        
        sensor_data = SensorData(
            timestamp=frame_data['timestamp'],
            frame_id=frame,
            rgb_image=frame_data.get('rgb'),
            depth_image=frame_data.get('depth'),
            semantic_image=frame_data.get('semantic'),
            camera_transform=frame_data['transform']
        )
        
        self.sensor_data.append(sensor_data)
        
        # Clean up processed frame data
        del self.current_frame_data[frame]
        
        logger.debug(f"Synchronized data created for frame {frame}")
    
    def get_sensor_data(self) -> List[SensorData]:
        """Get all collected sensor data"""
        return self.sensor_data
    
    def clear_data(self) -> None:
        """Clear all collected sensor data"""
        self.sensor_data.clear()
        self.current_frame_data.clear()
        self.frame_counter = 0
        logger.info("Sensor data cleared")
    
    def destroy(self) -> None:
        """Clean up sensors"""
        if self.rgb_sensor:
            self.rgb_sensor.destroy()
        if self.depth_sensor:
            self.depth_sensor.destroy()
        if self.semantic_sensor:
            self.semantic_sensor.destroy()
        logger.info("All sensors destroyed")

# --- Function to add camera and display ---
def add_camera_and_display(scenario: NurecScenario) -> PygameDisplay:
    """
    Creates a Pygame display and adds a NUREC camera to render to it.
    """
    pygame_display = PygameDisplay()
    
    # Add a NUREC camera. We'll use the 'camera_front_wide_120fov' as an example.
    # The lambda function tells the scenario to send the rendered image to our display.
    scenario.add_camera(
        "camera_front_wide_120fov", 
        lambda image: pygame_display.setImage(image, (1, 1), (0, 0)), 
        framerate=30, 
        resolution_ratio=0.25 # Lower resolution for better performance
    )
    
    logger.info("Pygame display and NUREC camera have been added to the scenario.")
    return pygame_display

# --- Step 5: Cosmos Transfer Integration ---

class CosmosTransferManager:
    """
    Manages data export and preparation for Cosmos Transfer processing.
    Handles batch creation, prompt generation, and data formatting.
    """

    def __init__(self, output_base_dir: str = "cosmos_transfer_data"):
        self.output_base_dir = Path(output_base_dir)
        self.output_base_dir.mkdir(exist_ok=True, parents=True)
        self.batch_counter = 0

        logger.info(f"CosmosTransferManager initialized with base directory: {self.output_base_dir}")

    def create_batch(self, sensor_data: List[SensorData],
                    environmental_condition: EnvironmentalCondition,
                    sequence_length: int = 30,
                    custom_prompt: str = None) -> CosmosTransferBatch:
        """
        Create a Cosmos Transfer batch from sensor data.

        Args:
            sensor_data: List of synchronized sensor data
            environmental_condition: Target environmental condition
            sequence_length: Number of frames in the sequence
            custom_prompt: Custom prompt override

        Returns:
            CosmosTransferBatch ready for processing
        """

        # Ensure we have enough data
        if len(sensor_data) < sequence_length:
            logger.warning(f"Not enough sensor data ({len(sensor_data)}) for sequence length ({sequence_length})")
            sequence_length = len(sensor_data)

        # Select sequence data
        sequence_data = sensor_data[:sequence_length]

        # Extract sequences
        rgb_sequence = [data.rgb_image for data in sequence_data if data.rgb_image is not None]
        depth_sequence = [data.depth_image for data in sequence_data if data.depth_image is not None]
        semantic_sequence = [data.semantic_image for data in sequence_data if data.semantic_image is not None]

        # Generate prompt
        prompt = custom_prompt if custom_prompt else self._generate_prompt(environmental_condition)

        # Create output directory for this batch
        batch_id = f"batch_{self.batch_counter:04d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        batch_output_path = self.output_base_dir / batch_id
        batch_output_path.mkdir(exist_ok=True)

        # Create metadata
        metadata = {
            "batch_id": batch_id,
            "sequence_length": len(rgb_sequence),
            "environmental_condition": environmental_condition.value,
            "prompt": prompt,
            "timestamp": datetime.now().isoformat(),
            "frame_ids": [data.frame_id for data in sequence_data],
            "timestamps": [data.timestamp for data in sequence_data]
        }

        batch = CosmosTransferBatch(
            rgb_sequence=rgb_sequence,
            depth_sequence=depth_sequence,
            semantic_sequence=semantic_sequence,
            prompt=prompt,
            metadata=metadata,
            output_path=str(batch_output_path)
        )

        self.batch_counter += 1
        logger.info(f"Created Cosmos Transfer batch: {batch_id}")

        return batch

    def export_batch(self, batch: CosmosTransferBatch,
                    save_individual_frames: bool = True,
                    save_video: bool = True) -> Dict[str, str]:
        """
        Export batch data to disk in Cosmos Transfer compatible format.

        Args:
            batch: CosmosTransferBatch to export
            save_individual_frames: Whether to save individual frame images
            save_video: Whether to create video sequences

        Returns:
            Dictionary with paths to exported files
        """

        batch_path = Path(batch.output_path)
        exported_files = {}

        # Save metadata
        metadata_path = batch_path / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(batch.metadata, f, indent=2)
        exported_files['metadata'] = str(metadata_path)

        # Save prompt
        prompt_path = batch_path / "prompt.txt"
        with open(prompt_path, 'w') as f:
            f.write(batch.prompt)
        exported_files['prompt'] = str(prompt_path)

        if save_individual_frames:
            # Create directories for each data type
            rgb_dir = batch_path / "rgb_frames"
            depth_dir = batch_path / "depth_frames"
            semantic_dir = batch_path / "semantic_frames"

            rgb_dir.mkdir(exist_ok=True)
            depth_dir.mkdir(exist_ok=True)
            semantic_dir.mkdir(exist_ok=True)

            # Save RGB frames
            rgb_paths = []
            for i, rgb_frame in enumerate(batch.rgb_sequence):
                frame_path = rgb_dir / f"frame_{i:06d}.png"
                self._save_image(rgb_frame, frame_path, 'rgb')
                rgb_paths.append(str(frame_path))
            exported_files['rgb_frames'] = rgb_paths

            # Save depth frames
            depth_paths = []
            for i, depth_frame in enumerate(batch.depth_sequence):
                frame_path = depth_dir / f"frame_{i:06d}.png"
                self._save_image(depth_frame, frame_path, 'depth')
                depth_paths.append(str(frame_path))
            exported_files['depth_frames'] = depth_paths

            # Save semantic frames
            semantic_paths = []
            for i, semantic_frame in enumerate(batch.semantic_sequence):
                frame_path = semantic_dir / f"frame_{i:06d}.png"
                self._save_image(semantic_frame, frame_path, 'semantic')
                semantic_paths.append(str(frame_path))
            exported_files['semantic_frames'] = semantic_paths

        if save_video:
            # Note: Video creation would require additional dependencies like OpenCV
            # For now, we'll create a placeholder for video export functionality
            logger.info("Video export functionality would be implemented here with OpenCV")
            exported_files['video_note'] = "Video export requires OpenCV implementation"

        logger.info(f"Batch exported to: {batch_path}")
        return exported_files

    def _generate_prompt(self, condition: EnvironmentalCondition) -> str:
        """Generate a descriptive prompt for the environmental condition"""
        base_prompts = {
            EnvironmentalCondition.CLEAR_DAY: "Transform this driving scene to a clear sunny day with bright blue skies and excellent visibility",
            EnvironmentalCondition.OVERCAST: "Transform this driving scene to an overcast day with cloudy gray skies and diffused lighting",
            EnvironmentalCondition.HEAVY_RAIN: "Transform this driving scene to heavy rain with wet reflective roads, water droplets, and stormy atmosphere",
            EnvironmentalCondition.LIGHT_RAIN: "Transform this driving scene to light rain with misty atmosphere and slightly wet surfaces",
            EnvironmentalCondition.FOG: "Transform this driving scene to dense fog with severely reduced visibility and atmospheric haze",
            EnvironmentalCondition.SUNSET: "Transform this driving scene to golden hour sunset with warm orange lighting and long shadows",
            EnvironmentalCondition.SUNRISE: "Transform this driving scene to early morning sunrise with soft golden light and fresh atmosphere",
            EnvironmentalCondition.NIGHT: "Transform this driving scene to nighttime with streetlights, headlights, and dark surroundings",
            EnvironmentalCondition.SNOW: "Transform this driving scene to snowy winter conditions with snow-covered roads and cold atmosphere",
            EnvironmentalCondition.DESERT: "Transform this driving scene to a dry desert road with sandy environment and harsh sunlight",
            EnvironmentalCondition.URBAN_NIGHT: "Transform this driving scene to urban nighttime with neon lights, city glow, and bustling atmosphere",
            EnvironmentalCondition.STORM: "Transform this driving scene to stormy weather with dark threatening clouds and dramatic lighting"
        }

        return base_prompts.get(condition, condition.value)

    def _save_image(self, image_array: np.ndarray, path: Path, image_type: str) -> None:
        """Save image array to disk with appropriate formatting"""
        try:
            if image_type == 'rgb':
                # RGB image - save as is
                from PIL import Image
                img = Image.fromarray(image_array.astype(np.uint8))
                img.save(path)
            elif image_type == 'depth':
                # Depth image - normalize and save as grayscale
                from PIL import Image
                # Normalize depth to 0-255 range
                depth_normalized = ((image_array / image_array.max()) * 255).astype(np.uint8)
                img = Image.fromarray(depth_normalized, mode='L')
                img.save(path)
            elif image_type == 'semantic':
                # Semantic segmentation - save as grayscale with original labels
                from PIL import Image
                img = Image.fromarray(image_array.astype(np.uint8), mode='L')
                img.save(path)
        except ImportError:
            logger.warning("PIL not available, saving as numpy array")
            np.save(path.with_suffix('.npy'), image_array)

# --- Step 6: Environmental Condition Controls ---

class EnvironmentalController:
    """
    Controls environmental conditions in CARLA for diverse data generation.
    """

    def __init__(self, world: carla.World):
        self.world = world
        self.weather = world.get_weather()
        logger.info("EnvironmentalController initialized")

    def apply_condition(self, condition: EnvironmentalCondition) -> None:
        """Apply environmental condition to CARLA world"""

        weather_presets = {
            EnvironmentalCondition.CLEAR_DAY: carla.WeatherParameters(
                cloudiness=10.0, precipitation=0.0, sun_altitude_angle=70.0,
                sun_azimuth_angle=0.0, precipitation_deposits=0.0, wind_intensity=10.0,
                fog_density=0.0, fog_distance=0.0, wetness=0.0
            ),
            EnvironmentalCondition.OVERCAST: carla.WeatherParameters(
                cloudiness=80.0, precipitation=0.0, sun_altitude_angle=45.0,
                sun_azimuth_angle=0.0, precipitation_deposits=0.0, wind_intensity=20.0,
                fog_density=0.0, fog_distance=0.0, wetness=0.0
            ),
            EnvironmentalCondition.HEAVY_RAIN: carla.WeatherParameters(
                cloudiness=90.0, precipitation=80.0, sun_altitude_angle=30.0,
                sun_azimuth_angle=0.0, precipitation_deposits=50.0, wind_intensity=50.0,
                fog_density=10.0, fog_distance=200.0, wetness=80.0
            ),
            EnvironmentalCondition.LIGHT_RAIN: carla.WeatherParameters(
                cloudiness=60.0, precipitation=30.0, sun_altitude_angle=40.0,
                sun_azimuth_angle=0.0, precipitation_deposits=20.0, wind_intensity=30.0,
                fog_density=5.0, fog_distance=500.0, wetness=40.0
            ),
            EnvironmentalCondition.FOG: carla.WeatherParameters(
                cloudiness=70.0, precipitation=0.0, sun_altitude_angle=20.0,
                sun_azimuth_angle=0.0, precipitation_deposits=0.0, wind_intensity=5.0,
                fog_density=80.0, fog_distance=50.0, wetness=0.0
            ),
            EnvironmentalCondition.SUNSET: carla.WeatherParameters(
                cloudiness=20.0, precipitation=0.0, sun_altitude_angle=10.0,
                sun_azimuth_angle=270.0, precipitation_deposits=0.0, wind_intensity=15.0,
                fog_density=0.0, fog_distance=0.0, wetness=0.0
            ),
            EnvironmentalCondition.SUNRISE: carla.WeatherParameters(
                cloudiness=30.0, precipitation=0.0, sun_altitude_angle=15.0,
                sun_azimuth_angle=90.0, precipitation_deposits=0.0, wind_intensity=10.0,
                fog_density=5.0, fog_distance=1000.0, wetness=0.0
            ),
            EnvironmentalCondition.NIGHT: carla.WeatherParameters(
                cloudiness=40.0, precipitation=0.0, sun_altitude_angle=-30.0,
                sun_azimuth_angle=0.0, precipitation_deposits=0.0, wind_intensity=20.0,
                fog_density=0.0, fog_distance=0.0, wetness=0.0
            ),
            EnvironmentalCondition.SNOW: carla.WeatherParameters(
                cloudiness=90.0, precipitation=60.0, sun_altitude_angle=20.0,
                sun_azimuth_angle=0.0, precipitation_deposits=80.0, wind_intensity=40.0,
                fog_density=20.0, fog_distance=300.0, wetness=0.0
            ),
            EnvironmentalCondition.STORM: carla.WeatherParameters(
                cloudiness=100.0, precipitation=90.0, sun_altitude_angle=15.0,
                sun_azimuth_angle=0.0, precipitation_deposits=70.0, wind_intensity=80.0,
                fog_density=30.0, fog_distance=100.0, wetness=90.0
            )
        }

        if condition in weather_presets:
            weather = weather_presets[condition]
            self.world.set_weather(weather)
            logger.info(f"Applied weather condition: {condition.value}")
        else:
            logger.warning(f"Weather preset not available for condition: {condition.value}")

    def reset_weather(self) -> None:
        """Reset weather to default clear conditions"""
        self.world.set_weather(self.weather)
        logger.info("Weather reset to default")

def main() -> None:
    """
    Enhanced main function with Cosmos Transfer integration.
    """
    argparser = argparse.ArgumentParser(description=__doc__)
    argparser.add_argument('--host', default='127.0.0.1', help='IP of the host server')
    argparser.add_argument('-p', '--port', default=2000, type=int, help='TCP port to listen to')
    argparser.add_argument('-np', '--nurec-port', default=46435, type=int, help='NUREC port')
    argparser.add_argument('-u', '--usdz-filename', required=True, help='Path to the USDZ file')
    argparser.add_argument('--output-dir', default='cosmos_transfer_data', help='Output directory for Cosmos Transfer data')
    argparser.add_argument('--sequence-length', default=30, type=int, help='Length of video sequences for Cosmos Transfer')
    argparser.add_argument('--environmental-conditions', nargs='+',
                          choices=[c.name for c in EnvironmentalCondition],
                          default=['CLEAR_DAY', 'HEAVY_RAIN', 'SUNSET', 'NIGHT'],
                          help='Environmental conditions to generate data for')
    argparser.add_argument('--enable-multi-sensor', action='store_true',
                          help='Enable multi-sensor data capture (RGB, depth, semantic)')
    argparser.add_argument('--export-cosmos-data', action='store_true',
                          help='Export data in Cosmos Transfer format')
    argparser.add_argument('--debug', action='store_true',
                          help='Enable debug logging')
    args = argparser.parse_args()

    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled")

    logger.info(f"Script arguments: {vars(args)}")

    client = None
    display: Optional[PygameDisplay] = None
    sensor_manager: Optional[MultiSensorManager] = None
    cosmos_manager: Optional[CosmosTransferManager] = None
    env_controller: Optional[EnvironmentalController] = None

    try:
        # --- Step 7: Connect to CARLA Client ---
        logger.info(f"Attempting to connect to CARLA at {args.host}:{args.port}...")
        client = carla.Client(args.host, args.port)
        client.set_timeout(60.0)
        server_version = client.get_server_version()
        logger.info(f"Successfully connected to CARLA. Server version: {server_version}")

        world = client.get_world()

        # --- Step 8: Initialize Managers ---
        if args.enable_multi_sensor:
            sensor_manager = MultiSensorManager(world, args.output_dir)
            logger.info("Multi-sensor manager initialized")

        if args.export_cosmos_data:
            cosmos_manager = CosmosTransferManager(args.output_dir)
            logger.info("Cosmos Transfer manager initialized")

        env_controller = EnvironmentalController(world)

        # --- Step 9: Initialize NurecScenario ---
        logger.info(f"Initializing NurecScenario with file: {args.usdz_filename} on port {args.nurec_port}")
        logger.info("This step involves starting the NuRec service and loading data. It may take a moment...")

        with NurecScenario(client, args.usdz_filename, port=args.nurec_port) as scenario:
            logger.info("--- NurecScenario context entered SUCCESSFULLY! ---")

            # --- Step 10: Setup Visualization and Sensors ---
            display = add_camera_and_display(scenario)

            # Setup multi-sensor capture if enabled - FIXED: Use correct attribute access
            if sensor_manager and hasattr(scenario, 'actor_mapping') and EGO_TRACK_ID in scenario.actor_mapping:
                ego_vehicle = scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                sensor_manager.setup_sensors(ego_vehicle)
                logger.info("Multi-sensor setup complete")

            # Get scenario information
            if hasattr(scenario, 'get_scenario_time_range'):
                start_time, end_time = scenario.get_scenario_time_range()
                duration_seconds = (end_time - start_time) / 1_000_000  # Convert microseconds to seconds
                logger.info(f"Scenario duration: {duration_seconds:.2f} seconds ({start_time} to {end_time} microseconds)")

            logger.info("Scenario loaded and ready.")

            # --- Step 11: Process Environmental Conditions ---
            environmental_conditions = [EnvironmentalCondition[name] for name in args.environmental_conditions]

            for condition in environmental_conditions:
                logger.info(f"Processing environmental condition: {condition.value}")

                # Apply environmental condition
                env_controller.apply_condition(condition)

                # Wait for weather to settle
                time.sleep(2.0)

                # Clear previous sensor data
                if sensor_manager:
                    sensor_manager.clear_data()

                # --- Step 12: Start Replay ---
                logger.info("Attempting to start scenario replay...")
                scenario.start_replay()
                logger.info("Replay started successfully.")

                # --- Step 13: Data Collection Loop ---
                logger.info("Entering data collection loop...")
                tick_count = 0
                max_ticks = args.sequence_length * 10  # Allow for some buffer

                while not scenario.is_done() and tick_count < max_ticks:
                    # Tick both the scenario and the world to ensure sensors work
                    scenario.tick()
                    world.tick()  # This is crucial for CARLA sensors to work

                    if tick_count % 100 == 0:
                        logger.info(f"Scenario tick {tick_count} for condition {condition.value}")
                        if sensor_manager:
                            logger.info(f"Collected {len(sensor_manager.get_sensor_data())} synchronized frames")

                    # Add a small delay to allow sensor callbacks to process
                    time.sleep(0.01)

                    tick_count += 1

                    # Check if we have enough data for this condition
                    if sensor_manager and len(sensor_manager.get_sensor_data()) >= args.sequence_length:
                        logger.info(f"Sufficient data collected for {condition.value}")
                        break

                logger.info(f"Data collection finished for {condition.value} after {tick_count} ticks")

                # --- Step 14: Export Cosmos Transfer Data ---
                if cosmos_manager and sensor_manager:
                    sensor_data = sensor_manager.get_sensor_data()
                    if len(sensor_data) >= args.sequence_length:
                        logger.info(f"Creating Cosmos Transfer batch for {condition.value}")

                        batch = cosmos_manager.create_batch(
                            sensor_data=sensor_data,
                            environmental_condition=condition,
                            sequence_length=args.sequence_length
                        )

                        exported_files = cosmos_manager.export_batch(batch)
                        logger.info(f"Exported batch for {condition.value}: {len(exported_files)} file groups")
                    else:
                        logger.warning(f"Insufficient data for {condition.value}: {len(sensor_data)} frames")

                # Reset weather for next condition
                env_controller.reset_weather()
                time.sleep(1.0)

            logger.info("All environmental conditions processed successfully")

    except RuntimeError as e:
        logger.error(f"A CARLA Runtime Error occurred: {e}", exc_info=True)
        logger.error("This often means the CARLA server is not running or is unresponsive.")
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt detected, exiting gracefully.")
    except Exception as e:
        logger.error(f"An unhandled exception occurred: {e}", exc_info=True)
        handle_exception(e)
    finally:
        # --- Comprehensive Cleanup ---
        if sensor_manager is not None:
            logger.info("Destroying sensor manager.")
            sensor_manager.destroy()

        if display is not None:
            logger.info("Destroying Pygame display.")
            display.destroy()

        if env_controller is not None:
            logger.info("Resetting environmental conditions.")
            env_controller.reset_weather()

        logger.info("--- Enhanced CARLA-Cosmos Transfer Pipeline execution finished. ---")


if __name__ == '__main__':
    main()
