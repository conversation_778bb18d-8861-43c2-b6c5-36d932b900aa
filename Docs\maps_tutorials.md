# Custom maps

In CARLA, a map includes the 3D model of a town and a definition of its road network. The road network is defined through the [__OpenDRIVE__](https://www.asam.net/standards/detail/opendrive/) standard. CARLA provides a diverse array of maps out of the box ready to use for a multitude of applications. User's can also create their own maps and load them into CARLA. The following set of tutorials detail the necessary steps for creating and loading custom maps into CARLA

* [__Overview__](tuto_M_custom_map_overview.md)
* [__Road painting__](tuto_M_custom_road_painter.md)
* [__Custom buildings__](tuto_M_custom_buildings.md) 
* [__Generate map__](tuto_M_generate_map.md)
* [__Add map package__](tuto_M_add_map_package.md)
* [__Add map source__](tuto_M_add_map_source.md)
* [__Alternative methods__](tuto_M_add_map_alternative.md)