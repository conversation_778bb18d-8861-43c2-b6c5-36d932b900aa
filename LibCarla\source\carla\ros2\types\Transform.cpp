// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Transform.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "Transform.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define geometry_msgs_msg_Vector3_max_cdr_typesize 24ULL;
#define geometry_msgs_msg_Transform_max_cdr_typesize 56ULL;
#define geometry_msgs_msg_Quaternion_max_cdr_typesize 32ULL;
#define geometry_msgs_msg_Vector3_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Transform_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Quaternion_max_key_cdr_typesize 0ULL;

geometry_msgs::msg::Transform::Transform()
{
}

geometry_msgs::msg::Transform::~Transform()
{
}

geometry_msgs::msg::Transform::Transform(
        const Transform& x)
{
    m_translation = x.m_translation;
    m_rotation = x.m_rotation;
}

geometry_msgs::msg::Transform::Transform(
        Transform&& x) noexcept
{
    m_translation = std::move(x.m_translation);
    m_rotation = std::move(x.m_rotation);
}

geometry_msgs::msg::Transform& geometry_msgs::msg::Transform::operator =(
        const Transform& x)
{
    m_translation = x.m_translation;
    m_rotation = x.m_rotation;

    return *this;
}

geometry_msgs::msg::Transform& geometry_msgs::msg::Transform::operator =(
        Transform&& x) noexcept
{
    m_translation = std::move(x.m_translation);
    m_rotation = std::move(x.m_rotation);

    return *this;
}

bool geometry_msgs::msg::Transform::operator ==(
        const Transform& x) const
{
    return (m_translation == x.m_translation && m_rotation == x.m_rotation);
}

bool geometry_msgs::msg::Transform::operator !=(
        const Transform& x) const
{
    return !(*this == x);
}

size_t geometry_msgs::msg::Transform::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return geometry_msgs_msg_Transform_max_cdr_typesize;
}

size_t geometry_msgs::msg::Transform::getCdrSerializedSize(
        const geometry_msgs::msg::Transform& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += geometry_msgs::msg::Vector3::getCdrSerializedSize(data.translation(), current_alignment);
    current_alignment += geometry_msgs::msg::Quaternion::getCdrSerializedSize(data.rotation(), current_alignment);

    return current_alignment - initial_alignment;
}

void geometry_msgs::msg::Transform::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_translation;
    scdr << m_rotation;
}

void geometry_msgs::msg::Transform::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_translation;
    dcdr >> m_rotation;
}

/*!
 * @brief This function copies the value in member translation
 * @param _translation New value to be copied in member translation
 */
void geometry_msgs::msg::Transform::translation(
        const geometry_msgs::msg::Vector3& _translation)
{
    m_translation = _translation;
}

/*!
 * @brief This function moves the value in member translation
 * @param _translation New value to be moved in member translation
 */
void geometry_msgs::msg::Transform::translation(
        geometry_msgs::msg::Vector3&& _translation)
{
    m_translation = std::move(_translation);
}

/*!
 * @brief This function returns a constant reference to member translation
 * @return Constant reference to member translation
 */
const geometry_msgs::msg::Vector3& geometry_msgs::msg::Transform::translation() const
{
    return m_translation;
}

/*!
 * @brief This function returns a reference to member translation
 * @return Reference to member translation
 */
geometry_msgs::msg::Vector3& geometry_msgs::msg::Transform::translation()
{
    return m_translation;
}
/*!
 * @brief This function copies the value in member rotation
 * @param _rotation New value to be copied in member rotation
 */
void geometry_msgs::msg::Transform::rotation(
        const geometry_msgs::msg::Quaternion& _rotation)
{
    m_rotation = _rotation;
}

/*!
 * @brief This function moves the value in member rotation
 * @param _rotation New value to be moved in member rotation
 */
void geometry_msgs::msg::Transform::rotation(
        geometry_msgs::msg::Quaternion&& _rotation)
{
    m_rotation = std::move(_rotation);
}

/*!
 * @brief This function returns a constant reference to member rotation
 * @return Constant reference to member rotation
 */
const geometry_msgs::msg::Quaternion& geometry_msgs::msg::Transform::rotation() const
{
    return m_rotation;
}

/*!
 * @brief This function returns a reference to member rotation
 * @return Reference to member rotation
 */
geometry_msgs::msg::Quaternion& geometry_msgs::msg::Transform::rotation()
{
    return m_rotation;
}

size_t geometry_msgs::msg::Transform::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return geometry_msgs_msg_Transform_max_key_cdr_typesize;
}

bool geometry_msgs::msg::Transform::isKeyDefined()
{
    return false;
}

void geometry_msgs::msg::Transform::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
