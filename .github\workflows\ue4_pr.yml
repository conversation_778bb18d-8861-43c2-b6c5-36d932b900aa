name: UE4-PR

on:
  pull_request:
    branches:
      - ue4-dev
    types: [opened, synchronize, reopened]

concurrency:
  group: pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  ubuntu-pr:
    name: Ubuntu PR
    uses: ./.github/workflows/_ci-ubuntu.yml
    with:
      python-versions: "3.12"
      smoke-test-python-versions: "3.12"
      additional-maps: false
      upload-package: false
      upload-replace-latest: false
      upload-docker: false

  windows-pr:
    name: Windows PR
    uses: ./.github/workflows/_ci-windows.yml
    with:
      python-versions: "3.12"
      additional-maps: false
      upload-package: false
      upload-replace-latest: false
