# Tutorials

Here you will find the multitude of tutorials available to help you understand how to use CARLA's many features.

## General

### CARLA features

* [__Retrieve simulation data__](tuto_G_retrieve_data.md) — A step by step guide to properly gather data using the recorder.
* [__Traffic manager__](tuto_G_traffic_manager.md) — How to use traffic manager to guide traffic around your town.
* [__Texture streaming__](tuto_G_texture_streaming.md) — Modify textures of map objects in real time to add variation.
* [__Instance segmentation camera__](tuto_G_instance_segmentation_sensor.md) — Use an instance segmentation camera to distinguish objects of the same class.
* [__Bounding boxes__](tuto_G_bounding_boxes.md) — Project bounding boxes from CARLA objects into the camera.
* [__Pedestrian bones__](tuto_G_pedestrian_bones.md) — Project pedestrian skeleton into camera plane.
* [__Control walker skeletons__](tuto_G_control_walker_skeletons.md) — Animate walkers using skeletons.

### Building and integration

* [__Build Unreal Engine and CARLA in Docker__](build_docker_unreal.md) — Build Unreal Engine and CARLA in Docker.
* [__CarSim Integration__](tuto_G_carsim_integration.md) — Tutorial on how to run a simulation using the CarSim vehicle dynamics engine.
* [__RLlib Integration__](tuto_G_rllib_integration.md) — Find out how to run your own experiment using the RLlib library.
* [__Chrono Integration__](tuto_G_chrono.md) — Use the Chrono integration to simulation physics.
* [__PyGame control__](tuto_G_pygame.md) — Use PyGame to display the output of camera sensors.

## Assets and maps

* [__Generate maps with OpenStreetMap__](tuto_G_openstreetmap.md) — Use OpenStreetMap to generate maps for use in simulations.
* [__Add a new vehicle__](tuto_A_add_vehicle.md) — Prepare a vehicle to be used in CARLA.
* [__Add new props__](tuto_A_add_props.md) — Import additional props into CARLA.
* [__Create standalone packages__](tuto_A_create_standalone.md) — Generate and handle standalone packages for assets.
* [__Material customization__](tuto_A_material_customization.md) — Edit vehicle and building materials.

## Developers

* [__How to upgrade content__](tuto_D_contribute_assets.md) — Add new content to CARLA.
* [__Create a sensor__](tuto_D_create_sensor.md) — Develop a new sensor to be used in CARLA.
* [__Create semantic tags__](tuto_D_create_semantic_tags.md) — Define customized tags for semantic segmentation.
* [__Customize vehicle suspension__](tuto_D_customize_vehicle_suspension.md) — Modify the suspension system of a vehicle.
* [__Generate detailed colliders__](tuto_D_generate_colliders.md) — Create detailed colliders for vehicles.
* [__Make a release__](tuto_D_make_release.md) — How to make a release of CARLA

## Video tutorials

* [__Fundamentals__](https://www.youtube.com/watch?v=pONr1R1dy88) — Learn the fundamental concepts of CARLA and start your first script. [__CODE__](https://carla-releases.s3.us-east-005.backblazeb2.com/Docs/Fundamentals.ipynb)
* [__An in depth look at CARLA's sensors__](https://www.youtube.com/watch?v=om8klsBj4rc) — An in depth look at CARLA's sensors and how to use them. [__CODE__](https://carla-releases.s3.us-east-005.backblazeb2.com/Docs/Sensors_code.zip)
