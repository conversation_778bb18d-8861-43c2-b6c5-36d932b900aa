#!/usr/bin/env python3

"""
Test NUREC Display

Simple test to verify NUREC display is working properly.
This script loads a NUREC scenario and shows the display without manual control.
"""

import carla
import argparse
import logging
import sys
import time

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("TestNurecDisplay")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

def test_nurec_display(args):
    """Test NUREC display functionality"""
    client = None
    scenario = None
    display = None
    
    try:
        # Connect to CARLA
        logger.info(f"Connecting to CARLA at {args.host}:{args.port}")
        client = carla.Client(args.host, args.port)
        client.set_timeout(60.0)
        world = client.get_world()
        logger.info(f"Connected to CARLA: {client.get_server_version()}")
        
        # Load NUREC scenario
        logger.info(f"Loading NUREC scenario: {args.usdz_filename}")
        with NurecScenario(client, args.usdz_filename, port=args.nurec_port) as scenario:
            logger.info("NUREC scenario loaded successfully")
            
            # Setup display
            display = PygameDisplay()
            logger.info("PygameDisplay created")
            
            # Add camera
            scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: display.setImage(image, (1, 1), (0, 0)),
                framerate=30,
                resolution_ratio=0.75
            )
            logger.info("NUREC camera added")
            
            # Check if ego vehicle exists
            if EGO_TRACK_ID in scenario.actor_mapping:
                ego_vehicle = scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {ego_vehicle.id}")
                
                # Enable physics
                scenario.actor_mapping[EGO_TRACK_ID].set_physics(True, scenario.get_sim_time())
                logger.info("Vehicle physics enabled")
            else:
                logger.warning("No ego vehicle found")
            
            # Start replay
            scenario.start_replay()
            logger.info("Scenario replay started")
            logger.info("You should now see the NUREC display window with the reconstructed environment")
            logger.info("The test will run for 30 seconds...")
            
            # Run for 30 seconds
            start_time = time.time()
            tick_count = 0
            
            while time.time() - start_time < 30:
                scenario.tick()
                world.tick()
                tick_count += 1
                
                if tick_count % 100 == 0:
                    elapsed = time.time() - start_time
                    logger.info(f"Test running... {elapsed:.1f}s elapsed, {tick_count} ticks")
                
                time.sleep(0.01)  # Small delay
            
            logger.info("Test completed successfully!")
            logger.info("If you saw the NUREC window with the reconstructed environment, the display is working correctly.")
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
        handle_exception(e)
        return False
    
    finally:
        if display:
            display.destroy()
        logger.info("Test cleanup complete")
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test NUREC Display")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    logger.info("Starting NUREC display test...")
    success = test_nurec_display(args)
    
    if success:
        logger.info("NUREC display test passed!")
        logger.info("You can now try the manual control scripts:")
        logger.info("python3 manual_control_nurec_simple.py --usdz-filename " + args.usdz_filename)
    else:
        logger.error("NUREC display test failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
