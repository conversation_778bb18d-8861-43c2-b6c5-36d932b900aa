// Copyright (c) 2024 Institut fuer Technik der Informationsverarbeitung (ITIV) at the 
// Karlsruhe Institute of Technology
//
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#pragma once

#include "carla/Memory.h"
#include "carla/sensor/RawData.h"
#include "carla/sensor/data/V2XData.h"

#include <cstdint>
#include <cstring>
class CAMContainer;
namespace carla
{
    namespace sensor
    {

        class SensorData;

        namespace s11n
        {

            // ===========================================================================
            // -- V2XSerializer --------------------------------------------------------
            // ===========================================================================

            /// Serializes the data generated by V2X sensors.
            // CAM
            class CAMDataSerializer
            {
            public:
                template <typename Sensor>
                static Buffer Serialize(
                    const Sensor &sensor,
                    const data::CAMDataS &data,
                    Buffer &&output);

                static SharedPtr<SensorData> Deserialize(RawData &&data);
            };

            template <typename Sensor>
            inline Buffer CAMDataSerializer::Serialize(
                const Sensor &,
                const data::CAMDataS &data,
                Buffer &&output)
            {
                output.copy_from(data.MessageList);
                return std::move(output);
            }

            // Custom message
            class CustomV2XDataSerializer
            {
            public:
                template <typename Sensor>
                static Buffer Serialize(
                    const Sensor &sensor,
                    const data::CustomV2XDataS &data,
                    Buffer &&output);

                static SharedPtr<SensorData> Deserialize(RawData &&data);
            };

            template <typename Sensor>
            inline Buffer CustomV2XDataSerializer::Serialize(
                const Sensor &,
                const data::CustomV2XDataS &data,
                Buffer &&output)
            {
                output.copy_from(data.MessageList);
                return std::move(output);
            }

        } // namespace s11n
    }     // namespace sensor
} // namespace carla
