# Update CARLA

*   [__Update commands summary__](#update-commands-summary)
*   [__Get the lastest binary release__](#get-latest-binary-release)
*   [__Update Linux and Windows build__](#update-linux-and-windows-build)
	*   [Clean the build](#clean-the-build)
	*   [Pull from origin](#pull-from-origin)
	*   [Download the assets](#download-the-assets)
	*   [Launch the server](#launch-the-server)
*   [__Get development assets__](#get-development-assets)

To post unexpected issues, doubts or suggestions, feel free to login in the CARLA forum.

<div class="build-buttons">
<p>
<a href="https://github.com/carla-simulator/carla/discussions/" target="_blank" class="btn btn-neutral" title="Go to the latest CARLA release">
CARLA forum</a>
</p>
</div>

---
## Update commands summary

<details>
<summary> Show command lines to update CARLA</summary>

```sh
# Update a CARLA packaged release.
#   1. Delete the current one.
#   2. Follow the Quick start installation to get the one desired.


# Update Linux build.
git checkout master
make clean
git pull origin master
./Update.sh


# Update Windows build.
git checkout master
make clean
git pull origin master
#   Erase the content in `Unreal\CarlaUE4\Content\Carla`.
#   Go to `\Util\ContentVersions.txt`.
#   Download the latest content.
#   Extract the new content in `Unreal\CarlaUE4\Content\Carla`.


# Get development assets.
#   Delete the `/Carla` folder containing previous assets.
#   Go to the main carla folder.
git clone https://bitbucket.org/carla-simulator/carla-content Unreal/CarlaUE4/Content/Carla

```
</details>

---
## Get latest binary release

Binary releases are prepackaged and thus, tied to a specific version of CARLA. To get the latest, erase the previous and follow the [quick start installation](start_quickstart.md) to get the one desired.

Releases are listed in __Development__ in the CARLA repository. There is also a highly experimental __Nightly build__ containing the current state of CARLA up to date.

<div class="build-buttons">
<p>
<a href="https://github.com/carla-simulator/carla/blob/master/Docs/download.md" target="_blank" class="btn btn-neutral" title="Go to the list of CARLA releases">
<span class="icon icon-github"></span> Get releases</a>
</p>

<p>
<a href="https://carla-releases.s3.us-east-005.backblazeb2.com/Linux/Dev/CARLA_Latest.tar.gz" target="_blank" class="btn btn-neutral" title="Go to the linux nightly CARLA build">
<span class="icon fa-cloud-download"></span> Get the linux nightly build</a>
</p>

<p>
<a href="https://carla-releases.s3.us-east-005.backblazeb2.com/Linux/Dev/AdditionalMaps_Latest.tar.gz" target="_blank" class="btn btn-neutral" title="Go to the linux nightly AdditionalMaps build">
<span class="icon fa-cloud-download"></span> Get the linux nightly build additional maps</a>
</p>

<p>
<a href="https://carla-releases.s3.us-east-005.backblazeb2.com/Windows/Dev/CARLA_Latest.zip" target="_blank" class="btn btn-neutral" title="Go to the windows nightly CARLA build">
<span class="icon fa-cloud-download"></span> Get the windows nightly build</a>
</p>

<p>
<a href="https://carla-releases.s3.us-east-005.backblazeb2.com/Windows/Dev/AdditionalMaps_Latest.zip" target="_blank" class="btn btn-neutral" title="Go to the windows nightly AdditionalMaps build">
<span class="icon fa-cloud-download"></span> Get the windows nightly build additional maps</a>
</p>

</div>

---
## Update Linux and Windows build

Make sure to be in the local `master` branch before the update. Then, merge or rebase the changes to other branches and solve possible conflicts.

```sh
git checkout master
```

### Clean the build

Go to the main CARLA folder and delete binaries and temporals generated by the previous build.
```sh
make clean
```

### Pull from origin

Get the current version from `master` in the CARLA repository.
```sh
git pull origin master
```

### Download the assets

__Linux.__
```sh
./Update.sh
```

__Windows.__

__1.__ Erase the previous content in `Unreal\CarlaUE4\Content\Carla`.
__2.__ Go to `\Util\ContentVersions.txt`.
__3.__ Download the content for `latest`.
__4.__ Extract the new content in `Unreal\CarlaUE4\Content\Carla`.

!!! Note
    In order to work with that the CARLA team is devleoping, go to __get development assets__ below.

### Launch the server

Run the server in spectator view to make sure that everything worked properly.

```sh
make launch
```

---
## Get development assets

The CARLA team works with assets still in development. These models and maps have a [public git repository][contentrepolink] where the CARLA team regularly pushes latest updates. Assets are still unfinished, using them is only recommended for developers.

In order to handle this repository it is advisted to install [git-lfs][gitlfslink]. The repository is modified regularly, and git-lfs works faster with large binary files.

To clone the repository, __go to the main CARLA directory__ and run the following command.

```sh
git clone https://bitbucket.org/carla-simulator/carla-content Unreal/CarlaUE4/Content/Carla
```

!!! Warning
    Delete the `/Carla` folder containing the assets before cloning the repository. Otherwise, an error will show.

[contentrepolink]: https://bitbucket.org/carla-simulator/carla-content
[gitlfslink]: https://github.com/git-lfs/git-lfs/wiki/Installation
