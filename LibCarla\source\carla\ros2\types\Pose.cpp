// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file Pose.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "Pose.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define geometry_msgs_msg_Pose_max_cdr_typesize 56ULL;
#define geometry_msgs_msg_Point_max_cdr_typesize 24ULL;
#define geometry_msgs_msg_Quaternion_max_cdr_typesize 32ULL;
#define geometry_msgs_msg_Pose_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Point_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Quaternion_max_key_cdr_typesize 0ULL;

geometry_msgs::msg::Pose::Pose()
{
}

geometry_msgs::msg::Pose::~Pose()
{
}

geometry_msgs::msg::Pose::Pose(
        const Pose& x)
{
    m_position = x.m_position;
    m_orientation = x.m_orientation;
}

geometry_msgs::msg::Pose::Pose(
        Pose&& x) noexcept
{
    m_position = std::move(x.m_position);
    m_orientation = std::move(x.m_orientation);
}

geometry_msgs::msg::Pose& geometry_msgs::msg::Pose::operator =(
        const Pose& x)
{
    m_position = x.m_position;
    m_orientation = x.m_orientation;

    return *this;
}

geometry_msgs::msg::Pose& geometry_msgs::msg::Pose::operator =(
        Pose&& x) noexcept
{
    m_position = std::move(x.m_position);
    m_orientation = std::move(x.m_orientation);

    return *this;
}

bool geometry_msgs::msg::Pose::operator ==(
        const Pose& x) const
{
    return (m_position == x.m_position && m_orientation == x.m_orientation);
}

bool geometry_msgs::msg::Pose::operator !=(
        const Pose& x) const
{
    return !(*this == x);
}

size_t geometry_msgs::msg::Pose::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return geometry_msgs_msg_Pose_max_cdr_typesize;
}

size_t geometry_msgs::msg::Pose::getCdrSerializedSize(
        const geometry_msgs::msg::Pose& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += geometry_msgs::msg::Point::getCdrSerializedSize(data.position(), current_alignment);
    current_alignment += geometry_msgs::msg::Quaternion::getCdrSerializedSize(data.orientation(), current_alignment);

    return current_alignment - initial_alignment;
}

void geometry_msgs::msg::Pose::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_position;
    scdr << m_orientation;
}

void geometry_msgs::msg::Pose::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_position;
    dcdr >> m_orientation;
}

/*!
 * @brief This function copies the value in member position
 * @param _position New value to be copied in member position
 */
void geometry_msgs::msg::Pose::position(
        const geometry_msgs::msg::Point& _position)
{
    m_position = _position;
}

/*!
 * @brief This function moves the value in member position
 * @param _position New value to be moved in member position
 */
void geometry_msgs::msg::Pose::position(
        geometry_msgs::msg::Point&& _position)
{
    m_position = std::move(_position);
}

/*!
 * @brief This function returns a constant reference to member position
 * @return Constant reference to member position
 */
const geometry_msgs::msg::Point& geometry_msgs::msg::Pose::position() const
{
    return m_position;
}

/*!
 * @brief This function returns a reference to member position
 * @return Reference to member position
 */
geometry_msgs::msg::Point& geometry_msgs::msg::Pose::position()
{
    return m_position;
}

/*!
 * @brief This function copies the value in member orientation
 * @param _orientation New value to be copied in member orientation
 */
void geometry_msgs::msg::Pose::orientation(
        const geometry_msgs::msg::Quaternion& _orientation)
{
    m_orientation = _orientation;
}

/*!
 * @brief This function moves the value in member orientation
 * @param _orientation New value to be moved in member orientation
 */
void geometry_msgs::msg::Pose::orientation(
        geometry_msgs::msg::Quaternion&& _orientation)
{
    m_orientation = std::move(_orientation);
}

/*!
 * @brief This function returns a constant reference to member orientation
 * @return Constant reference to member orientation
 */
const geometry_msgs::msg::Quaternion& geometry_msgs::msg::Pose::orientation() const
{
    return m_orientation;
}

/*!
 * @brief This function returns a reference to member orientation
 * @return Reference to member orientation
 */
geometry_msgs::msg::Quaternion& geometry_msgs::msg::Pose::orientation()
{
    return m_orientation;
}


size_t geometry_msgs::msg::Pose::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return geometry_msgs_msg_Pose_max_key_cdr_typesize;
}

bool geometry_msgs::msg::Pose::isKeyDefined()
{
    return false;
}

void geometry_msgs::msg::Pose::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
