# CARLA-NUREC-Cosmos Transfer Integration Pipeline Requirements
# Install with: pip install -r cosmos_requirements.txt

# Core dependencies
numpy>=1.21.0
pillow>=8.3.0
pygame>=2.1.0

# Data processing and analysis
pathlib2>=2.3.6; python_version < '3.4'

# Optional dependencies for enhanced functionality
opencv-python>=4.5.0  # For video processing and advanced image operations
matplotlib>=3.5.0     # For data visualization and analysis
pandas>=1.3.0         # For data analysis and reporting
tqdm>=4.62.0          # For progress bars during batch processing

# Development and testing dependencies (optional)
pytest>=6.2.0         # For unit testing
pytest-cov>=2.12.0    # For test coverage
black>=21.9.0          # For code formatting
flake8>=4.0.0          # For code linting

# Documentation dependencies (optional)
sphinx>=4.2.0          # For documentation generation
sphinx-rtd-theme>=1.0.0  # For documentation theme

# CARLA-specific dependencies
# Note: CARLA Python API should be installed separately
# Follow CARLA installation instructions for your platform

# NUREC-specific dependencies
# Note: NUREC integration components are included in the project
# Additional NUREC dependencies may be required based on your setup

# System-specific notes:
# - Windows: Ensure Visual C++ redistributables are installed
# - Linux: May require additional system packages for pygame and OpenCV
# - macOS: May require Xcode command line tools

# Installation commands for different platforms:
#
# Windows:
# pip install -r cosmos_requirements.txt
#
# Linux (Ubuntu/Debian):
# sudo apt-get update
# sudo apt-get install python3-dev python3-pip
# pip install -r cosmos_requirements.txt
#
# macOS:
# brew install python3
# pip3 install -r cosmos_requirements.txt

# For GPU acceleration (optional, for advanced image processing):
# torch>=1.9.0
# torchvision>=0.10.0
# tensorflow>=2.6.0  # Alternative to PyTorch

# For cloud storage integration (optional):
# boto3>=1.18.0      # For AWS S3
# google-cloud-storage>=1.42.0  # For Google Cloud Storage
# azure-storage-blob>=12.8.0    # For Azure Blob Storage
