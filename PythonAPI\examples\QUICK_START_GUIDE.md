# Quick Start Guide: CARLA-Cosmos Transfer Pipeline

Get up and running with the CARLA-NUREC-Cosmos Transfer integration pipeline in minutes.

## Prerequisites Checklist

- [ ] CARLA server installed and running
- [ ] NUREC integration components available
- [ ] Python 3.7+ installed
- [ ] USDZ scenario file ready

## Step 1: Installation

```bash
# Navigate to the examples directory
cd PythonAPI/examples/

# Install required dependencies
pip install -r cosmos_requirements.txt

# Verify CARLA connection (optional)
python -c "import carla; client = carla.Client('localhost', 2000); print('CARLA connected:', client.get_server_version())"
```

## Step 2: Basic Test Run

Start with a simple test to verify everything works:

```bash
# Basic run with minimal settings
python improved_load_usdz_scene_3.py \
    --usdz-filename path/to/your/scenario.usdz \
    --environmental-conditions CLEAR_DAY
```

Expected output:
```
2025-01-18 14:30:22.123 [INFO] [Nurec_Verbose_Loader] - --- Starting NUREC Scenario Loader Script ---
2025-01-18 14:30:22.124 [INFO] [Nurec_Verbose_Loader] - Successfully imported NUREC components.
2025-01-18 14:30:22.125 [INFO] [Nurec_Verbose_Loader] - Successfully connected to CARLA. Server version: 0.9.15
...
2025-01-18 14:30:45.678 [INFO] [Nurec_Verbose_Loader] - All environmental conditions processed successfully
```

## Step 3: Enable Multi-Sensor Capture

Add depth and semantic segmentation data capture:

```bash
python improved_load_usdz_scene_3.py \
    --usdz-filename path/to/your/scenario.usdz \
    --enable-multi-sensor \
    --environmental-conditions CLEAR_DAY HEAVY_RAIN
```

This will capture:
- RGB camera images
- Depth maps
- Semantic segmentation masks

## Step 4: Export Cosmos Transfer Data

Enable data export for Cosmos Transfer processing:

```bash
python improved_load_usdz_scene_3.py \
    --usdz-filename path/to/your/scenario.usdz \
    --enable-multi-sensor \
    --export-cosmos-data \
    --environmental-conditions CLEAR_DAY HEAVY_RAIN SUNSET NIGHT \
    --sequence-length 30 \
    --output-dir ./my_cosmos_data
```

## Step 5: Verify Output

Check the generated data:

```bash
# List generated batches
ls -la my_cosmos_data/

# Check a specific batch
ls -la my_cosmos_data/batch_0001_*/

# View metadata
cat my_cosmos_data/batch_0001_*/metadata.json

# View prompt
cat my_cosmos_data/batch_0001_*/prompt.txt
```

Expected directory structure:
```
my_cosmos_data/
├── batch_0001_20250118_143022/
│   ├── metadata.json
│   ├── prompt.txt
│   ├── rgb_frames/
│   │   ├── frame_000000.png
│   │   ├── frame_000001.png
│   │   └── ...
│   ├── depth_frames/
│   │   ├── frame_000000.png
│   │   └── ...
│   └── semantic_frames/
│       ├── frame_000000.png
│       └── ...
├── batch_0002_20250118_143055/
└── cosmos_transfer_summary.json
```

## Step 6: Run Examples

Explore the example script for more advanced usage:

```bash
# Run all examples
python cosmos_transfer_example.py --example all

# Run specific example
python cosmos_transfer_example.py --example single
```

## Common Commands

### Quick Test (Minimal)
```bash
python improved_load_usdz_scene_3.py --usdz-filename scenario.usdz --environmental-conditions CLEAR_DAY
```

### Full Pipeline (Recommended)
```bash
python improved_load_usdz_scene_3.py \
    --usdz-filename scenario.usdz \
    --enable-multi-sensor \
    --export-cosmos-data \
    --environmental-conditions CLEAR_DAY HEAVY_RAIN SUNSET NIGHT \
    --sequence-length 45
```

### Batch Processing
```bash
# Process multiple scenarios
for scenario in scenarios/*.usdz; do
    python improved_load_usdz_scene_3.py \
        --usdz-filename "$scenario" \
        --enable-multi-sensor \
        --export-cosmos-data \
        --output-dir "./output/$(basename "$scenario" .usdz)"
done
```

## Troubleshooting

### Issue: "Failed to import a required module"
**Solution**: Ensure NUREC components are in the correct path
```bash
# Check if NUREC components exist
ls nvidia/nurec/
# Should show: nurec_integration.py, pygame_display.py, utils.py
```

### Issue: "CARLA Runtime Error"
**Solution**: Verify CARLA server is running
```bash
# Start CARLA server (adjust path as needed)
./CarlaUE4.sh -carla-server -fps=20
```

### Issue: "Insufficient data collected"
**Solution**: Increase sequence length or check scenario duration
```bash
# Use longer sequences
--sequence-length 60

# Or check scenario length first
python -c "from nvidia.nurec.scenario import Scenario; s = Scenario('scenario.usdz'); print(f'Scenario length: {len(s.tracks)} tracks')"
```

### Issue: Out of memory
**Solution**: Reduce image resolution or sequence length
```bash
# Modify sensor setup in the script to use smaller images
# Or use shorter sequences
--sequence-length 15
```

## Next Steps

1. **Review Generated Data**: Check the quality and completeness of exported batches
2. **Customize Prompts**: Modify prompts in `CosmosTransferManager` for your specific use case
3. **Add Custom Conditions**: Extend `EnvironmentalCondition` enum for new weather types
4. **Integrate with Cosmos Transfer**: Submit your data to Cosmos Transfer for processing
5. **Analyze Results**: Use the generated summary reports to understand your dataset

## Getting Help

- Check the full documentation: `COSMOS_TRANSFER_README.md`
- Review example usage: `cosmos_transfer_example.py`
- Enable debug logging: Add `--log-level DEBUG` to commands
- Check CARLA documentation for server setup issues
- Verify NUREC integration components are properly installed

## Performance Tips

- Start with shorter sequences (15-30 frames) for testing
- Use fewer environmental conditions initially
- Monitor disk space - each batch can be several GB
- Consider processing conditions sequentially for large datasets
- Use SSD storage for better I/O performance

Happy data generation! 🚗🌦️✨
