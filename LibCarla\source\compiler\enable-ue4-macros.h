// Copyright (c) 2017 Computer Vision Center (CVC) at the Universitat Autonoma
// de Barcelona (UAB).
//
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#if defined(_MSC_VER)
#  pragma warning(pop)
#  ifdef UpdateResource
#    undef UpdateResource
#  endif
#endif

#if defined(__clang__)
#  pragma clang diagnostic pop
#endif

#pragma pop_macro("GET_AI_CONFIG_VAR")
#pragma pop_macro("BT_VLOG")
#pragma pop_macro("BT_SEARCHLOG")
#pragma pop_macro("EQSHEADERLOG")
#pragma pop_macro("MEM_STAT_UPDATE_WRAPPER")
#pragma pop_macro("GET_STRUCT_NAME_CHECKED")
#pragma pop_macro("PRINT_TABLE_ROW")
#pragma pop_macro("SIGHT_LOG_SEGMENT")
#pragma pop_macro("SIGHT_LOG_LOCATION")
#pragma pop_macro("ANALYTICS_FLUSH_TRACKING_BEGIN")
#pragma pop_macro("ANALYTICS_FLUSH_TRACKING_END")
#pragma pop_macro("OCULUS_DEVICE_LOOP")
#pragma pop_macro("OPENSLES_RETURN_ON_FAIL")
#pragma pop_macro("OPENSLES_CHECK_ON_FAIL")
#pragma pop_macro("OPENSLES_LOG_ON_FAIL")
#pragma pop_macro("CASE_ENUM_TO_TEXT")
#pragma pop_macro("TRACE_BLENDSPACE_PLAYER")
#pragma pop_macro("SEQUENCER_INSTANCE_PLAYER_TYPE")
#pragma pop_macro("IMAGE_BRUSH")
#pragma pop_macro("BOX_BRUSH")
#pragma pop_macro("BORDER_BRUSH")
#pragma pop_macro("DEFAULT_FONT")
#pragma pop_macro("INTERNAL_DECORATOR")
#pragma pop_macro("LLM_SCOPE_METAL")
#pragma pop_macro("LLM_PLATFORM_SCOPE_METAL")
#pragma pop_macro("METAL_DEBUG_OPTION")
#pragma pop_macro("METAL_DEBUG_ONLY")
#pragma pop_macro("METAL_DEBUG_LAYER")
#pragma pop_macro("METAL_GPUPROFILE")
#pragma pop_macro("UNREAL_TO_METAL_BUFFER_INDEX")
#pragma pop_macro("METAL_TO_UNREAL_BUFFER_INDEX")
#pragma pop_macro("METAL_FATAL_ERROR")
#pragma pop_macro("METAL_FATAL_ASSERT")
#pragma pop_macro("METAL_IGNORED")
#pragma pop_macro("NOT_SUPPORTED")
#pragma pop_macro("METAL_INC_DWORD_STAT_BY")
#pragma pop_macro("CHECK_JNI_RESULT")
#pragma pop_macro("SET_PRESSED")
#pragma pop_macro("ADD_WINDOWS_MESSAGE_STRING")
#pragma pop_macro("ADD_IMN_STRING")
#pragma pop_macro("ADD_IMR_STRING")
#pragma pop_macro("IsTouchEvent")
#pragma pop_macro("ADDTOMAP")
#pragma pop_macro("UE_PACKAGEREADER_CORRUPTPACKAGE_WARNING")
#pragma pop_macro("MUTEX_INITIALIZE")
#pragma pop_macro("MUTEX_DESTROY")
#pragma pop_macro("MUTEX_LOCK")
#pragma pop_macro("MUTEX_UNLOCK")
#pragma pop_macro("SAFE_RELEASE")
#pragma pop_macro("AUDIO_MIXER_DEBUG_LOG")
#pragma pop_macro("AUDIO_PLATFORM_ERROR")
#pragma pop_macro("AUDIO_MIXER_CHECK")
#pragma pop_macro("AUDIO_MIXER_CHECK_GAME_THREAD")
#pragma pop_macro("AUDIO_MIXER_CHECK_AUDIO_PLAT_THREAD")
#pragma pop_macro("DEFINE_AR_COMPONENT_DEBUG_MODE")
#pragma pop_macro("DEFINE_AR_COMPONENT_VIRTUALS")
#pragma pop_macro("DEFINE_AR_SI_DELEGATE_FUNCS")
#pragma pop_macro("DEFINE_AR_BPLIB_DELEGATE_FUNCS")
#pragma pop_macro("DECLARE_AR_SI_DELEGATE_FUNCS")
#pragma pop_macro("DEFINE_AR_DELEGATE_BASE")
#pragma pop_macro("DEFINE_AR_DELEGATE_ONE_PARAM")
#pragma pop_macro("CHECK_HR")
#pragma pop_macro("CHECK_HR_DEFAULT")
#pragma pop_macro("CHECK_HR_COM")
#pragma pop_macro("CHECK_HR_VOID")
#pragma pop_macro("CHECK_AMF_RET")
#pragma pop_macro("CHECK_AMF_NORET")
#pragma pop_macro("CHECK_NV_RES")
#pragma pop_macro("NV_RESULT")
#pragma pop_macro("B")
#pragma pop_macro("AMF_DECLARE_IID")
#pragma pop_macro("AMF_MACRO_STRING2")
#pragma pop_macro("AMF_MACRO_STRING")
#pragma pop_macro("AMF_TODO")
#pragma pop_macro("AMF_ALIGN")
#pragma pop_macro("amf_countof")
#pragma pop_macro("AMF_MIN")
#pragma pop_macro("AMF_MAX")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_DATA")
#pragma pop_macro("AMF_QUERY_INTERFACE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_INTERFACE")
#pragma pop_macro("AMF_GET_PROPERTY_INTERFACE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_TYPE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_INT64")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_DOUBLE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_BOOL")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_RECT")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_SIZE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_POINT")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_RATE")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_RATIO")
#pragma pop_macro("AMF_ASSIGN_PROPERTY_COLOR")
#pragma pop_macro("AMFVariantEmpty")
#pragma pop_macro("AMFVariantBool")
#pragma pop_macro("AMFVariantInt64")
#pragma pop_macro("AMFVariantDouble")
#pragma pop_macro("AMFVariantRect")
#pragma pop_macro("AMFVariantSize")
#pragma pop_macro("AMFVariantPoint")
#pragma pop_macro("AMFVariantRate")
#pragma pop_macro("AMFVariantRatio")
#pragma pop_macro("AMFVariantColor")
#pragma pop_macro("AMFVariantString")
#pragma pop_macro("AMFVariantWString")
#pragma pop_macro("AMFVariantInterface")
#pragma pop_macro("AMF_VARIANT_RETURN_IF_INVALID_POINTER")
#pragma pop_macro("AMFConvertTool")
#pragma pop_macro("AMF_MAKE_FULL_VERSION")
#pragma pop_macro("AMF_GET_MAJOR_VERSION")
#pragma pop_macro("AMF_GET_MINOR_VERSION")
#pragma pop_macro("AMF_GET_SUBMINOR_VERSION")
#pragma pop_macro("AMF_GET_BUILD_VERSION")
#pragma pop_macro("NVENCAPI_STRUCT_VERSION")
#pragma pop_macro("__lseek")
#pragma pop_macro("__pread")
#pragma pop_macro("__pwrite")
#pragma pop_macro("__ftruncate")
#pragma pop_macro("VK_MAKE_VERSION")
#pragma pop_macro("VK_VERSION_MAJOR")
#pragma pop_macro("VK_VERSION_MINOR")
#pragma pop_macro("VK_VERSION_PATCH")
#pragma pop_macro("VK_DEFINE_HANDLE")
#pragma pop_macro("SET_DWORD_STAT_BY_FNAME")
#pragma pop_macro("SET_FLOAT_STAT_BY_FNAME")
#pragma pop_macro("CSV_STAT_PTR")
#pragma pop_macro("CSV_CUSTOM_STAT_DEFINED_BY_PTR")
#pragma pop_macro("HANDLE_CASE")
#pragma pop_macro("KDBG_CODE")
#pragma pop_macro("APPSDBG_CODE")
#pragma pop_macro("TASKGRAPH_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("likely")
#pragma pop_macro("unlikely")
#pragma pop_macro("ALLOC")
#pragma pop_macro("ALLOC_AND_ZERO")
#pragma pop_macro("FREEMEM")
#pragma pop_macro("MEM_INIT")
#pragma pop_macro("LZ4_STATIC_ASSERT")
#pragma pop_macro("MIN")
#pragma pop_macro("HASH_FUNCTION")
#pragma pop_macro("DELTANEXTMAXD")
#pragma pop_macro("DELTANEXTU16")
#pragma pop_macro("UPDATABLE")
#pragma pop_macro("SUBSTRINGTEST")
#pragma pop_macro("FP_TEXT_PASTE")
// #pragma pop_macro("WTEXT")
#pragma pop_macro("CASE")
#pragma pop_macro("LLM_TAG_NAME_ARRAY")
#pragma pop_macro("LLM_TAG_STAT_ARRAY")
#pragma pop_macro("LLM_TAG_STATGROUP_ARRAY")
#pragma pop_macro("bswap_32")
#pragma pop_macro("bswap_64")
#pragma pop_macro("uint32_in_expected_order")
#pragma pop_macro("uint64_in_expected_order")
#pragma pop_macro("LIKELY")
#pragma pop_macro("PERMUTE3")
#pragma pop_macro("DEF_GETPLURALFORM_CAST")
#pragma pop_macro("DEF_ASNUMBER_CAST")
#pragma pop_macro("DEF_ASNUMBER")
#pragma pop_macro("DEF_ASCURRENCY_CAST")
#pragma pop_macro("DEF_ASCURRENCY")
#pragma pop_macro("DEF_ASPERCENT_CAST")
#pragma pop_macro("DEF_ASPERCENT")
#pragma pop_macro("CONDITIONAL_CREATE_TEXT_HISTORY")
#pragma pop_macro("ENUM_CASE_FROM_STRING")
#pragma pop_macro("ENUM_CASE_TO_STRING")
#pragma pop_macro("WRITE_CUSTOM_OPTION")
#pragma pop_macro("READ_BOOL_OPTION")
#pragma pop_macro("READ_CUSTOM_OPTION")
#pragma pop_macro("TEXT_STRINGIFICATION_FUNC_MODIFY_BUFFER_AND_VALIDATE")
#pragma pop_macro("TEXT_STRINGIFICATION_PEEK_MARKER")
#pragma pop_macro("TEXT_STRINGIFICATION_PEEK_INSENSITIVE_MARKER")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_MARKER")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_INSENSITIVE_MARKER")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_MARKER_LEN")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_WHITESPACE")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_WHITESPACE_TO_CHAR")
#pragma pop_macro("TEXT_STRINGIFICATION_SKIP_WHITESPACE_AND_CHAR")
#pragma pop_macro("TEXT_STRINGIFICATION_READ_NUMBER")
#pragma pop_macro("TEXT_STRINGIFICATION_READ_ALNUM")
#pragma pop_macro("TEXT_STRINGIFICATION_READ_QUOTED_STRING")
#pragma pop_macro("TEXT_STRINGIFICATION_READ_SCOPED_ENUM")
#pragma pop_macro("PLATFORM_MAC_MAKE_FOURCC")
#pragma pop_macro("MEMPRO_STATIC_ASSERT")
#pragma pop_macro("ENDIAN_TEST")
#pragma pop_macro("_T")
#pragma pop_macro("KEYLENGTH")
#pragma pop_macro("RKLENGTH")
#pragma pop_macro("NROUNDS")
#pragma pop_macro("GETU32")
#pragma pop_macro("PUTU32")
#pragma pop_macro("DEFINE_LOG_CATEGORY_HELPER")
#pragma pop_macro("ADD_64b_2_64b")
#pragma pop_macro("ADD_16b_2_64b")
#pragma pop_macro("MD5_F")
#pragma pop_macro("MD5_G")
#pragma pop_macro("MD5_H")
#pragma pop_macro("MD5_I")
#pragma pop_macro("ROTLEFT")
#pragma pop_macro("MD5_FF")
#pragma pop_macro("MD5_GG")
#pragma pop_macro("MD5_HH")
#pragma pop_macro("MD5_II")
#pragma pop_macro("ROL32")
#pragma pop_macro("SHABLK0")
#pragma pop_macro("SHABLK")
#pragma pop_macro("_R0")
#pragma pop_macro("_R1")
#pragma pop_macro("_R2")
#pragma pop_macro("_R3")
#pragma pop_macro("_R4")
#pragma pop_macro("GROWABLE_LOGF")
#pragma pop_macro("ABTEST_LOG")
#pragma pop_macro("CPUPROFILERTRACE_OUTPUTBEGINEVENT_PROLOGUE")
#pragma pop_macro("CPUPROFILERTRACE_OUTPUTBEGINEVENT_EPILOGUE")
#pragma pop_macro("STATS_HIERARCHICAL_TIMER_FUNC")
#pragma pop_macro("BENCHMARK")
#pragma pop_macro("TEST")
#pragma pop_macro("TEST_EX")
#pragma pop_macro("TEST_QUAT_ROTATE")
#pragma pop_macro("INTERP_WITH_RANGE")
#pragma pop_macro("TestUnixEquivalent")
#pragma pop_macro("TestYear")
#pragma pop_macro("TestMonth")
#pragma pop_macro("TestMonthOfYear")
#pragma pop_macro("TestDay")
#pragma pop_macro("TestHour")
#pragma pop_macro("TestMinute")
#pragma pop_macro("TestSecond")
#pragma pop_macro("TestMillisecond")
#pragma pop_macro("UE_LOG_UNIX_FILE")
#pragma pop_macro("REGISTER_NAME")
#pragma pop_macro("DECLARE_LOG_CATEGORY_EXTERN_HELPER")
#pragma pop_macro("SCOPED_BOOT_TIMING")
#pragma pop_macro("FOREACH_ENUM_EPIXELFORMAT")
#pragma pop_macro("PLATFORM_CODE_SECTION")
#pragma pop_macro("GCC_PACK")
#pragma pop_macro("GCC_ALIGN")
#pragma pop_macro("PLATFORM_BREAK")
#pragma pop_macro("UE_DEBUG_BREAK_IMPL")
#pragma pop_macro("_aligned_malloc")
#pragma pop_macro("_aligned_realloc")
#pragma pop_macro("_aligned_free")
// #pragma pop_macro("TEXT")
#pragma pop_macro("LLM_SCOPE_APPLE")
#pragma pop_macro("LLM_PLATFORM_SCOPE_APPLE")
#pragma pop_macro("APPLE_PLATFORM_OBJECT_ALLOC_OVERRIDES")
#pragma pop_macro("checkThreadGraph")
#pragma pop_macro("CA_SUPPRESS")
#pragma pop_macro("CA_ASSUME")
#pragma pop_macro("CA_CONSTANT_IF")
#pragma pop_macro("TSAN_BEFORE")
#pragma pop_macro("TSAN_AFTER")
#pragma pop_macro("TSAN_ATOMIC")
#pragma pop_macro("DEPRECATED")
#pragma pop_macro("EMIT_CUSTOM_WARNING_AT_LINE")
#pragma pop_macro("LZ4_QUOTE")
#pragma pop_macro("LZ4_EXPAND_AND_QUOTE")
#pragma pop_macro("LZ4_COMPRESSBOUND")
#pragma pop_macro("LZ4_DECODER_RING_BUFFER_SIZE")
#pragma pop_macro("LZ4_DECOMPRESS_INPLACE_MARGIN")
#pragma pop_macro("LZ4_DECOMPRESS_INPLACE_BUFFER_SIZE")
#pragma pop_macro("LZ4_COMPRESS_INPLACE_BUFFER_SIZE")
#pragma pop_macro("checkLockFreePointerList")
#pragma pop_macro("ExchangeB")
// #pragma pop_macro("TCHAR_TO_ANSI")
// #pragma pop_macro("ANSI_TO_TCHAR")
// #pragma pop_macro("TCHAR_TO_UTF8")
// #pragma pop_macro("UTF8_TO_TCHAR")
// #pragma pop_macro("TCHAR_TO_UTF16")
// #pragma pop_macro("UTF16_TO_TCHAR")
// #pragma pop_macro("TCHAR_TO_UTF32")
// #pragma pop_macro("UTF32_TO_TCHAR")
// #pragma pop_macro("TCHAR_TO_WCHAR")
// #pragma pop_macro("WCHAR_TO_TCHAR")
#pragma pop_macro("FUNC_CONCAT")
#pragma pop_macro("FUNC_DECLARE_DELEGATE")
#pragma pop_macro("FUNC_DECLARE_MULTICAST_DELEGATE")
#pragma pop_macro("FUNC_DECLARE_EVENT")
#pragma pop_macro("DECLARE_DERIVED_EVENT")
#pragma pop_macro("FUNC_DECLARE_DYNAMIC_DELEGATE")
#pragma pop_macro("FUNC_DECLARE_DYNAMIC_DELEGATE_RETVAL")
#pragma pop_macro("FUNC_DECLARE_DYNAMIC_MULTICAST_DELEGATE")
#pragma pop_macro("STATIC_FUNCTION_FNAME")
#pragma pop_macro("BindDynamic")
#pragma pop_macro("AddDynamic")
#pragma pop_macro("AddUniqueDynamic")
#pragma pop_macro("RemoveDynamic")
#pragma pop_macro("IsAlreadyBound")
#pragma pop_macro("DECLARE_DELEGATE")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE")
#pragma pop_macro("DECLARE_EVENT")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE")
#pragma pop_macro("DECLARE_DELEGATE_RetVal")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal")
#pragma pop_macro("DECLARE_DELEGATE_OneParam")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_OneParam")
#pragma pop_macro("DECLARE_EVENT_OneParam")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_OneParam")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_OneParam")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_OneParam")
#pragma pop_macro("DECLARE_DELEGATE_TwoParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_TwoParams")
#pragma pop_macro("DECLARE_EVENT_TwoParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_TwoParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_TwoParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_TwoParams")
#pragma pop_macro("DECLARE_DELEGATE_ThreeParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_ThreeParams")
#pragma pop_macro("DECLARE_EVENT_ThreeParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_ThreeParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_ThreeParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_ThreeParams")
#pragma pop_macro("DECLARE_DELEGATE_FourParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_FourParams")
#pragma pop_macro("DECLARE_EVENT_FourParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_FourParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_FourParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_FourParams")
#pragma pop_macro("DECLARE_DELEGATE_FiveParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_FiveParams")
#pragma pop_macro("DECLARE_EVENT_FiveParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_FiveParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_FiveParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_FiveParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_FiveParams")
#pragma pop_macro("DECLARE_DELEGATE_SixParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_SixParams")
#pragma pop_macro("DECLARE_EVENT_SixParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_SixParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_SixParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_SixParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_SixParams")
#pragma pop_macro("DECLARE_DELEGATE_SevenParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_SevenParams")
#pragma pop_macro("DECLARE_EVENT_SevenParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_SevenParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_SevenParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_SevenParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_SevenParams")
#pragma pop_macro("DECLARE_DELEGATE_EightParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_EightParams")
#pragma pop_macro("DECLARE_EVENT_EightParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_EightParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_EightParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_EightParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_EightParams")
#pragma pop_macro("DECLARE_DELEGATE_NineParams")
#pragma pop_macro("DECLARE_MULTICAST_DELEGATE_NineParams")
#pragma pop_macro("DECLARE_EVENT_NineParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_NineParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_DELEGATE_NineParams")
#pragma pop_macro("DECLARE_DELEGATE_RetVal_NineParams")
#pragma pop_macro("DECLARE_DYNAMIC_DELEGATE_RetVal_NineParams")
#pragma pop_macro("CHECK_CONCURRENT_ACCESS")
#pragma pop_macro("FRAMEPRO_FRAME_START")
#pragma pop_macro("FRAMEPRO_SHUTDOWN")
#pragma pop_macro("FRAMEPRO_SET_PORT")
#pragma pop_macro("FRAMEPRO_SET_SESSION_INFO")
#pragma pop_macro("FRAMEPRO_SET_ALLOCATOR")
#pragma pop_macro("FRAMEPRO_SET_THREAD_NAME")
#pragma pop_macro("FRAMEPRO_THREAD_ORDER")
#pragma pop_macro("FRAMEPRO_REGISTER_STRING")
#pragma pop_macro("FRAMEPRO_START_RECORDING")
#pragma pop_macro("FRAMEPRO_STOP_RECORDING")
#pragma pop_macro("FRAMEPRO_REGISTER_CONNECTION_CHANGED_CALLBACK")
#pragma pop_macro("FRAMEPRO_UNREGISTER_CONNECTION_CHANGED_CALLBACK")
#pragma pop_macro("FRAMEPRO_SET_THREAD_PRIORITY")
#pragma pop_macro("FRAMEPRO_SET_THREAD_AFFINITY")
#pragma pop_macro("FRAMEPRO_BLOCK_SOCKETS")
#pragma pop_macro("FRAMEPRO_UNBLOCK_SOCKETS")
#pragma pop_macro("FRAMEPRO_CLEANUP_THREAD")
#pragma pop_macro("FRAMEPRO_THREAD_SCOPE")
#pragma pop_macro("FRAMEPRO_LOG")
#pragma pop_macro("FRAMEPRO_COLOUR")
#pragma pop_macro("FRAMEPRO_SET_CONDITIONAL_SCOPE_MIN_TIME")
#pragma pop_macro("FRAMEPRO_SCOPE")
#pragma pop_macro("FRAMEPRO_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_NAMED_SCOPE_W")
#pragma pop_macro("FRAMEPRO_ID_SCOPE")
#pragma pop_macro("FRAMEPRO_DYNAMIC_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_ID_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_NAMED_SCOPE_W")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_BOOL_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_BOOL_ID_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_BOOL_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_BOOL_NAMED_SCOPE_W")
#pragma pop_macro("FRAMEPRO_START_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_STOP_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_START_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_STOP_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_STOP_DYNAMIC_SCOPE")
#pragma pop_macro("FRAMEPRO_CONDITIONAL_PARENT_SCOPE")
#pragma pop_macro("FRAMEPRO_SET_SCOPE_COLOUR")
#pragma pop_macro("FRAMEPRO_IDLE_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_NAMED_SCOPE_W")
#pragma pop_macro("FRAMEPRO_IDLE_ID_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_DYNAMIC_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_ID_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_NAMED_SCOPE_W")
#pragma pop_macro("FRAMEPRO_IDLE_START_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_STOP_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_START_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_STOP_NAMED_SCOPE")
#pragma pop_macro("FRAMEPRO_IDLE_CONDITIONAL_STOP_DYNAMIC_SCOPE")
#pragma pop_macro("FRAMEPRO_CUSTOM_STAT")
#pragma pop_macro("FRAMEPRO_DYNAMIC_CUSTOM_STAT")
#pragma pop_macro("FRAMEPRO_SCOPE_CUSTOM_STAT")
#pragma pop_macro("FRAMEPRO_SET_CUSTOM_STAT_GRAPH")
#pragma pop_macro("FRAMEPRO_SET_CUSTOM_STAT_UNIT")
#pragma pop_macro("FRAMEPRO_SET_CUSTOM_STAT_COLOUR")
#pragma pop_macro("FRAMEPRO_HIRES_SCOPE")
#pragma pop_macro("FRAMEPRO_DECL_GLOBAL_HIRES_TIMER")
#pragma pop_macro("FRAMEPRO_GLOBAL_HIRES_SCOPE")
#pragma pop_macro("FRAMEPRO_EVENT")
#pragma pop_macro("FRAMEPRO_WAIT_EVENT_SCOPE")
#pragma pop_macro("FRAMEPRO_TRIGGER_WAIT_EVENT")
#pragma pop_macro("FRAMEPRO_STRINGIZE")
#pragma pop_macro("FRAMEPRO_STRINGIZE2")
#pragma pop_macro("FRAMEPRO_JOIN")
#pragma pop_macro("FRAMEPRO_JOIN2")
#pragma pop_macro("FRAMEPRO_UNIQUE")
#pragma pop_macro("FRAMEPRO_WIDESTR")
#pragma pop_macro("FRAMEPRO_WIDESTR2")
#pragma pop_macro("FRAMEPRO_ASSERT")
#pragma pop_macro("FRAMEPRO_UNREFERENCED")
#pragma pop_macro("FRAMEPRO_GET_CLOCK_COUNT")
#pragma pop_macro("MULTI_STATEMENT")
#pragma pop_macro("FRAMEPRO_ALIGN_STRUCT")
#pragma pop_macro("EMIT_CUSTOM_WARNING")
#pragma pop_macro("DEPRECATED_MACRO")
#pragma pop_macro("PLATFORM_MEMORY_SIZE_BUCKET_LIST")
#pragma pop_macro("PLATFORM_MEMORY_SIZE_BUCKET_ENUM")
#pragma pop_macro("PLATFORM_MEMORY_SIZE_BUCKET_LEXTOSTRING")
#pragma pop_macro("FMemory_Alloca")
#pragma pop_macro("UE_DEBUG_BREAK")
#pragma pop_macro("cvarCheckCode")
#pragma pop_macro("FILE_LOG")
#pragma pop_macro("DECLARE_LLM_MEMORY_STAT")
#pragma pop_macro("DECLARE_LLM_MEMORY_STAT_EXTERN")
#pragma pop_macro("LLMCheckMessage")
#pragma pop_macro("LLMCheckfMessage")
#pragma pop_macro("LLMEnsureMessage")
#pragma pop_macro("LLMCheck")
#pragma pop_macro("LLMCheckf")
#pragma pop_macro("LLMEnsure")
#pragma pop_macro("LLM_ENUM_GENERIC_TAGS")
#pragma pop_macro("LLM_ENUM")
#pragma pop_macro("LLM")
#pragma pop_macro("LLM_IF_ENABLED")
#pragma pop_macro("LLM_SCOPE")
#pragma pop_macro("LLM_PLATFORM_SCOPE")
#pragma pop_macro("LLM_SCOPED_PAUSE_TRACKING")
#pragma pop_macro("LLM_SCOPED_PAUSE_TRACKING_FOR_TRACKER")
#pragma pop_macro("LLM_SCOPED_PAUSE_TRACKING_WITH_ENUM_AND_AMOUNT")
#pragma pop_macro("LLM_REALLOC_SCOPE")
#pragma pop_macro("LLM_REALLOC_PLATFORM_SCOPE")
#pragma pop_macro("LLM_SCOPED_TAG_WITH_STAT")
#pragma pop_macro("LLM_SCOPED_TAG_WITH_STAT_IN_SET")
#pragma pop_macro("LLM_SCOPED_TAG_WITH_STAT_NAME")
#pragma pop_macro("LLM_SCOPED_TAG_WITH_STAT_NAME_IN_SET")
#pragma pop_macro("LLM_SCOPED_SINGLE_PLATFORM_STAT_TAG")
#pragma pop_macro("LLM_SCOPED_SINGLE_PLATFORM_STAT_TAG_IN_SET")
#pragma pop_macro("LLM_SCOPED_SINGLE_STAT_TAG")
#pragma pop_macro("LLM_SCOPED_SINGLE_STAT_TAG_IN_SET")
#pragma pop_macro("LLM_SCOPED_PAUSE_TRACKING_WITH_STAT_AND_AMOUNT")
#pragma pop_macro("LLM_SCOPED_TAG_WITH_OBJECT_IN_SET")
#pragma pop_macro("LLM_PUSH_STATS_FOR_ASSET_TAGS")
#pragma pop_macro("LLM_DUMP_TAG")
#pragma pop_macro("LLM_DUMP_PLATFORM_TAG")
#pragma pop_macro("LLM_SCOPED_SINGLE_RHI_STAT_TAG")
#pragma pop_macro("LLM_SCOPED_SINGLE_RHI_STAT_TAG_IN_SET")
#pragma pop_macro("MEM_TIME")
#pragma pop_macro("MBA_STAT")
#pragma pop_macro("MBG_STAT")
#pragma pop_macro("MALLOCLEAK_WHITELIST_SCOPE")
#pragma pop_macro("MALLOCLEAK_SCOPED_CONTEXT")
#pragma pop_macro("FUNCTION_CHECK_RETURN")
#pragma pop_macro("UE_ASSUME")
#pragma pop_macro("ASSUME")
#pragma pop_macro("UNLIKELY")
#pragma pop_macro("DECLARE_UINT64")
#pragma pop_macro("MS_ALIGN")
#pragma pop_macro("MSVC_PRAGMA")
#pragma pop_macro("FLUSH_CACHE_LINE")
#pragma pop_macro("MSC_FORMAT_DIAGNOSTIC_HELPER_2")
#pragma pop_macro("MSC_FORMAT_DIAGNOSTIC_HELPER")
#pragma pop_macro("COMPILE_WARNING")
#pragma pop_macro("COMPILE_ERROR")
#pragma pop_macro("GCC_DIAGNOSTIC_HELPER")
#pragma pop_macro("checkAtCompileTime")
#pragma pop_macro("DEPRECATED_FORGAME")
#pragma pop_macro("INT32_MAIN_INT32_ARGC_TCHAR_ARGV")
#pragma pop_macro("TEXT_PASTE")
#pragma pop_macro("NAMED_EVENT_STR")
#pragma pop_macro("SCOPED_NAMED_EVENT")
#pragma pop_macro("SCOPED_NAMED_EVENT_FSTRING")
#pragma pop_macro("SCOPED_NAMED_EVENT_TCHAR")
#pragma pop_macro("SCOPED_NAMED_EVENT_TEXT")
#pragma pop_macro("SCOPED_NAMED_EVENT_F")
#pragma pop_macro("SCOPED_PROFILER_COLOR")
#pragma pop_macro("PREPROCESSOR_TO_STRING")
#pragma pop_macro("PREPROCESSOR_TO_STRING_INNER")
#pragma pop_macro("PREPROCESSOR_JOIN")
#pragma pop_macro("PREPROCESSOR_JOIN_INNER")
#pragma pop_macro("PREPROCESSOR_JOIN_FIRST")
#pragma pop_macro("PREPROCESSOR_JOIN_FIRST_INNER")
#pragma pop_macro("PREPROCESSOR_IF")
#pragma pop_macro("PREPROCESSOR_IF_INNER_1")
#pragma pop_macro("PREPROCESSOR_IF_INNER_0")
#pragma pop_macro("PREPROCESSOR_COMMA_SEPARATED")
#pragma pop_macro("DEFINE_VARIABLE")
#pragma pop_macro("PREPROCESSOR_REMOVE_OPTIONAL_PARENS")
#pragma pop_macro("PREPROCESSOR_REMOVE_OPTIONAL_PARENS_IMPL")
#pragma pop_macro("COMPILED_PLATFORM_HEADER")
#pragma pop_macro("COMPILED_PLATFORM_HEADER_WITH_PREFIX")
#pragma pop_macro("FAST_DECIMAL_FORMAT_SIGNED_IMPL")
#pragma pop_macro("FAST_DECIMAL_FORMAT_UNSIGNED_IMPL")
#pragma pop_macro("FAST_DECIMAL_FORMAT_FRACTIONAL_IMPL")
#pragma pop_macro("FAST_DECIMAL_PARSE_INTEGER_IMPL")
#pragma pop_macro("FAST_DECIMAL_PARSE_FRACTIONAL_IMPL")
#pragma pop_macro("LOCTEXT")
#pragma pop_macro("NSLOCTEXT")
#pragma pop_macro("INVTEXT")
#pragma pop_macro("LOCGEN_NUMBER")
#pragma pop_macro("LOCGEN_NUMBER_GROUPED")
#pragma pop_macro("LOCGEN_NUMBER_UNGROUPED")
#pragma pop_macro("LOCGEN_NUMBER_CUSTOM")
#pragma pop_macro("LOCGEN_PERCENT")
#pragma pop_macro("LOCGEN_PERCENT_GROUPED")
#pragma pop_macro("LOCGEN_PERCENT_UNGROUPED")
#pragma pop_macro("LOCGEN_PERCENT_CUSTOM")
#pragma pop_macro("LOCGEN_CURRENCY")
#pragma pop_macro("LOCGEN_DATE_UTC")
#pragma pop_macro("LOCGEN_DATE_LOCAL")
#pragma pop_macro("LOCGEN_TIME_UTC")
#pragma pop_macro("LOCGEN_TIME_LOCAL")
#pragma pop_macro("LOCGEN_DATETIME_UTC")
#pragma pop_macro("LOCGEN_DATETIME_LOCAL")
#pragma pop_macro("LOCGEN_TOUPPER")
#pragma pop_macro("LOCGEN_TOLOWER")
#pragma pop_macro("LOCGEN_FORMAT_ORDERED")
#pragma pop_macro("LOCGEN_FORMAT_NAMED")
#pragma pop_macro("LOCTABLE_NEW")
#pragma pop_macro("LOCTABLE_FROMFILE_ENGINE")
#pragma pop_macro("LOCTABLE_FROMFILE_GAME")
#pragma pop_macro("LOCTABLE_SETSTRING")
#pragma pop_macro("LOCTABLE_SETMETA")
#pragma pop_macro("LOCTABLE")
#pragma pop_macro("_aligned_msize")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Fatal")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Error")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Warning")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Display")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Log")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_Verbose")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_VeryVerbose")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_All")
#pragma pop_macro("UE_LOG_EXPAND_IS_FATAL_SetColor")
#pragma pop_macro("UE_LOG_SOURCE_FILE")
#pragma pop_macro("UE_LOG")
#pragma pop_macro("UE_LOG_CLINKAGE")
#pragma pop_macro("UE_CLOG")
#pragma pop_macro("UE_LOG_ACTIVE")
#pragma pop_macro("UE_LOG_ANY_ACTIVE")
#pragma pop_macro("UE_SUPPRESS")
#pragma pop_macro("UE_GET_LOG_VERBOSITY")
#pragma pop_macro("UE_SET_LOG_VERBOSITY")
#pragma pop_macro("DECLARE_LOG_CATEGORY_EXTERN")
#pragma pop_macro("DEFINE_LOG_CATEGORY")
#pragma pop_macro("DEFINE_LOG_CATEGORY_STATIC")
#pragma pop_macro("DECLARE_LOG_CATEGORY_CLASS")
#pragma pop_macro("DEFINE_LOG_CATEGORY_CLASS")
#pragma pop_macro("UE_SECURITY_LOG")
#pragma pop_macro("NOTIFY_CLIENT_OF_SECURITY_EVENT_IF_NOT_SHIPPING")
#pragma pop_macro("CLOSE_CONNECTION_DUE_TO_SECURITY_VIOLATION_INNER")
#pragma pop_macro("CLOSE_CONNECTION_DUE_TO_SECURITY_VIOLATION")
#pragma pop_macro("logOrEnsureNanError")
#pragma pop_macro("LOG_SCOPE_VERBOSITY_OVERRIDE")
#pragma pop_macro("TRACE_LOG_CATEGORY")
#pragma pop_macro("TRACE_LOG_MESSAGE")
#pragma pop_macro("CREATE_FUNCTION_SHIM")
#pragma pop_macro("CREATE_GLOBAL_SHIM")
#pragma pop_macro("CREATE_DEPRECATED_SHIM")
#pragma pop_macro("CREATE_DEPRECATED_MSG_SHIM")
#pragma pop_macro("LUMIN_MLSDK_API_DEPRECATED_MSG")
#pragma pop_macro("AOS_TO_SOA2_ISPC")
#pragma pop_macro("AOS_TO_SOA3_ISPC")
#pragma pop_macro("AOS_TO_SOA4_ISPC")
#pragma pop_macro("AOS_TO_SOA6_ISPC")
#pragma pop_macro("DEFINE_EXPRESSION_OPERATOR_NODE")
#pragma pop_macro("FOREACH_OCTREE_CHILD_NODE")
#pragma pop_macro("DEFINE_INTERPCURVE_WRAPPER_STRUCT")
#pragma pop_macro("DEFINE_INTERVAL_WRAPPER_STRUCT")
#pragma pop_macro("DEFINE_RANGE_WRAPPER_STRUCT")
#pragma pop_macro("DEFINE_RANGEBOUND_WRAPPER_STRUCT")
#pragma pop_macro("NonZeroAnimWeight")
#pragma pop_macro("NonOneAnimWeight")
#pragma pop_macro("ScalarReciprocal")
#pragma pop_macro("SOA_TO_AOS2_ISPC")
#pragma pop_macro("SOA_TO_AOS3_ISPC")
#pragma pop_macro("SOA_TO_AOS4_ISPC")
#pragma pop_macro("SOA_TO_AOS6_ISPC")
#pragma pop_macro("_PS_CONST")
#pragma pop_macro("_PI32_CONST")
#pragma pop_macro("_PS_CONST_TYPE")
#pragma pop_macro("COPY_XMM_TO_MM")
#pragma pop_macro("COPY_MM_TO_XMM")
#pragma pop_macro("DECLARE_VECTOR_REGISTER")
#pragma pop_macro("VectorZero")
#pragma pop_macro("VectorOne")
#pragma pop_macro("VectorLoad")
#pragma pop_macro("VectorLoadFloat3")
#pragma pop_macro("VectorLoadFloat3_W0")
#pragma pop_macro("VectorLoadFloat3_W1")
#pragma pop_macro("VectorLoadAligned")
#pragma pop_macro("VectorLoadFloat1")
#pragma pop_macro("VectorLoadFloat2")
#pragma pop_macro("VectorSetFloat3")
#pragma pop_macro("VectorSetFloat1")
#pragma pop_macro("VectorSet")
#pragma pop_macro("VectorStoreAligned")
#pragma pop_macro("VectorStoreAlignedStreamed")
#pragma pop_macro("VectorStore")
#pragma pop_macro("VectorStoreFloat3")
#pragma pop_macro("VectorStoreFloat1")
#pragma pop_macro("VectorReplicate")
#pragma pop_macro("VectorAbs")
#pragma pop_macro("VectorNegate")
#pragma pop_macro("VectorAdd")
#pragma pop_macro("VectorSubtract")
#pragma pop_macro("VectorMultiply")
#pragma pop_macro("VectorDivide")
#pragma pop_macro("VectorMultiplyAdd")
#pragma pop_macro("VectorDot3")
#pragma pop_macro("VectorDot4")
#pragma pop_macro("VectorCompareEQ")
#pragma pop_macro("VectorCompareNE")
#pragma pop_macro("VectorCompareGT")
#pragma pop_macro("VectorCompareGE")
#pragma pop_macro("VectorCompareLT")
#pragma pop_macro("VectorCompareLE")
#pragma pop_macro("VectorSelect")
#pragma pop_macro("VectorBitwiseOr")
#pragma pop_macro("VectorBitwiseAnd")
#pragma pop_macro("VectorBitwiseXor")
#pragma pop_macro("VectorMaskBits")
#pragma pop_macro("VectorCross")
#pragma pop_macro("VectorPow")
#pragma pop_macro("VectorReciprocalSqrt")
#pragma pop_macro("VectorReciprocal")
#pragma pop_macro("VectorReciprocalLen")
#pragma pop_macro("VectorReciprocalSqrtAccurate")
#pragma pop_macro("VectorReciprocalAccurate")
#pragma pop_macro("VectorNormalize")
#pragma pop_macro("VectorSet_W0")
#pragma pop_macro("VectorSet_W1")
#pragma pop_macro("VectorMin")
#pragma pop_macro("VectorMax")
#pragma pop_macro("VectorSwizzle")
#pragma pop_macro("VectorShuffle")
#pragma pop_macro("VectorMask_LT")
#pragma pop_macro("VectorMask_LE")
#pragma pop_macro("VectorMask_GT")
#pragma pop_macro("VectorMask_GE")
#pragma pop_macro("VectorMask_EQ")
#pragma pop_macro("VectorMask_NE")
#pragma pop_macro("VectorLoadByte4")
#pragma pop_macro("VectorLoadSignedByte4")
#pragma pop_macro("VectorStoreByte4")
#pragma pop_macro("VectorStoreSignedByte4")
#pragma pop_macro("VectorLoadURGB10A2N")
#pragma pop_macro("VectorStoreURGB10A2N")
#pragma pop_macro("VectorLoadURGBA16N")
#pragma pop_macro("VectorLoadSRGBA16N")
#pragma pop_macro("VectorStoreURGBA16N")
#pragma pop_macro("VectorResetFloatRegisters")
#pragma pop_macro("VectorGetControlRegister")
#pragma pop_macro("VectorIntAnd")
#pragma pop_macro("VectorIntOr")
#pragma pop_macro("VectorIntXor")
#pragma pop_macro("VectorIntAndNot")
#pragma pop_macro("VectorIntNot")
#pragma pop_macro("VectorIntCompareEQ")
#pragma pop_macro("VectorIntCompareNEQ")
#pragma pop_macro("VectorIntCompareGT")
#pragma pop_macro("VectorIntCompareLT")
#pragma pop_macro("VectorIntCompareGE")
#pragma pop_macro("VectorIntCompareLE")
#pragma pop_macro("VectorIntAdd")
#pragma pop_macro("VectorIntSubtract")
#pragma pop_macro("VectorIntNegate")
#pragma pop_macro("VectorIntSign")
#pragma pop_macro("VectorIntToFloat")
#pragma pop_macro("VectorFloatToInt")
#pragma pop_macro("VectorIntStore")
#pragma pop_macro("VectorIntLoad")
#pragma pop_macro("VectorIntStoreAligned")
#pragma pop_macro("VectorIntLoadAligned")
#pragma pop_macro("VectorIntLoad1")
#pragma pop_macro("VectorLoadByte4Reverse")
#pragma pop_macro("VectorPermute")
#pragma pop_macro("VectorSetComponent")
#pragma pop_macro("VectorIntMultiply")
#pragma pop_macro("VectorIntMin")
#pragma pop_macro("VectorIntMax")
#pragma pop_macro("VectorIntAbs")
#pragma pop_macro("SHUFFLEMASK")
#pragma pop_macro("VectorMergeVecXYZ_VecW")
#pragma pop_macro("VectorAnyGreaterThan")
#pragma pop_macro("INTEL_ORDER_VECTOR")
#pragma pop_macro("MEMPRO_TRACK_ALLOC")
#pragma pop_macro("MEMPRO_TRACK_FREE")
#pragma pop_macro("MEMPRO_ASSERT")
#pragma pop_macro("MEMPRO_DISABLE_WARNING")
#pragma pop_macro("MEMPRO_ALIGN_SUFFIX")
#pragma pop_macro("_DebugBreakAndPromptForRemote")
#pragma pop_macro("checkCode")
#pragma pop_macro("verify")
#pragma pop_macro("check")
#pragma pop_macro("UE_CHECK_IMPL")
#pragma pop_macro("verifyf")
#pragma pop_macro("checkf")
#pragma pop_macro("UE_CHECK_F_IMPL")
#pragma pop_macro("checkNoEntry")
#pragma pop_macro("checkNoReentry")
#pragma pop_macro("checkNoRecursion")
#pragma pop_macro("unimplemented")
#pragma pop_macro("checkSlow")
#pragma pop_macro("checkfSlow")
#pragma pop_macro("verifySlow")
#pragma pop_macro("UE_ENSURE_IMPL")
#pragma pop_macro("ensure")
#pragma pop_macro("ensureMsgf")
#pragma pop_macro("ensureAlways")
#pragma pop_macro("ensureAlwaysMsgf")
#pragma pop_macro("GET_ENUMERATOR_NAME_CHECKED")
#pragma pop_macro("GET_MEMBER_NAME_CHECKED")
#pragma pop_macro("GET_MEMBER_NAME_STRING_CHECKED")
#pragma pop_macro("GET_FUNCTION_NAME_CHECKED")
#pragma pop_macro("GET_FUNCTION_NAME_STRING_CHECKED")
#pragma pop_macro("LowLevelFatalError")
#pragma pop_macro("TestTrueExpr")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND_ONE_PARAMETER")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND_TWO_PARAMETER")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND_THREE_PARAMETER")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND_FOUR_PARAMETER")
#pragma pop_macro("DEFINE_LATENT_AUTOMATION_COMMAND_FIVE_PARAMETER")
#pragma pop_macro("DEFINE_EXPORTED_LATENT_AUTOMATION_COMMAND")
#pragma pop_macro("DEFINE_EXPORTED_LATENT_AUTOMATION_COMMAND_ONE_PARAMETER")
#pragma pop_macro("DEFINE_ENGINE_LATENT_AUTOMATION_COMMAND")
#pragma pop_macro("DEFINE_ENGINE_LATENT_AUTOMATION_COMMAND_ONE_PARAMETER")
#pragma pop_macro("ADD_LATENT_AUTOMATION_COMMAND")
#pragma pop_macro("START_NETWORK_AUTOMATION_COMMAND")
#pragma pop_macro("END_NETWORK_AUTOMATION_COMMAND")
#pragma pop_macro("IMPLEMENT_SIMPLE_AUTOMATION_TEST_PRIVATE")
#pragma pop_macro("IMPLEMENT_COMPLEX_AUTOMATION_TEST_PRIVATE")
#pragma pop_macro("IMPLEMENT_NETWORKED_AUTOMATION_TEST_PRIVATE")
#pragma pop_macro("IMPLEMENT_BDD_AUTOMATION_TEST_PRIVATE")
#pragma pop_macro("DEFINE_SPEC_PRIVATE")
#pragma pop_macro("BEGIN_DEFINE_SPEC_PRIVATE")
#pragma pop_macro("IMPLEMENT_SIMPLE_AUTOMATION_TEST")
#pragma pop_macro("IMPLEMENT_COMPLEX_AUTOMATION_TEST")
#pragma pop_macro("IMPLEMENT_COMPLEX_AUTOMATION_CLASS")
#pragma pop_macro("IMPLEMENT_NETWORKED_AUTOMATION_TEST")
#pragma pop_macro("IMPLEMENT_CUSTOM_SIMPLE_AUTOMATION_TEST")
#pragma pop_macro("IMPLEMENT_CUSTOM_COMPLEX_AUTOMATION_TEST")
#pragma pop_macro("IMPLEMENT_BDD_AUTOMATION_TEST")
#pragma pop_macro("DEFINE_SPEC")
#pragma pop_macro("BEGIN_DEFINE_SPEC")
#pragma pop_macro("END_DEFINE_SPEC")
#pragma pop_macro("BEGIN_CUSTOM_COMPLEX_AUTOMATION_TEST")
#pragma pop_macro("END_CUSTOM_COMPLEX_AUTOMATION_TEST")
#pragma pop_macro("UTEST_EQUAL")
#pragma pop_macro("UTEST_EQUAL_TOLERANCE")
#pragma pop_macro("UTEST_EQUAL_INSENSITIVE")
#pragma pop_macro("UTEST_NOT_EQUAL")
#pragma pop_macro("UTEST_SAME")
#pragma pop_macro("UTEST_NOT_SAME")
#pragma pop_macro("UTEST_TRUE")
#pragma pop_macro("UTEST_FALSE")
#pragma pop_macro("UTEST_VALID")
#pragma pop_macro("UTEST_INVALID")
#pragma pop_macro("UTEST_NULL")
#pragma pop_macro("UTEST_NOT_NULL")
#pragma pop_macro("BYTESWAP_ORDER16_unsigned")
#pragma pop_macro("BYTESWAP_ORDER32_unsigned")
#pragma pop_macro("UE_BYTESWAP_INTRINSIC_PRIVATE_16")
#pragma pop_macro("UE_BYTESWAP_INTRINSIC_PRIVATE_32")
#pragma pop_macro("UE_BYTESWAP_INTRINSIC_PRIVATE_64")
#pragma pop_macro("INTEL_ORDER16")
#pragma pop_macro("INTEL_ORDER32")
#pragma pop_macro("INTEL_ORDERF")
#pragma pop_macro("INTEL_ORDER64")
#pragma pop_macro("INTEL_ORDER_TCHARARRAY")
#pragma pop_macro("NETWORK_ORDER16")
#pragma pop_macro("NETWORK_ORDER32")
#pragma pop_macro("NETWORK_ORDERF")
#pragma pop_macro("NETWORK_ORDER64")
#pragma pop_macro("NETWORK_ORDER_TCHARARRAY")
#pragma pop_macro("LITERAL")
#pragma pop_macro("WhitelistCommandLines")
#pragma pop_macro("STUBBED")
#pragma pop_macro("CLOCK_CYCLES")
#pragma pop_macro("UNCLOCK_CYCLES")
#pragma pop_macro("RETURN_VAL_IF_EXIT_REQUESTED")
#pragma pop_macro("PURE_VIRTUAL")
#pragma pop_macro("WARNING_LOCATION")
#pragma pop_macro("PUSH_MACRO")
#pragma pop_macro("POP_MACRO")
#pragma pop_macro("ANONYMOUS_VARIABLE")
#pragma pop_macro("UE_DEPRECATED")
#pragma pop_macro("UE_DEPRECATED_FORGAME")
#pragma pop_macro("UE_STATIC_DEPRECATE")
#pragma pop_macro("UE_PTRDIFF_TO_INT32")
#pragma pop_macro("UE_PTRDIFF_TO_UINT32")
#pragma pop_macro("UE_NONCOPYABLE")
#pragma pop_macro("UE_GREATER_SORT")
#pragma pop_macro("UE_VERSION_NEWER_THAN")
#pragma pop_macro("UE_VERSION_OLDER_THAN")
#pragma pop_macro("ENUM_CLASS_FLAGS")
#pragma pop_macro("FRIEND_ENUM_CLASS_FLAGS")
#pragma pop_macro("ENUM_RANGE_BY_COUNT")
#pragma pop_macro("ENUM_RANGE_BY_FIRST_AND_LAST")
#pragma pop_macro("ENUM_RANGE_BY_VALUES")
#pragma pop_macro("DEFINE_EXPRESSION_NODE_TYPE")
#pragma pop_macro("MONOLITHIC_HEADER_BOILERPLATE")
#pragma pop_macro("SET_WARN_COLOR")
#pragma pop_macro("SET_WARN_COLOR_AND_BACKGROUND")
#pragma pop_macro("CLEAR_WARN_COLOR")
#pragma pop_macro("LogRuntimeError")
#pragma pop_macro("LogRuntimeWarning")
#pragma pop_macro("ensureAsRuntimeWarning")
#pragma pop_macro("UE_STATIC_ASSERT_COMPLETE_TYPE")
#pragma pop_macro("DEFINE_TEXT_EXPRESSION_OPERATOR_NODE")
#pragma pop_macro("SCOPE_TIME_GUARD")
#pragma pop_macro("SCOPE_TIME_GUARD_MS")
#pragma pop_macro("SCOPE_TIME_GUARD_NAMED")
#pragma pop_macro("SCOPE_TIME_GUARD_NAMED_MS")
#pragma pop_macro("SCOPE_TIME_GUARD_DELEGATE")
#pragma pop_macro("SCOPE_TIME_GUARD_DELEGATE_MS")
#pragma pop_macro("ENABLE_TIME_GUARDS")
#pragma pop_macro("CLEAR_TIME_GUARDS")
#pragma pop_macro("LIGHTWEIGHT_TIME_GUARD_BEGIN")
#pragma pop_macro("LIGHTWEIGHT_TIME_GUARD_END")
#pragma pop_macro("VARARG_DECL")
#pragma pop_macro("VARARG_BODY")
#pragma pop_macro("GET_VARARGS")
#pragma pop_macro("GET_VARARGS_WIDE")
#pragma pop_macro("GET_VARARGS_ANSI")
#pragma pop_macro("GET_VARARGS_RESULT")
#pragma pop_macro("GET_VARARGS_RESULT_WIDE")
#pragma pop_macro("GET_VARARGS_RESULT_ANSI")
#pragma pop_macro("VARARG_EXTRA")
#pragma pop_macro("IMPLEMENT_MODULE")
#pragma pop_macro("IMPLEMENT_GAME_MODULE")
#pragma pop_macro("IMPLEMENT_FOREIGN_ENGINE_DIR")
#pragma pop_macro("IMPLEMENT_LIVE_CODING_ENGINE_DIR")
#pragma pop_macro("IMPLEMENT_LIVE_CODING_PROJECT")
#pragma pop_macro("UE_LIST_ARGUMENT")
#pragma pop_macro("UE_REGISTER_SIGNING_KEY")
#pragma pop_macro("UE_REGISTER_ENCRYPTION_KEY")
#pragma pop_macro("IMPLEMENT_TARGET_NAME_REGISTRATION")
#pragma pop_macro("IMPLEMENT_APPLICATION")
#pragma pop_macro("IMPLEMENT_PRIMARY_GAME_MODULE")
#pragma pop_macro("PER_MODULE_BOILERPLATE_ANYLINK")
#pragma pop_macro("SCOPED_ABTEST")
#pragma pop_macro("SCOPED_ABTEST_DOFIRSTTEST")
#pragma pop_macro("COOK_STAT")
#pragma pop_macro("__TRACE_DECLARE_INLINE_COUNTER")
#pragma pop_macro("TRACE_INT_VALUE")
#pragma pop_macro("TRACE_FLOAT_VALUE")
#pragma pop_macro("TRACE_MEMORY_VALUE")
#pragma pop_macro("TRACE_DECLARE_INT_COUNTER")
#pragma pop_macro("TRACE_DECLARE_INT_COUNTER_EXTERN")
#pragma pop_macro("TRACE_DECLARE_FLOAT_COUNTER")
#pragma pop_macro("TRACE_DECLARE_FLOAT_COUNTER_EXTERN")
#pragma pop_macro("TRACE_DECLARE_MEMORY_COUNTER")
#pragma pop_macro("TRACE_DECLARE_MEMORY_COUNTER_EXTERN")
#pragma pop_macro("TRACE_COUNTER_SET")
#pragma pop_macro("TRACE_COUNTER_ADD")
#pragma pop_macro("TRACE_COUNTER_SUBTRACT")
#pragma pop_macro("TRACE_COUNTER_INCREMENT")
#pragma pop_macro("TRACE_COUNTER_DECREMENT")
#pragma pop_macro("TRACE_CPUPROFILER_SHUTDOWN")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE_ON_CHANNEL_STR")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE_ON_CHANNEL")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE_STR")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE_TEXT_ON_CHANNEL")
#pragma pop_macro("TRACE_CPUPROFILER_EVENT_SCOPE_TEXT")
#pragma pop_macro("CSV_CATEGORY_INDEX")
#pragma pop_macro("CSV_STAT_FNAME")
#pragma pop_macro("CSV_SCOPED_TIMING_STAT")
#pragma pop_macro("CSV_SCOPED_TIMING_STAT_GLOBAL")
#pragma pop_macro("CSV_SCOPED_TIMING_STAT_EXCLUSIVE")
#pragma pop_macro("CSV_SCOPED_TIMING_STAT_EXCLUSIVE_CONDITIONAL")
#pragma pop_macro("CSV_SCOPED_WAIT_CONDITIONAL")
#pragma pop_macro("CSV_SCOPED_SET_WAIT_STAT")
#pragma pop_macro("CSV_SCOPED_SET_WAIT_STAT_IGNORE")
#pragma pop_macro("CSV_CUSTOM_STAT")
#pragma pop_macro("CSV_CUSTOM_STAT_GLOBAL")
#pragma pop_macro("CSV_DEFINE_STAT")
#pragma pop_macro("CSV_DEFINE_STAT_GLOBAL")
#pragma pop_macro("CSV_DECLARE_STAT_EXTERN")
#pragma pop_macro("CSV_CUSTOM_STAT_DEFINED")
#pragma pop_macro("CSV_DEFINE_CATEGORY")
#pragma pop_macro("CSV_DECLARE_CATEGORY_EXTERN")
#pragma pop_macro("CSV_DEFINE_CATEGORY_MODULE")
#pragma pop_macro("CSV_DECLARE_CATEGORY_MODULE_EXTERN")
#pragma pop_macro("CSV_EVENT")
#pragma pop_macro("CSV_EVENT_GLOBAL")
#pragma pop_macro("CSV_METADATA")
#pragma pop_macro("TRACE_CSV_PROFILER_REGISTER_CATEGORY")
#pragma pop_macro("TRACE_CSV_PROFILER_INLINE_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_INLINE_STAT_EXCLUSIVE")
#pragma pop_macro("TRACE_CSV_PROFILER_DECLARED_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_BEGIN_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_END_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_BEGIN_EXCLUSIVE_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_END_EXCLUSIVE_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_CUSTOM_STAT")
#pragma pop_macro("TRACE_CSV_PROFILER_EVENT")
#pragma pop_macro("TRACE_CSV_PROFILER_BEGIN_CAPTURE")
#pragma pop_macro("TRACE_CSV_PROFILER_END_CAPTURE")
#pragma pop_macro("TRACE_CSV_PROFILER_METADATA")
#pragma pop_macro("SCOPE_PROFILER_INCLUDER")
#pragma pop_macro("SCOPE_PROFILER_EXCLUDER")
#pragma pop_macro("COUNT_INSTANCES")
#pragma pop_macro("COUNT_INSTANCES_AND_LOG")
#pragma pop_macro("ACCUM_LOADTIME")
#pragma pop_macro("SCOPED_ACCUM_LOADTIME")
#pragma pop_macro("SCOPED_LOADTIMER_TEXT")
#pragma pop_macro("SCOPED_LOADTIMER")
#pragma pop_macro("SCOPED_LOADTIMER_CNT")
#pragma pop_macro("ADD_CUSTOM_LOADTIMER_META")
#pragma pop_macro("SCOPED_CUSTOM_LOADTIMER")
#pragma pop_macro("SCOPED_ACCUM_LOADTIME_STAT")
#pragma pop_macro("ACCUM_LOADTIMECOUNT_STAT")
#pragma pop_macro("TRACE_BOOKMARK")
#pragma pop_macro("TRACE_BEGIN_FRAME")
#pragma pop_macro("TRACE_END_FRAME")
#pragma pop_macro("TRACE_PLATFORMFILE_BEGIN_OPEN")
#pragma pop_macro("TRACE_PLATFORMFILE_END_OPEN")
#pragma pop_macro("TRACE_PLATFORMFILE_FAIL_OPEN")
#pragma pop_macro("TRACE_PLATFORMFILE_BEGIN_CLOSE")
#pragma pop_macro("TRACE_PLATFORMFILE_END_CLOSE")
#pragma pop_macro("TRACE_PLATFORMFILE_FAIL_CLOSE")
#pragma pop_macro("TRACE_PLATFORMFILE_BEGIN_READ")
#pragma pop_macro("TRACE_PLATFORMFILE_END_READ")
#pragma pop_macro("TRACE_PLATFORMFILE_BEGIN_WRITE")
#pragma pop_macro("TRACE_PLATFORMFILE_END_WRITE")
#pragma pop_macro("MALLOC_PROFILER")
#pragma pop_macro("FArchive_Serialize_BitfieldBool")
#pragma pop_macro("TRACE_LOADTIME_REQUEST_GROUP_SCOPE")
#pragma pop_macro("UE_STATIC_ONLY")
#pragma pop_macro("UE_DECLARE_INTERNAL_LINK_BASE")
#pragma pop_macro("UE_DECLARE_INTERNAL_LINK_SPECIALIZATION")
#pragma pop_macro("INTERNAL_LAYOUT_FIELD")
#pragma pop_macro("INTERNAL_LAYOUT_FIELD_WITH_WRITER")
#pragma pop_macro("INTERNAL_LAYOUT_WRITE_MEMORY_IMAGE")
#pragma pop_macro("INTERNAL_LAYOUT_TOSTRING")
#pragma pop_macro("LAYOUT_FIELD")
#pragma pop_macro("LAYOUT_MUTABLE_FIELD")
#pragma pop_macro("LAYOUT_FIELD_INITIALIZED")
#pragma pop_macro("LAYOUT_MUTABLE_FIELD_INITIALIZED")
#pragma pop_macro("LAYOUT_ARRAY")
#pragma pop_macro("LAYOUT_MUTABLE_BITFIELD")
#pragma pop_macro("LAYOUT_BITFIELD")
#pragma pop_macro("LAYOUT_FIELD_WITH_WRITER")
#pragma pop_macro("LAYOUT_MUTABLE_FIELD_WITH_WRITER")
#pragma pop_macro("LAYOUT_WRITE_MEMORY_IMAGE")
#pragma pop_macro("LAYOUT_TOSTRING")
#pragma pop_macro("LAYOUT_FIELD_EDITORONLY")
#pragma pop_macro("LAYOUT_ARRAY_EDITORONLY")
#pragma pop_macro("LAYOUT_BITFIELD_EDITORONLY")
#pragma pop_macro("LAYOUT_FIELD_RAYTRACING")
#pragma pop_macro("LAYOUT_FIELD_INITIALIZED_RAYTRACING")
#pragma pop_macro("LAYOUT_ARRAY_RAYTRACING")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_PREFIX_NonVirtual")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_PREFIX_Virtual")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_PREFIX_Abstract")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_PREFIX")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_SUFFIX")
#pragma pop_macro("INTERNAL_LAYOUT_INTERFACE_INLINE_IMPL")
#pragma pop_macro("INTERNAL_DECLARE_TYPE_LAYOUT_COMMON")
#pragma pop_macro("INTERNAL_DECLARE_INLINE_TYPE_LAYOUT")
#pragma pop_macro("INTERNAL_DECLARE_TYPE_LAYOUT")
#pragma pop_macro("INTERNAL_DECLARE_LAYOUT_BASE")
#pragma pop_macro("INTERNAL_DECLARE_LAYOUT_EXPLICIT_BASES")
#pragma pop_macro("DECLARE_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_INLINE_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_EXPORTED_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_TYPE_LAYOUT_EXPLICIT_BASES")
#pragma pop_macro("DECLARE_INLINE_TYPE_LAYOUT_EXPLICIT_BASES")
#pragma pop_macro("DECLARE_EXPORTED_TYPE_LAYOUT_EXPLICIT_BASES")
#pragma pop_macro("INTERNAL_IMPLEMENT_TYPE_LAYOUT_COMMON")
#pragma pop_macro("INTERNAL_REGISTER_TYPE_LAYOUT")
#pragma pop_macro("IMPLEMENT_UNREGISTERED_TEMPLATE_TYPE_LAYOUT")
#pragma pop_macro("IMPLEMENT_TEMPLATE_TYPE_LAYOUT")
#pragma pop_macro("IMPLEMENT_TYPE_LAYOUT")
#pragma pop_macro("IMPLEMENT_ABSTRACT_TYPE_LAYOUT")
#pragma pop_macro("REGISTER_INLINE_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_TEMPLATE_INTRINSIC_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_EXPORTED_TEMPLATE_INTRINSIC_TYPE_LAYOUT")
#pragma pop_macro("IMPLEMENT_EXPORTED_INTRINSIC_TYPE_LAYOUT")
#pragma pop_macro("DECLARE_INTRINSIC_TYPE_LAYOUT")
#pragma pop_macro("ALIAS_TEMPLATE_TYPE_LAYOUT")
#pragma pop_macro("ALIAS_TYPE_LAYOUT")
#pragma pop_macro("SA_VALUE")
#pragma pop_macro("SA_ATTRIBUTE")
#pragma pop_macro("SA_OPTIONAL_ATTRIBUTE")
#pragma pop_macro("SA_FIELD_NAME")
#pragma pop_macro("STAT")
#pragma pop_macro("ANSI_TO_PROFILING")
#pragma pop_macro("DECLARE_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("QUICK_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("SCOPE_CYCLE_COUNTER")
#pragma pop_macro("CONDITIONAL_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("RETURN_QUICK_DECLARE_CYCLE_STAT")
#pragma pop_macro("GET_STATID")
#pragma pop_macro("SCOPE_SECONDS_ACCUMULATOR")
#pragma pop_macro("SCOPE_MS_ACCUMULATOR")
#pragma pop_macro("DEFINE_STAT")
#pragma pop_macro("QUICK_USE_CYCLE_STAT")
#pragma pop_macro("DECLARE_CYCLE_STAT")
#pragma pop_macro("DECLARE_FLOAT_COUNTER_STAT")
#pragma pop_macro("DECLARE_DWORD_COUNTER_STAT")
#pragma pop_macro("DECLARE_FLOAT_ACCUMULATOR_STAT")
#pragma pop_macro("DECLARE_DWORD_ACCUMULATOR_STAT")
#pragma pop_macro("DECLARE_FNAME_STAT")
#pragma pop_macro("DECLARE_PTR_STAT")
#pragma pop_macro("DECLARE_MEMORY_STAT")
#pragma pop_macro("DECLARE_MEMORY_STAT_POOL")
#pragma pop_macro("DECLARE_CYCLE_STAT_EXTERN")
#pragma pop_macro("DECLARE_FLOAT_COUNTER_STAT_EXTERN")
#pragma pop_macro("DECLARE_DWORD_COUNTER_STAT_EXTERN")
#pragma pop_macro("DECLARE_FLOAT_ACCUMULATOR_STAT_EXTERN")
#pragma pop_macro("DECLARE_DWORD_ACCUMULATOR_STAT_EXTERN")
#pragma pop_macro("DECLARE_FNAME_STAT_EXTERN")
#pragma pop_macro("DECLARE_PTR_STAT_EXTERN")
#pragma pop_macro("DECLARE_MEMORY_STAT_EXTERN")
#pragma pop_macro("DECLARE_MEMORY_STAT_POOL_EXTERN")
#pragma pop_macro("DECLARE_STATS_GROUP")
#pragma pop_macro("DECLARE_STATS_GROUP_VERBOSE")
#pragma pop_macro("DECLARE_STATS_GROUP_MAYBE_COMPILED_OUT")
#pragma pop_macro("SET_CYCLE_COUNTER")
#pragma pop_macro("INC_DWORD_STAT")
#pragma pop_macro("INC_FLOAT_STAT_BY")
#pragma pop_macro("INC_DWORD_STAT_BY")
#pragma pop_macro("INC_DWORD_STAT_FNAME_BY")
#pragma pop_macro("INC_MEMORY_STAT_BY")
#pragma pop_macro("DEC_DWORD_STAT")
#pragma pop_macro("DEC_FLOAT_STAT_BY")
#pragma pop_macro("DEC_DWORD_STAT_BY")
#pragma pop_macro("DEC_DWORD_STAT_FNAME_BY")
#pragma pop_macro("DEC_MEMORY_STAT_BY")
#pragma pop_macro("SET_MEMORY_STAT")
#pragma pop_macro("SET_DWORD_STAT")
#pragma pop_macro("SET_FLOAT_STAT")
#pragma pop_macro("STAT_ADD_CUSTOMMESSAGE_NAME")
#pragma pop_macro("STAT_ADD_CUSTOMMESSAGE_PTR")
#pragma pop_macro("SET_CYCLE_COUNTER_FName")
#pragma pop_macro("INC_DWORD_STAT_FName")
#pragma pop_macro("INC_FLOAT_STAT_BY_FName")
#pragma pop_macro("INC_DWORD_STAT_BY_FName")
#pragma pop_macro("INC_MEMORY_STAT_BY_FName")
#pragma pop_macro("DEC_DWORD_STAT_FName")
#pragma pop_macro("DEC_FLOAT_STAT_BY_FName")
#pragma pop_macro("DEC_DWORD_STAT_BY_FName")
#pragma pop_macro("DEC_MEMORY_STAT_BY_FName")
#pragma pop_macro("SET_MEMORY_STAT_FName")
#pragma pop_macro("SET_DWORD_STAT_FName")
#pragma pop_macro("SET_FLOAT_STAT_FName")
#pragma pop_macro("GET_STATFNAME")
#pragma pop_macro("GET_STATDESCRIPTION")
#pragma pop_macro("DECLARE_STAT_GROUP")
#pragma pop_macro("DECLARE_STAT")
#pragma pop_macro("GET_STATISEVERYFRAME")
#pragma pop_macro("STAT_GROUP_TO_FStatGroup")
#pragma pop_macro("DECLARE_STATS_GROUP_SORTBYNAME")
#pragma pop_macro("checkStats")
#pragma pop_macro("DECLARE_SCOPE_HIERARCHICAL_COUNTER")
#pragma pop_macro("DECLARE_SCOPE_HIERARCHICAL_COUNTER_FUNC")
#pragma pop_macro("SCOPE_SECONDS_COUNTER_BASE")
#pragma pop_macro("SCOPE_SECONDS_COUNTER_RECURSION_SAFE_BASE")
#pragma pop_macro("SCOPE_SECONDS_COUNTER")
#pragma pop_macro("SCOPE_SECONDS_COUNTER_RECURSION_SAFE")
#pragma pop_macro("SCOPE_LOG_TIME")
#pragma pop_macro("SCOPE_LOG_TIME_IN_SECONDS")
#pragma pop_macro("SCOPE_LOG_TIME_FUNC")
#pragma pop_macro("SCOPE_LOG_TIME_FUNC_WITH_GLOBAL")
#pragma pop_macro("CONDITIONAL_SCOPE_LOG_TIME")
#pragma pop_macro("CONDITIONAL_SCOPE_LOG_TIME_IN_SECONDS")
#pragma pop_macro("CONDITIONAL_SCOPE_LOG_TIME_FUNC")
#pragma pop_macro("CONDITIONAL_SCOPE_LOG_TIME_FUNC_WITH_GLOBAL")
#pragma pop_macro("TRACE_STAT_INCREMENT")
#pragma pop_macro("TRACE_STAT_DECREMENT")
#pragma pop_macro("TRACE_STAT_ADD")
#pragma pop_macro("TRACE_STAT_SET")
#pragma pop_macro("ALIGNOF")
#pragma pop_macro("ARE_TYPES_EQUAL")
#pragma pop_macro("UE_PROJECTION")
#pragma pop_macro("UE_PROJECTION_MEMBER")
#pragma pop_macro("PROJECTION")
#pragma pop_macro("PROJECTION_MEMBER")
#pragma pop_macro("UE_TSHAREDPTR_STATIC_ASSERT_VALID_MODE")
#pragma pop_macro("IMPLEMENT_ALIGNED_STORAGE")
#pragma pop_macro("UE_ARRAY_COUNT")
#pragma pop_macro("ARRAY_COUNT")
#pragma pop_macro("STRUCT_OFFSET")
#pragma pop_macro("VTABLE_OFFSET")
#pragma pop_macro("FGuardValue_Bitfield")
#pragma pop_macro("TEMPLATE_PARAMETERS2")
#pragma pop_macro("HAS_TRIVIAL_CONSTRUCTOR")
#pragma pop_macro("IS_POD")
#pragma pop_macro("IS_EMPTY")
#pragma pop_macro("Expose_TFormatSpecifier")
#pragma pop_macro("Expose_TNameOf")
#pragma pop_macro("GENERATE_MEMBER_FUNCTION_CHECK")
#pragma pop_macro("NAME_INTERNAL_TO_EXTERNAL")
#pragma pop_macro("NAME_EXTERNAL_TO_INTERNAL")
#pragma pop_macro("PREPROCESSOR_ENUM_PROTECT")
#pragma pop_macro("DEFERRED_DEPENDENCY_CHECK")
#pragma pop_macro("UE_ASYNC_PACKAGE_DEBUG")
#pragma pop_macro("UE_ASYNC_UPACKAGE_DEBUG")
#pragma pop_macro("UE_ASYNC_PACKAGE_LOG")
#pragma pop_macro("UE_ASYNC_PACKAGE_CLOG")
#pragma pop_macro("UE_ASYNC_PACKAGE_LOG_VERBOSE")
#pragma pop_macro("UE_ASYNC_PACKAGE_CLOG_VERBOSE")
#pragma pop_macro("CHECK_IOSTATUS")
#pragma pop_macro("TRACE_LOADTIME_START_ASYNC_LOADING")
#pragma pop_macro("TRACE_LOADTIME_SUSPEND_ASYNC_LOADING")
#pragma pop_macro("TRACE_LOADTIME_RESUME_ASYNC_LOADING")
#pragma pop_macro("TRACE_LOADTIME_BEGIN_REQUEST")
#pragma pop_macro("TRACE_LOADTIME_END_REQUEST")
#pragma pop_macro("TRACE_LOADTIME_NEW_ASYNC_PACKAGE")
#pragma pop_macro("TRACE_LOADTIME_BEGIN_LOAD_ASYNC_PACKAGE")
#pragma pop_macro("TRACE_LOADTIME_END_LOAD_ASYNC_PACKAGE")
#pragma pop_macro("TRACE_LOADTIME_DESTROY_ASYNC_PACKAGE")
#pragma pop_macro("TRACE_LOADTIME_PACKAGE_SUMMARY")
#pragma pop_macro("TRACE_LOADTIME_ASYNC_PACKAGE_REQUEST_ASSOCIATION")
#pragma pop_macro("TRACE_LOADTIME_ASYNC_PACKAGE_LINKER_ASSOCIATION")
#pragma pop_macro("TRACE_LOADTIME_ASYNC_PACKAGE_IMPORT_DEPENDENCY")
#pragma pop_macro("TRACE_LOADTIME_CREATE_EXPORT_SCOPE")
#pragma pop_macro("TRACE_LOADTIME_SERIALIZE_EXPORT_SCOPE")
#pragma pop_macro("TRACE_LOADTIME_POSTLOAD_EXPORT_SCOPE")
#pragma pop_macro("TRACE_LOADTIME_CLASS_INFO")
#pragma pop_macro("XFERSTRING")
#pragma pop_macro("XFERUNICODESTRING")
#pragma pop_macro("XFERTEXT")
#pragma pop_macro("DECLARE_CAST_BY_FLAG")
#pragma pop_macro("FIXUP_EXPR_OBJECT_POINTER")
#pragma pop_macro("CLASS_REDIRECT")
#pragma pop_macro("CLASS_REDIRECT_INSTANCES")
#pragma pop_macro("STRUCT_REDIRECT")
#pragma pop_macro("ENUM_REDIRECT")
#pragma pop_macro("PROPERTY_REDIRECT")
#pragma pop_macro("FUNCTION_REDIRECT")
#pragma pop_macro("PACKAGE_REDIRECT")
#pragma pop_macro("COMPARE_MEMBER")
#pragma pop_macro("DEFERRED_DEPENDENCY_ENSURE")
#pragma pop_macro("CompCheck")
#pragma pop_macro("DECLARE_OBJECT_FLAG")
#pragma pop_macro("SCOPED_SAVETIMER")
#pragma pop_macro("STORE_INSTRUCTION_NAME")
#pragma pop_macro("IMPLEMENT_FUNCTION")
#pragma pop_macro("IMPLEMENT_CAST_FUNCTION")
#pragma pop_macro("IMPLEMENT_VM_FUNCTION")
#pragma pop_macro("ADD_COOK_STAT")
#pragma pop_macro("STREAMINGTOKEN_PARAM")
#pragma pop_macro("DECLARE_CAST_BY_FLAG_FWD")
#pragma pop_macro("DECLARE_CAST_BY_FLAG_CAST")
#pragma pop_macro("IMPLEMENT_STRUCT")
#pragma pop_macro("NET_CHECKSUM_OR_END")
#pragma pop_macro("NET_CHECKSUM")
#pragma pop_macro("NET_CHECKSUM_CUSTOM")
#pragma pop_macro("NET_CHECKSUM_IGNORE")
#pragma pop_macro("DECLARE_FIELD")
#pragma pop_macro("IMPLEMENT_FIELD")
#pragma pop_macro("UPROPERTY")
#pragma pop_macro("UFUNCTION")
#pragma pop_macro("USTRUCT")
#pragma pop_macro("UMETA")
#pragma pop_macro("UPARAM")
#pragma pop_macro("UENUM")
#pragma pop_macro("UDELEGATE")
#pragma pop_macro("RIGVM_METHOD")
#pragma pop_macro("BODY_MACRO_COMBINE_INNER")
#pragma pop_macro("BODY_MACRO_COMBINE")
#pragma pop_macro("GENERATED_BODY_LEGACY")
#pragma pop_macro("GENERATED_BODY")
#pragma pop_macro("GENERATED_USTRUCT_BODY")
#pragma pop_macro("GENERATED_UCLASS_BODY")
#pragma pop_macro("GENERATED_UINTERFACE_BODY")
#pragma pop_macro("GENERATED_IINTERFACE_BODY")
#pragma pop_macro("UCLASS")
#pragma pop_macro("UINTERFACE")
#pragma pop_macro("DECLARE_FUNCTION")
#pragma pop_macro("DEFINE_FUNCTION")
#pragma pop_macro("RELAY_CONSTRUCTOR")
#pragma pop_macro("COMPILED_IN_FLAGS")
#pragma pop_macro("DECLARE_SERIALIZER")
#pragma pop_macro("IMPLEMENT_FARCHIVE_SERIALIZER")
#pragma pop_macro("IMPLEMENT_FSTRUCTUREDARCHIVE_SERIALIZER")
#pragma pop_macro("DECLARE_FARCHIVE_SERIALIZER")
#pragma pop_macro("DECLARE_FSTRUCTUREDARCHIVE_SERIALIZER")
#pragma pop_macro("DECLARE_CLASS")
#pragma pop_macro("DEFINE_FORBIDDEN_DEFAULT_CONSTRUCTOR_CALL")
#pragma pop_macro("DEFINE_DEFAULT_CONSTRUCTOR_CALL")
#pragma pop_macro("DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL")
#pragma pop_macro("DECLARE_VTABLE_PTR_HELPER_CTOR")
#pragma pop_macro("DEFINE_VTABLE_PTR_HELPER_CTOR")
#pragma pop_macro("DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER_DUMMY")
#pragma pop_macro("DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER")
#pragma pop_macro("DECLARE_CLASS_INTRINSIC_NO_CTOR")
#pragma pop_macro("DECLARE_CLASS_INTRINSIC")
#pragma pop_macro("DECLARE_CASTED_CLASS_INTRINSIC_WITH_API_NO_CTOR")
#pragma pop_macro("DECLARE_CASTED_CLASS_INTRINSIC_WITH_API")
#pragma pop_macro("DECLARE_CASTED_CLASS_INTRINSIC_NO_CTOR_NO_VTABLE_CTOR")
#pragma pop_macro("DECLARE_CASTED_CLASS_INTRINSIC_NO_CTOR")
#pragma pop_macro("DECLARE_CASTED_CLASS_INTRINSIC")
#pragma pop_macro("DECLARE_WITHIN_INTERNAL")
#pragma pop_macro("DECLARE_WITHIN")
#pragma pop_macro("DECLARE_WITHIN_UPACKAGE")
#pragma pop_macro("IMPLEMENT_CLASS")
#pragma pop_macro("IMPLEMENT_INTRINSIC_CLASS")
#pragma pop_macro("IMPLEMENT_CORE_INTRINSIC_CLASS")
#pragma pop_macro("IMPLEMENT_DYNAMIC_CLASS")
#pragma pop_macro("SCOPED_SCRIPT_NATIVE_TIMER")
#pragma pop_macro("ZERO_INIT")
#pragma pop_macro("PARAM_PASSED_BY_VAL")
#pragma pop_macro("PARAM_PASSED_BY_VAL_ZEROED")
#pragma pop_macro("PARAM_PASSED_BY_REF")
#pragma pop_macro("PARAM_PASSED_BY_REF_ZEROED")
#pragma pop_macro("P_GET_PROPERTY")
#pragma pop_macro("P_GET_PROPERTY_REF")
#pragma pop_macro("P_GET_UBOOL")
#pragma pop_macro("P_GET_UBOOL8")
#pragma pop_macro("P_GET_UBOOL16")
#pragma pop_macro("P_GET_UBOOL32")
#pragma pop_macro("P_GET_UBOOL64")
#pragma pop_macro("P_GET_UBOOL_REF")
#pragma pop_macro("P_GET_STRUCT")
#pragma pop_macro("P_GET_STRUCT_REF")
#pragma pop_macro("P_GET_OBJECT")
#pragma pop_macro("P_GET_OBJECT_REF")
#pragma pop_macro("P_GET_OBJECT_NO_PTR")
#pragma pop_macro("P_GET_OBJECT_REF_NO_PTR")
#pragma pop_macro("P_GET_TARRAY")
#pragma pop_macro("P_GET_TARRAY_REF")
#pragma pop_macro("P_GET_TMAP")
#pragma pop_macro("P_GET_TMAP_REF")
#pragma pop_macro("P_GET_TSET")
#pragma pop_macro("P_GET_TSET_REF")
#pragma pop_macro("P_GET_TINTERFACE")
#pragma pop_macro("P_GET_TINTERFACE_REF")
#pragma pop_macro("P_GET_SOFTOBJECT")
#pragma pop_macro("P_GET_SOFTOBJECT_REF")
#pragma pop_macro("P_GET_SOFTCLASS")
#pragma pop_macro("P_GET_SOFTCLASS_REF")
#pragma pop_macro("P_GET_ARRAY")
#pragma pop_macro("P_GET_ARRAY_REF")
#pragma pop_macro("P_GET_ENUM")
#pragma pop_macro("P_GET_ENUM_REF")
#pragma pop_macro("P_THIS_CAST")
#pragma pop_macro("XFER")
#pragma pop_macro("XFERNAME")
#pragma pop_macro("XFERPTR")
#pragma pop_macro("XFER_OBJECT_POINTER")
#pragma pop_macro("FUNC_DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_OneParam")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_TwoParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_ThreeParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_FourParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_FiveParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_SixParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_SevenParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_EightParams")
#pragma pop_macro("DECLARE_DYNAMIC_MULTICAST_SPARSE_DELEGATE_NineParams")
#pragma pop_macro("CPP_ARRAY_DIM")
#pragma pop_macro("SCOPE_CYCLE_UOBJECT")
#pragma pop_macro("UE_ASSET_LOG")
#pragma pop_macro("METADATA_PARAMS")
#pragma pop_macro("IF_WITH_EDITOR")
#pragma pop_macro("IF_WITH_EDITORONLY_DATA")
#pragma pop_macro("INCREASE_ALLOC_COUNTER")
#pragma pop_macro("DECREASE_ALLOC_COUNTER")
#pragma pop_macro("ContextRedirect")
#pragma pop_macro("ContextGPU0")
#pragma pop_macro("DECLARE_ISBOUNDSHADER")
#pragma pop_macro("VALIDATE_BOUND_SHADER")
#pragma pop_macro("COPY_SHADER")
#pragma pop_macro("EXT_SHADER")
#pragma pop_macro("PSO_IF_NOT_EQUAL_RETURN_FALSE")
#pragma pop_macro("PSO_IF_MEMCMP_FAILS_RETURN_FALSE")
#pragma pop_macro("PSO_IF_STRING_COMPARE_FAILS_RETURN_FALSE")
#pragma pop_macro("DEBUG_EXECUTE_COMMAND_LIST")
#pragma pop_macro("DEBUG_EXECUTE_COMMAND_CONTEXT")
#pragma pop_macro("DEBUG_RHI_EXECUTE_COMMAND_LIST")
#pragma pop_macro("EMBED_DXGI_ERROR_LIST")
#pragma pop_macro("CONDITIONAL_SET_SRVS")
#pragma pop_macro("CONDITIONAL_SET_CBVS")
#pragma pop_macro("CONDITIONAL_SET_SAMPLERS")
#pragma pop_macro("DECLARE_SHADER_TRAITS")
#pragma pop_macro("D3DERR")
#pragma pop_macro("MAKE_D3DHRESULT")
#pragma pop_macro("D3DFORMATCASE")
#pragma pop_macro("MERGE_EXT")
#pragma pop_macro("CASE_ERROR_NAME")
#pragma pop_macro("VERIFYD3D12RESULT_LAMBDA")
#pragma pop_macro("VERIFYD3D12RESULT_EX")
#pragma pop_macro("VERIFYD3D12RESULT")
#pragma pop_macro("VERIFYD3D12CREATETEXTURERESULT")
#pragma pop_macro("checkComRefCount")
#pragma pop_macro("UPDATE_BITFLAGS")
#pragma pop_macro("DS_ELEMENT_TYPE")
#pragma pop_macro("SerialAny_Case")
#pragma pop_macro("DECLARE_SCOPE_HIERARCHICAL_COUNTER_ANIMNODE")
#pragma pop_macro("GETCURVE_REPORTERROR")
#pragma pop_macro("GETCURVE_REPORTERROR_WITHPATHNAME")
#pragma pop_macro("GETROW_REPORTERROR")
#pragma pop_macro("GETROWOBJECT_REPORTERROR")
#pragma pop_macro("DECLARE_SCOPED_DELEGATE")
#pragma pop_macro("STREAMABLERENDERASSET_NODEFAULT")
#pragma pop_macro("HIDE_ACTOR_TRANSFORM_FUNCTIONS")
#pragma pop_macro("DEPRECATED_CHARACTER_MOVEMENT_RPC")
#pragma pop_macro("PARTICLE_PERF_STAT_INSTANCE_COUNT")
#pragma pop_macro("PARTICLE_PERF_STAT_CYCLES")
#pragma pop_macro("EFFECT_SETTINGS_NAME2")
#pragma pop_macro("EFFECT_SETTINGS_NAME1")
#pragma pop_macro("EFFECT_SETTINGS_NAME")
#pragma pop_macro("EFFECT_PRESET_NAME2")
#pragma pop_macro("EFFECT_PRESET_NAME1")
#pragma pop_macro("EFFECT_PRESET_NAME")
#pragma pop_macro("GET_EFFECT_SETTINGS")
#pragma pop_macro("EFFECT_PRESET_METHODS")
#pragma pop_macro("DECLARE_SOUNDNODE_ELEMENT")
#pragma pop_macro("DECLARE_SOUNDNODE_ELEMENT_PTR")
#pragma pop_macro("DEBUG_CALLSPACE")
#pragma pop_macro("UE_MAKEFOURCC")
#pragma pop_macro("UE_mmioFOURCC")
#pragma pop_macro("SAFE_TRACEINDEX_DECREASE")
#pragma pop_macro("DATATABLE_CHANGE_SCOPE")
#pragma pop_macro("CURVETABLE_CHANGE_SCOPE")
#pragma pop_macro("UE_DRAW_SERVER_DEBUG_ON_EACH_CLIENT")
#pragma pop_macro("IMPLEMENT_GPUSKINNING_VERTEX_FACTORY_TYPE_INTERNAL")
#pragma pop_macro("IMPLEMENT_GPUSKINNING_VERTEX_FACTORY_TYPE")
#pragma pop_macro("IMPLEMENT_GPUSKINNING_VERTEX_FACTORY_PARAMETER_TYPE")
#pragma pop_macro("STRUCTTRACK_GETNUMKEYFRAMES")
#pragma pop_macro("STRUCTTRACK_GETTIMERANGE")
#pragma pop_macro("STRUCTTRACK_GETTRACKENDTIME")
#pragma pop_macro("STRUCTTRACK_GETKEYFRAMETIME")
#pragma pop_macro("STRUCTTRACK_GETKEYFRAMEINDEX")
#pragma pop_macro("STRUCTTRACK_SETKEYFRAMETIME")
#pragma pop_macro("STRUCTTRACK_REMOVEKEYFRAME")
#pragma pop_macro("STRUCTTRACK_DUPLICATEKEYFRAME")
#pragma pop_macro("STRUCTTRACK_GETCLOSESTSNAPPOSITION")
#pragma pop_macro("DEBUG_REMOTEFUNCTION")
#pragma pop_macro("UpdatePerClientMinMaxAvg")
#pragma pop_macro("BUILD_NETEMULATION_CONSOLE_COMMAND")
#pragma pop_macro("SCOPE_LOCK_REF")
#pragma pop_macro("CHECK_REPL_EQUALITY")
#pragma pop_macro("CHECK_REPL_VALIDITY")
#pragma pop_macro("COMPOSE_NET_GUID")
#pragma pop_macro("ALLOC_NEW_NET_GUID")
#pragma pop_macro("PolyCheck")
#pragma pop_macro("CHECK_VIRTUALTEXTURE_USAGE")
#pragma pop_macro("WarnInvalidPhysicsOperations")
#pragma pop_macro("REPDATATYPE_SPECIALIZATION")
#pragma pop_macro("LERP_PP")
#pragma pop_macro("IF_PP")
#pragma pop_macro("IMPLEMENT_PLATFORM_INTERFACE_SINGLETON")
#pragma pop_macro("SHOWFLAG_ALWAYS_ACCESSIBLE")
#pragma pop_macro("SHOWFLAG_FIXED_IN_SHIPPING")
#pragma pop_macro("DISABLE_ENGINE_SHOWFLAG")
#pragma pop_macro("DEBUGBROKENCONSTRAINTUPDATE")
#pragma pop_macro("GENERATE_LOD_MODEL")
#pragma pop_macro("SERIALIZE_CURVE")
#pragma pop_macro("SERIALIZE_OPTION")
#pragma pop_macro("SKIP_OLD_OPTION")
#pragma pop_macro("DECLARETEXTUREGROUPSTAT")
#pragma pop_macro("ASSIGNTEXTUREGROUPSTATNAME")
#pragma pop_macro("TEXT_TO_ENUM")
#pragma pop_macro("TEXT_TO_MIPGENSETTINGS")
#pragma pop_macro("GROUPNAMES")
#pragma pop_macro("TIMER_TEST_TEXT")
#pragma pop_macro("SCOPE_TIME_TO_VAR")
#pragma pop_macro("CALCULATE_WEIGHTING")
#pragma pop_macro("DO_ANIMSTAT_PROCESSING")
#pragma pop_macro("IS_VALID_COLLISIONCHANNEL")
#pragma pop_macro("devCode")
#pragma pop_macro("SKY_DECLARE_BLUEPRINT_SETFUNCTION")
#pragma pop_macro("SKY_DECLARE_BLUEPRINT_SETFUNCTION_LINEARCOEFFICIENT")
#pragma pop_macro("CLOUD_DECLARE_BLUEPRINT_SETFUNCTION")
#pragma pop_macro("SETUPLODGROUP")
#pragma pop_macro("TEXT_TO_SHADINGMODEL")
#pragma pop_macro("TEXT_TO_BLENDMODE")
#pragma pop_macro("SWAP_REFERENCE_TO")
#pragma pop_macro("IF_INPUT_RETURN")
#pragma pop_macro("COMPILER_OR_LOG_ERROR")
#pragma pop_macro("DECLARE_MATERIALUNIFORMEXPRESSION_TYPE")
#pragma pop_macro("IMPLEMENT_MATERIALUNIFORMEXPRESSION_TYPE")
#pragma pop_macro("WORLD_TYPE_CASE")
#pragma pop_macro("STARTQUERYTIMER")
#pragma pop_macro("CAPTUREGEOMSWEEP")
#pragma pop_macro("CAPTURERAYCAST")
#pragma pop_macro("CAPTUREGEOMOVERLAP")
#pragma pop_macro("SET_DRIVE_PARAM")
#pragma pop_macro("SUBSTEPPING_WARNING")
#pragma pop_macro("ALLOCATE_VERTEX_DATA_TEMPLATE")
#pragma pop_macro("CHECK_SUCCESS")
#pragma pop_macro("CHECK_FAIL")
#pragma pop_macro("CHECK_NOT_NULL")
#pragma pop_macro("MAKE_565")
#pragma pop_macro("MAKE_8888")
#pragma pop_macro("UNORM16")
#pragma pop_macro("UNORM16_SRGB")
#pragma pop_macro("AC_UnalignedSwap")
#pragma pop_macro("ECC_TO_BITFIELD")
#pragma pop_macro("CRC_TO_BITFIELD")
#pragma pop_macro("SCENE_QUERY_STAT_ONLY")
#pragma pop_macro("SCENE_QUERY_STAT_NAME_ONLY")
#pragma pop_macro("SCENE_QUERY_STAT")
#pragma pop_macro("MAKEFOURCC")
#pragma pop_macro("DIST_GET_RANDOM_VALUE")
#pragma pop_macro("DEFINE_GAME_DELEGATE")
#pragma pop_macro("DEFINE_GAME_DELEGATE_TYPED")
#pragma pop_macro("SET_BONE_DATA")
#pragma pop_macro("DECLARE_HIT_PROXY_STATIC")
#pragma pop_macro("DECLARE_HIT_PROXY")
#pragma pop_macro("IMPLEMENT_HIT_PROXY_BASE")
#pragma pop_macro("IMPLEMENT_HIT_PROXY")
#pragma pop_macro("EVAL_CURVE")
#pragma pop_macro("FIND_POINT")
#pragma pop_macro("IMPLEMENT_MATERIAL_SHADER_TYPE")
#pragma pop_macro("TRACE_CLASS")
#pragma pop_macro("TRACE_OBJECT")
#pragma pop_macro("TRACE_OBJECT_EVENT")
#pragma pop_macro("UNCONDITIONAL_TRACE_OBJECT_EVENT")
#pragma pop_macro("TRACE_WORLD")
#pragma pop_macro("PARTICLE_INSTANCE_PREFETCH")
#pragma pop_macro("DECLARE_PARTICLE")
#pragma pop_macro("DECLARE_PARTICLE_CONST")
#pragma pop_macro("DECLARE_PARTICLE_PTR")
#pragma pop_macro("PARTICLE_ELEMENT")
#pragma pop_macro("TRAIL_EMITTER_CHECK_FLAG")
#pragma pop_macro("TRAIL_EMITTER_SET_FLAG")
#pragma pop_macro("TRAIL_EMITTER_GET_PREVNEXT")
#pragma pop_macro("TRAIL_EMITTER_SET_PREVNEXT")
#pragma pop_macro("TRAIL_EMITTER_IS_START")
#pragma pop_macro("TRAIL_EMITTER_SET_START")
#pragma pop_macro("TRAIL_EMITTER_IS_END")
#pragma pop_macro("TRAIL_EMITTER_SET_END")
#pragma pop_macro("TRAIL_EMITTER_IS_MIDDLE")
#pragma pop_macro("TRAIL_EMITTER_SET_MIDDLE")
#pragma pop_macro("TRAIL_EMITTER_IS_ONLY")
#pragma pop_macro("TRAIL_EMITTER_SET_ONLY")
#pragma pop_macro("TRAIL_EMITTER_IS_FORCEKILL")
#pragma pop_macro("TRAIL_EMITTER_SET_FORCEKILL")
#pragma pop_macro("TRAIL_EMITTER_IS_DEADTRAIL")
#pragma pop_macro("TRAIL_EMITTER_SET_DEADTRAIL")
#pragma pop_macro("TRAIL_EMITTER_IS_HEAD")
#pragma pop_macro("TRAIL_EMITTER_IS_HEADONLY")
#pragma pop_macro("TRAIL_EMITTER_GET_PREV")
#pragma pop_macro("TRAIL_EMITTER_SET_PREV")
#pragma pop_macro("TRAIL_EMITTER_GET_NEXT")
#pragma pop_macro("TRAIL_EMITTER_SET_NEXT")
#pragma pop_macro("BEAM2_TYPEDATA_SETFREQUENCY")
#pragma pop_macro("SRA_UPDATE_CALLBACK")
#pragma pop_macro("VIEW_UNIFORM_BUFFER_MEMBER")
#pragma pop_macro("VIEW_UNIFORM_BUFFER_MEMBER_EX")
#pragma pop_macro("VIEW_UNIFORM_BUFFER_MEMBER_ARRAY")
#pragma pop_macro("STEREO_LAYER_SHAPE_BOILERPLATE")
#pragma pop_macro("CAN_TRACE_OBJECT")
#pragma pop_macro("CANNOT_TRACE_OBJECT")
#pragma pop_macro("MARK_OBJECT_TRACEABLE")
#pragma pop_macro("SET_OBJECT_TRACEABLE")
#pragma pop_macro("GET_TRACE_OBJECT_VALUE")
#pragma pop_macro("DISABLE_ENGINE_ACTOR_TRACE_FILTERING")
#pragma pop_macro("DISABLE_ENGINE_WORLD_TRACE_FILTERING")
#pragma pop_macro("ANIM_MT_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("TRACE_ANIM_TICK_RECORD")
#pragma pop_macro("TRACE_SKELETAL_MESH")
#pragma pop_macro("TRACE_SKELETAL_MESH_COMPONENT")
#pragma pop_macro("TRACE_SKELETALMESH_FRAME")
#pragma pop_macro("TRACE_SCOPED_ANIM_GRAPH")
#pragma pop_macro("TRACE_SCOPED_ANIM_NODE")
#pragma pop_macro("TRACE_ANIM_NODE_VALUE")
#pragma pop_macro("TRACE_ANIM_SEQUENCE_PLAYER")
#pragma pop_macro("TRACE_ANIM_STATE_MACHINE_STATE")
#pragma pop_macro("TRACE_ANIM_NOTIFY")
#pragma pop_macro("TRACE_ANIM_SYNC_MARKER")
#pragma pop_macro("TRACE_ANIM_MONTAGE")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_ZEROPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_ONEPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_TWOPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_THREEPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_FOURPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_FIVEPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_SIXPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_SEVENPARAM")
#pragma pop_macro("DEFINE_CONTROL_CHANNEL_MESSAGE_EIGHTPARAM")
#pragma pop_macro("IMPLEMENT_CONTROL_CHANNEL_MESSAGE")
#pragma pop_macro("UE_LOG_PACKET_NOTIFY_WARNING")
#pragma pop_macro("GRANULAR_NETWORK_MEMORY_TRACKING_INIT")
#pragma pop_macro("GRANULAR_NETWORK_MEMORY_TRACKING_TRACK")
#pragma pop_macro("GRANULAR_NETWORK_MEMORY_TRACKING_CUSTOM_WORK")
#pragma pop_macro("NETWORK_PROFILER")
#pragma pop_macro("DOREPLIFETIME_WITH_PARAMS_FAST")
#pragma pop_macro("DOREPLIFETIME_WITH_PARAMS_FAST_STATIC_ARRAY")
#pragma pop_macro("DOREPLIFETIME_WITH_PARAMS")
#pragma pop_macro("DOREPLIFETIME")
#pragma pop_macro("DOREPLIFETIME_DIFFNAMES")
#pragma pop_macro("DOREPLIFETIME_CONDITION")
#pragma pop_macro("DOREPLIFETIME_CONDITION_NOTIFY")
#pragma pop_macro("DOREPLIFETIME_ACTIVE_OVERRIDE_FAST")
#pragma pop_macro("DOREPLIFETIME_ACTIVE_OVERRIDE_FAST_STATIC_ARRAY")
#pragma pop_macro("DOREPLIFETIME_ACTIVE_OVERRIDE")
#pragma pop_macro("DOREPLIFETIME_CHANGE_CONDITION")
#pragma pop_macro("DISABLE_REPLICATED_PROPERTY")
#pragma pop_macro("DISABLE_REPLICATED_PRIVATE_PROPERTY")
#pragma pop_macro("DISABLE_REPLICATED_PROPERTY_FAST")
#pragma pop_macro("DISABLE_REPLICATED_PROPERTY_FAST_STATIC_ARRAY")
#pragma pop_macro("DISABLE_ALL_CLASS_REPLICATED_PROPERTIES")
#pragma pop_macro("DISABLE_ALL_CLASS_REPLICATED_PROPERTIES_FAST")
#pragma pop_macro("RESET_REPLIFETIME_CONDITION")
#pragma pop_macro("RESET_REPLIFETIME")
#pragma pop_macro("RESET_REPLIFETIME_CONDITION_FAST")
#pragma pop_macro("RESET_REPLIFETIME_CONDITION_FAST_STATIC_ARRAY")
#pragma pop_macro("RESET_REPLIFETIME_FAST")
#pragma pop_macro("RESET_REPLIFETIME_FAST_STATIC_ARRAY")
#pragma pop_macro("RPC_VALIDATE")
#pragma pop_macro("REDIRECT_TO_VLOG")
#pragma pop_macro("REDIRECT_OBJECT_TO_VLOG")
#pragma pop_macro("CONNECT_WITH_VLOG")
#pragma pop_macro("CONNECT_OBJECT_WITH_VLOG")
#pragma pop_macro("UE_VLOG")
#pragma pop_macro("UE_CVLOG")
#pragma pop_macro("UE_VLOG_UELOG")
#pragma pop_macro("UE_CVLOG_UELOG")
#pragma pop_macro("UE_VLOG_SEGMENT")
#pragma pop_macro("UE_CVLOG_SEGMENT")
#pragma pop_macro("UE_VLOG_SEGMENT_THICK")
#pragma pop_macro("UE_CVLOG_SEGMENT_THICK")
#pragma pop_macro("UE_VLOG_LOCATION")
#pragma pop_macro("UE_CVLOG_LOCATION")
#pragma pop_macro("UE_VLOG_BOX")
#pragma pop_macro("UE_CVLOG_BOX")
#pragma pop_macro("UE_VLOG_OBOX")
#pragma pop_macro("UE_CVLOG_OBOX")
#pragma pop_macro("UE_VLOG_CONE")
#pragma pop_macro("UE_CVLOG_CONE")
#pragma pop_macro("UE_VLOG_CYLINDER")
#pragma pop_macro("UE_CVLOG_CYLINDER")
#pragma pop_macro("UE_VLOG_CAPSULE")
#pragma pop_macro("UE_CVLOG_CAPSULE")
#pragma pop_macro("UE_VLOG_HISTOGRAM")
#pragma pop_macro("UE_CVLOG_HISTOGRAM")
#pragma pop_macro("UE_VLOG_PULLEDCONVEX")
#pragma pop_macro("UE_CVLOG_PULLEDCONVEX")
#pragma pop_macro("UE_VLOG_MESH")
#pragma pop_macro("UE_CVLOG_MESH")
#pragma pop_macro("UE_VLOG_CONVEXPOLY")
#pragma pop_macro("UE_CVLOG_CONVEXPOLY")
#pragma pop_macro("UE_VLOG_ARROW")
#pragma pop_macro("UE_CVLOG_ARROW")
#pragma pop_macro("DECLARE_VLOG_EVENT")
#pragma pop_macro("DEFINE_VLOG_EVENT")
#pragma pop_macro("UE_VLOG_EVENTS")
#pragma pop_macro("UE_CVLOG_EVENTS")
#pragma pop_macro("UE_VLOG_EVENT_WITH_DATA")
#pragma pop_macro("UE_CVLOG_EVENT_WITH_DATA")
#pragma pop_macro("UE_IFVLOG")
#pragma pop_macro("TEXT_CONDITION")
#pragma pop_macro("COLLAPSED_LOGF")
#pragma pop_macro("DEFINE_ENUM_TO_STRING")
#pragma pop_macro("DECLARE_ENUM_TO_STRING")
#pragma pop_macro("EVOLUTION_TRAIT")
#pragma pop_macro("SCOPE_CYCLE_COUNTER_GJK")
#pragma pop_macro("MAX_CLAMP")
#pragma pop_macro("MIN_CLAMP")
#pragma pop_macro("RANGE_CLAMP")
#pragma pop_macro("CHAOS_CHECK")
#pragma pop_macro("CHAOS_ENSURE")
#pragma pop_macro("CHAOS_ENSURE_MSG")
#pragma pop_macro("CHAOS_LOG")
#pragma pop_macro("CHAOS_CLOG")
#pragma pop_macro("CHAOS_PERF_TEST")
#pragma pop_macro("CHAOS_SCOPED_TIMER")
#pragma pop_macro("PARTICLE_PROPERTY")
#pragma pop_macro("PROPERTY_TYPE")
#pragma pop_macro("SHAPE_PROPERTY")
#pragma pop_macro("PARTICLE_PROPERTY_CHECKED")
#pragma pop_macro("CONSTRAINT_JOINT_PROPERPETY_IMPL")
#pragma pop_macro("SCOPE_CYCLE_COUNTER_NAROWPHASE")
#pragma pop_macro("CHAOS_COLLISION_STAT")
#pragma pop_macro("CHAOS_COLLISION_STAT_DISABLED")
#pragma pop_macro("MANAGED_ARRAY_TYPE")
#pragma pop_macro("COPY_ON_WRITE_ATTRIBUTE")
#pragma pop_macro("SCOPE_LOG_GAMEPLAYTAGS")
#pragma pop_macro("PARSE_FLOAT")
#pragma pop_macro("PARSE_INT")
#pragma pop_macro("ADDKEYMAP")
#pragma pop_macro("MAP_OEM_VK_TO_SCAN")
#pragma pop_macro("JSON_SERIALIZE")
#pragma pop_macro("JSON_SERIALIZE_ARRAY")
#pragma pop_macro("JSON_SERIALIZE_MAP")
#pragma pop_macro("JSON_SERIALIZE_SIMPLECOPY")
#pragma pop_macro("JSON_SERIALIZE_MAP_SAFE")
#pragma pop_macro("JSON_SERIALIZE_SERIALIZABLE")
#pragma pop_macro("JSON_SERIALIZE_RAW_JSON_STRING")
#pragma pop_macro("JSON_SERIALIZE_ARRAY_SERIALIZABLE")
#pragma pop_macro("JSON_SERIALIZE_MAP_SERIALIZABLE")
#pragma pop_macro("JSON_SERIALIZE_OBJECT_SERIALIZABLE")
#pragma pop_macro("JSON_SERIALIZE_DATETIME_UNIX_TIMESTAMP")
#pragma pop_macro("JSON_SERIALIZE_DATETIME_UNIX_TIMESTAMP_MILLISECONDS")
#pragma pop_macro("JSON_SERIALIZE_ENUM")
#pragma pop_macro("HEIGHTDATA")
#pragma pop_macro("CHECK_JNI_METHOD")
#pragma pop_macro("VERSION_TEXT")
#pragma pop_macro("VERSION_STRINGIFY_2")
#pragma pop_macro("VERSION_STRINGIFY")
#pragma pop_macro("SAFE_CA_CALL")
#pragma pop_macro("CORE_AUDIO_ERR")
#pragma pop_macro("NEW_GLOBAL_PROPERTY")
#pragma pop_macro("NEW_OUTPUT_PROPERTY")
#pragma pop_macro("DECLARE_RPC")
#pragma pop_macro("MOVIESCENE_DETAILED_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("UE_MOVIESCENE_TODO_IMPL")
#pragma pop_macro("UE_MOVIESCENE_TODO")
#pragma pop_macro("INITIALIZE_NAVQUERY_SIMPLE")
#pragma pop_macro("INITIALIZE_NAVQUERY")
#pragma pop_macro("INITIALIZE_NAVQUERY_WLINKFILTER")
#pragma pop_macro("TEXT_WEAKOBJ_NAME")
#pragma pop_macro("REGISTER_NET_ANALYTICS")
#pragma pop_macro("CONDITIONAL_ON_PUSH_MODEL")
#pragma pop_macro("IS_PUSH_MODEL_ENABLED")
#pragma pop_macro("PUSH_MAKE_BP_PROPERTIES_PUSH_MODEL")
#pragma pop_macro("GET_PROPERTY_REP_INDEX")
#pragma pop_macro("GET_PROPERTY_REP_INDEX_STATIC_ARRAY_START")
#pragma pop_macro("GET_PROPERTY_REP_INDEX_STATIC_ARRAY_END")
#pragma pop_macro("GET_PROPERTY_REP_INDEX_STATIC_ARRAY_INDEX")
#pragma pop_macro("IS_PROPERTY_REPLICATED")
#pragma pop_macro("CONDITIONAL_ON_OBJECT_NET_ID")
#pragma pop_macro("CONDITIONAL_ON_OBJECT_NET_ID_DYNAMIC")
#pragma pop_macro("CONDITIONAL_ON_REP_INDEX_AND_OBJECT_NET_ID")
#pragma pop_macro("MARK_PROPERTY_DIRTY_UNSAFE")
#pragma pop_macro("MARK_PROPERTY_DIRTY")
#pragma pop_macro("MARK_PROPERTY_DIRTY_STATIC_ARRAY_INDEX")
#pragma pop_macro("MARK_PROPERTY_DIRTY_STATIC_ARRAY")
#pragma pop_macro("MARK_PROPERTY_DIRTY_FROM_NAME")
#pragma pop_macro("MARK_PROPERTY_DIRTY_FROM_NAME_STATIC_ARRAY_INDEX")
#pragma pop_macro("MARK_PROPERTY_DIRTY_FROM_NAME_STATIC_ARRAY")
#pragma pop_macro("GET_PROPERTY_REP_INDEX_STATIC_ARRAY")
#pragma pop_macro("REPLICATED_BASE_CLASS")
#pragma pop_macro("UE_NET_TRACE_CREATE_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_DESTROY_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_SCOPE")
#pragma pop_macro("UE_NET_TRACE_OBJECT_SCOPE")
#pragma pop_macro("UE_NET_TRACE_DYNAMIC_NAME_SCOPE")
#pragma pop_macro("UE_NET_TRACE_NAMED_SCOPE")
#pragma pop_macro("UE_NET_TRACE_NAMED_OBJECT_SCOPE")
#pragma pop_macro("UE_NET_TRACE_NAMED_DYNAMIC_NAME_SCOPE")
#pragma pop_macro("UE_NET_TRACE_SET_SCOPE_NAME")
#pragma pop_macro("UE_NET_TRACE_SET_SCOPE_OBJECTID")
#pragma pop_macro("UE_NET_TRACE_EXIT_NAMED_SCOPE")
#pragma pop_macro("UE_NET_TRACE_OFFSET_SCOPE")
#pragma pop_macro("UE_NET_TRACE")
#pragma pop_macro("UE_NET_TRACE_DYNAMIC_NAME")
#pragma pop_macro("UE_NET_TRACE_FLUSH_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_BEGIN_BUNCH")
#pragma pop_macro("UE_NET_TRACE_DISCARD_BUNCH")
#pragma pop_macro("UE_NET_TRACE_POP_SEND_BUNCH")
#pragma pop_macro("UE_NET_TRACE_EVENTS")
#pragma pop_macro("UE_NET_TRACE_END_BUNCH")
#pragma pop_macro("UE_NET_TRACE_BUNCH_SCOPE")
#pragma pop_macro("UE_NET_TRACE_ASSIGNED_GUID")
#pragma pop_macro("UE_NET_TRACE_NETHANDLE_CREATED")
#pragma pop_macro("UE_NET_TRACE_NETHANDLE_DESTROYED")
#pragma pop_macro("UE_NET_TRACE_CONNECTION_CREATED")
#pragma pop_macro("UE_NET_TRACE_CONNECTION_CLOSED")
#pragma pop_macro("UE_NET_TRACE_PACKET_DROPPED")
#pragma pop_macro("UE_NET_TRACE_PACKET_SEND")
#pragma pop_macro("UE_NET_TRACE_PACKET_RECV")
#pragma pop_macro("UE_NET_TRACE_END_SESSION")
#pragma pop_macro("UE_NET_TRACE_DO_IF")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_CREATE_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_DESTROY_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_FLUSH_COLLECTOR")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_BEGIN_BUNCH")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_DISCARD_BUNCH")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_POP_SEND_BUNCH")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_EVENTS")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_END_BUNCH")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_BUNCH_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_OBJECT_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_DYNAMIC_NAME_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_NAMED_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_NAMED_OBJECT_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_NAMED_DYNAMIC_NAME_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_SET_SCOPE_NAME")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_SET_SCOPE_OBJECTID")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_EXIT_NAMED_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_OFFSET_SCOPE")
#pragma pop_macro("UE_NET_TRACE_INTERNAL")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_DYNAMIC_NAME")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_ASSIGNED_GUID")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_NETHANDLE_CREATED")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_NETHANDLE_DESTROYED")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_CONNECTION_CREATED")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_CONNECTION_CLOSED")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_PACKET_DROPPED")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_PACKET_SEND")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_PACKET_RECV")
#pragma pop_macro("UE_NET_TRACE_INTERNAL_END_SESSION")
#pragma pop_macro("CASE_ENUM_TO_STR")
#pragma pop_macro("RETURN_IF_EQUAL")
#pragma pop_macro("ROTLEFT_64B")
#pragma pop_macro("_TEST_EQUAL")
#pragma pop_macro("_TEST_NOT_EQUAL")
#pragma pop_macro("_TEST_NULL")
#pragma pop_macro("_TEST_NOT_NULL")
#pragma pop_macro("TEST_EQUAL")
#pragma pop_macro("TEST_NOT_EQUAL")
#pragma pop_macro("TEST_TRUE")
#pragma pop_macro("TEST_FALSE")
#pragma pop_macro("TEST_NULL")
#pragma pop_macro("TEST_NOT_NULL")
#pragma pop_macro("TEST_BECOMES_TRUE")
#pragma pop_macro("MOCK_FUNC_NOT_IMPLEMENTED")
#pragma pop_macro("ARRAY")
#pragma pop_macro("ARRAYU64")
#pragma pop_macro("CASE_ENUM_SET")
#pragma pop_macro("MAP_TO_RESULTSTRING")
#pragma pop_macro("PrintCurlFeature")
#pragma pop_macro("EnumCase")
#pragma pop_macro("RESTRICT_SSL_TLS_PROTOCOL")
#pragma pop_macro("DEFINE_COMMAND")
#pragma pop_macro("DECLARE_COMMAND")
#pragma pop_macro("IMPL_HEADER_FIELD_GETTER")
#pragma pop_macro("OPUS_CHECK_CTL")
#pragma pop_macro("LWSWEBSOCKET_ESTATE_TOSTRING")
#pragma pop_macro("LOG_AND_GET_GL_QUERY_INT")
#pragma pop_macro("INDEX_TO_VOID")
#pragma pop_macro("ASSERT_NO_GL_ERROR")
#pragma pop_macro("LOG_GL_STRING")
#pragma pop_macro("LOG_AND_GET_GL_INT_TEMP")
#pragma pop_macro("CHECK_COMPRESSED_FORMAT")
#pragma pop_macro("LOG_GL_DEBUG_FLAG")
#pragma pop_macro("GL_CHECK")
#pragma pop_macro("LOG_AND_GET_GL_INT")
#pragma pop_macro("GET_GL_INT")
#pragma pop_macro("INTERFACE_BLOCK")
#pragma pop_macro("VERIFY_EGL")
#pragma pop_macro("MACRO_TOKENIZER")
#pragma pop_macro("MACRO_TOKENIZER2")
#pragma pop_macro("VERIFY_EGL_SCOPE_WITH_MSG_STR")
#pragma pop_macro("VERIFY_EGL_SCOPE")
#pragma pop_macro("VERIFY_EGL_FUNC")
#pragma pop_macro("QUERY_CHECK")
#pragma pop_macro("DEFINE_GL_ENTRYPOINTS")
#pragma pop_macro("GET_GL_ENTRYPOINTS")
#pragma pop_macro("CHECK_GL_ENTRYPOINTS")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_OPTIONAL")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_ALL")
#pragma pop_macro("DECLARE_GL_ENTRYPOINTS")
#pragma pop_macro("CHECK_GL_ENTRYPOINTS_NULL")
#pragma pop_macro("CHECK_GL_ENTRYPOINTS_OK")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_1_0")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_1_1")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_1_3")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_1_4")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_1_5")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_2_0")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_2_1")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_3_0")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_3_1")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_3_2")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_3_3")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_0")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_1")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_2")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_3")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_4")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_4_5")
#pragma pop_macro("GET_GL_ENTRYPOINTS_DLL")
#pragma pop_macro("ENUM_GL_ENTRYPOINTS_DLL")
#pragma pop_macro("UGL_REQUIRED")
#pragma pop_macro("UGL_OPTIONAL")
#pragma pop_macro("RHITHREAD_GLCOMMAND_PROLOGUE")
#pragma pop_macro("RHITHREAD_GLCOMMAND_EPILOGUE_RETURN")
#pragma pop_macro("RHITHREAD_GLCOMMAND_EPILOGUE_GET_RETURN")
#pragma pop_macro("RHITHREAD_GLCOMMAND_EPILOGUE")
#pragma pop_macro("RHITHREAD_GLCOMMAND_EPILOGUE_NORETURN")
#pragma pop_macro("SCOPE_CYCLE_COUNTER_DETAILED")
#pragma pop_macro("DETAILED_QUICK_SCOPE_CYCLE_COUNTER")
#pragma pop_macro("GLAF_CHECK")
#pragma pop_macro("CHECK_EXPECTED_GL_THREAD")
#pragma pop_macro("VERIFY_GL")
#pragma pop_macro("VERIFY_GL_SCOPE_WITH_MSG_STR")
#pragma pop_macro("VERIFY_GL_SCOPE")
#pragma pop_macro("VERIFY_GL_FUNC")
#pragma pop_macro("glBlitFramebuffer")
#pragma pop_macro("glTexImage2D")
#pragma pop_macro("glTexSubImage2D")
#pragma pop_macro("glCompressedTexImage2D")
#pragma pop_macro("REPORT_GL_DRAW_ARRAYS_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("REPORT_GL_DRAW_ARRAYS_INSTANCED_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("REPORT_GL_DRAW_RANGE_ELEMENTS_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("REPORT_GL_DRAW_ELEMENTS_INSTANCED_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("REPORT_GL_CLEAR_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("REPORT_GL_FRAMEBUFFER_BLIT_EVENT")
#pragma pop_macro("REPORT_GL_END_BUFFER_EVENT_FOR_FRAME_DUMP")
#pragma pop_macro("INITIATE_GL_FRAME_DUMP")
#pragma pop_macro("INITIATE_GL_FRAME_DUMP_EVERY_X_CALLS")
#pragma pop_macro("SCOPED_SCENE_READ_LOCK")
#pragma pop_macro("SCOPED_SCENE_WRITE_LOCK")
#pragma pop_macro("SCOPED_APEX_SCENE_READ_LOCK")
#pragma pop_macro("SCOPED_APEX_SCENE_WRITE_LOCK")
#pragma pop_macro("SCENE_LOCK_READ")
#pragma pop_macro("SCENE_UNLOCK_READ")
#pragma pop_macro("SCENE_LOCK_WRITE")
#pragma pop_macro("SCENE_UNLOCK_WRITE")
#pragma pop_macro("SCOPED_SCENE_READ_LOCK_INDEXED")
#pragma pop_macro("SCOPED_SCENE_WRITE_LOCK_INDEXED")
#pragma pop_macro("SQ_REPLAY_TEST")
#pragma pop_macro("BROADCAST_PROPERTY_CHANGED")
#pragma pop_macro("IMPLEMENT_COPY_RESOURCE_SHADER")
#pragma pop_macro("IMPLEMENT_COPY_RESOURCE_SHADER_ALL_TYPES")
#pragma pop_macro("IMPLEMENT_ONECOLORVS")
#pragma pop_macro("EmitRDGWarningf")
#pragma pop_macro("TEXT_TO_PIXELFORMAT")
#pragma pop_macro("DECLARE_GLOBAL_SHADER")
#pragma pop_macro("IMPLEMENT_GLOBAL_SHADER")
#pragma pop_macro("RDG_ASYNC_COMPUTE_BUDGET_SCOPE")
#pragma pop_macro("RDG_GPU_MASK_SCOPE")
#pragma pop_macro("IF_RDG_ENABLE_DEBUG")
#pragma pop_macro("IF_RDG_GPU_SCOPES")
#pragma pop_macro("IF_RDG_CPU_SCOPES")
#pragma pop_macro("RDG_EVENT_NAME")
#pragma pop_macro("RDG_EVENT_SCOPE")
#pragma pop_macro("RDG_EVENT_SCOPE_CONDITIONAL")
#pragma pop_macro("RDG_GPU_STAT_SCOPE")
#pragma pop_macro("RDG_CSV_STAT_EXCLUSIVE_SCOPE")
#pragma pop_macro("RDG_CSV_STAT_EXCLUSIVE_SCOPE_CONDITIONAL")
#pragma pop_macro("RDG_WAIT_FOR_TASKS_CONDITIONAL")
#pragma pop_macro("RDG_WAIT_FOR_TASKS")
#pragma pop_macro("SCOPED_SUSPEND_RENDERING_THREAD")
#pragma pop_macro("LogRenderCommand")
#pragma pop_macro("TASK_FUNCTION")
#pragma pop_macro("TASKNAME_FUNCTION")
#pragma pop_macro("ENQUEUE_RENDER_COMMAND")
#pragma pop_macro("SHADER_DECLARE_VTABLE")
#pragma pop_macro("INTERNAL_DECLARE_SHADER_TYPE_COMMON")
#pragma pop_macro("DECLARE_EXPORTED_SHADER_TYPE")
#pragma pop_macro("DECLARE_SHADER_TYPE")
#pragma pop_macro("DECLARE_SHADER_TYPE_EXPLICIT_BASES")
#pragma pop_macro("SHADER_TYPE_VTABLE")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE_WITH_DEBUG_NAME")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE2_WITH_TEMPLATE_PREFIX")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE2")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE3")
#pragma pop_macro("IMPLEMENT_SHADER_TYPE4_WITH_TEMPLATE_PREFIX")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSPS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSGSPS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSGS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSHSDSPS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSHSDSGSPS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSHSDS")
#pragma pop_macro("IMPLEMENT_SHADERPIPELINE_TYPE_VSHSDSGS")
#pragma pop_macro("IMPLEMENT_ALIGNED_TYPE")
#pragma pop_macro("INTERNAL_UNIFORM_BUFFER_STRUCT_GET_STRUCT_METADATA")
#pragma pop_macro("INTERNAL_SHADER_PARAMETER_GET_STRUCT_METADATA")
#pragma pop_macro("INTERNAL_SHADER_PARAMETER_STRUCT_BEGIN")
#pragma pop_macro("INTERNAL_SHADER_PARAMETER_EXPLICIT")
#pragma pop_macro("BEGIN_SHADER_PARAMETER_STRUCT")
#pragma pop_macro("END_SHADER_PARAMETER_STRUCT")
#pragma pop_macro("BEGIN_UNIFORM_BUFFER_STRUCT")
#pragma pop_macro("BEGIN_UNIFORM_BUFFER_STRUCT_WITH_CONSTRUCTOR")
#pragma pop_macro("END_UNIFORM_BUFFER_STRUCT")
#pragma pop_macro("IMPLEMENT_UNIFORM_BUFFER_STRUCT")
#pragma pop_macro("IMPLEMENT_UNIFORM_BUFFER_ALIAS_STRUCT")
#pragma pop_macro("IMPLEMENT_STATIC_UNIFORM_BUFFER_STRUCT")
#pragma pop_macro("IMPLEMENT_STATIC_UNIFORM_BUFFER_SLOT")
#pragma pop_macro("SHADER_PARAMETER")
#pragma pop_macro("SHADER_PARAMETER_EX")
#pragma pop_macro("SHADER_PARAMETER_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_ARRAY_EX")
#pragma pop_macro("SHADER_PARAMETER_TEXTURE")
#pragma pop_macro("SHADER_PARAMETER_TEXTURE_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_SRV")
#pragma pop_macro("SHADER_PARAMETER_SRV_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_UAV")
#pragma pop_macro("SHADER_PARAMETER_SAMPLER")
#pragma pop_macro("SHADER_PARAMETER_SAMPLER_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE_SRV")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE_SRV_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE_UAV")
#pragma pop_macro("SHADER_PARAMETER_RDG_TEXTURE_UAV_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_SRV")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_SRV_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_UAV")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_UAV_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_RDG_UNIFORM_BUFFER")
#pragma pop_macro("SHADER_PARAMETER_STRUCT")
#pragma pop_macro("SHADER_PARAMETER_STRUCT_ARRAY")
#pragma pop_macro("SHADER_PARAMETER_STRUCT_INCLUDE")
#pragma pop_macro("SHADER_PARAMETER_STRUCT_REF")
#pragma pop_macro("RDG_BUFFER_ACCESS")
#pragma pop_macro("RDG_BUFFER_ACCESS_DYNAMIC")
#pragma pop_macro("RDG_TEXTURE_ACCESS")
#pragma pop_macro("RDG_TEXTURE_ACCESS_DYNAMIC")
#pragma pop_macro("SHADER_PARAMETER_RDG_BUFFER_UPLOAD")
#pragma pop_macro("RENDER_TARGET_BINDING_SLOTS")
#pragma pop_macro("SHADER_USE_PARAMETER_STRUCT_INTERNAL")
#pragma pop_macro("SHADER_USE_PARAMETER_STRUCT")
#pragma pop_macro("SHADER_USE_PARAMETER_STRUCT_WITH_LEGACY_BASE")
#pragma pop_macro("SHADER_USE_ROOT_PARAMETER_STRUCT")
#pragma pop_macro("DECLARE_SHADER_PERMUTATION_IMPL")
#pragma pop_macro("SHADER_PERMUTATION_BOOL")
#pragma pop_macro("SHADER_PERMUTATION_INT")
#pragma pop_macro("SHADER_PERMUTATION_RANGE_INT")
#pragma pop_macro("SHADER_PERMUTATION_SPARSE_INT")
#pragma pop_macro("SHADER_PERMUTATION_ENUM_CLASS")
#pragma pop_macro("STRUCTMEMBER_VERTEXSTREAMCOMPONENT")
#pragma pop_macro("IMPLEMENT_VERTEX_FACTORY_PARAMETER_TYPE")
#pragma pop_macro("DECLARE_VERTEX_FACTORY_TYPE")
#pragma pop_macro("IMPLEMENT_VERTEX_FACTORY_VTABLE")
#pragma pop_macro("IMPLEMENT_VERTEX_FACTORY_TYPE")
#pragma pop_macro("IMPLEMENT_TEMPLATE_VERTEX_FACTORY_TYPE_EX")
#pragma pop_macro("IMPLEMENT_VERTEX_FACTORY_TYPE_EX")
#pragma pop_macro("SCOPED_GPU_EVENT")
#pragma pop_macro("SCOPED_GPU_EVENT_COLOR")
#pragma pop_macro("SCOPED_GPU_EVENTF")
#pragma pop_macro("SCOPED_GPU_EVENTF_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_GPU_EVENT")
#pragma pop_macro("SCOPED_CONDITIONAL_GPU_EVENT_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_GPU_EVENTF")
#pragma pop_macro("SCOPED_CONDITIONAL_GPU_EVENTF_COLOR")
#pragma pop_macro("BEGIN_GPU_EVENTF")
#pragma pop_macro("BEGIN_GPU_EVENTF_COLOR")
#pragma pop_macro("STOP_GPU_EVENT")
#pragma pop_macro("SCOPED_DRAW_EVENT")
#pragma pop_macro("SCOPED_DRAW_EVENT_COLOR")
#pragma pop_macro("SCOPED_DRAW_EVENTF")
#pragma pop_macro("SCOPED_DRAW_EVENTF_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_DRAW_EVENT")
#pragma pop_macro("SCOPED_CONDITIONAL_DRAW_EVENT_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_DRAW_EVENTF")
#pragma pop_macro("SCOPED_CONDITIONAL_DRAW_EVENTF_COLOR")
#pragma pop_macro("BEGIN_DRAW_EVENTF")
#pragma pop_macro("BEGIN_DRAW_EVENTF_COLOR")
#pragma pop_macro("STOP_DRAW_EVENT")
#pragma pop_macro("SCOPED_RHI_DRAW_EVENT")
#pragma pop_macro("SCOPED_RHI_DRAW_EVENT_COLOR")
#pragma pop_macro("SCOPED_RHI_DRAW_EVENTF")
#pragma pop_macro("SCOPED_RHI_DRAW_EVENTF_COLOR")
#pragma pop_macro("SCOPED_RHI_CONDITIONAL_DRAW_EVENT")
#pragma pop_macro("SCOPED_RHI_CONDITIONAL_DRAW_EVENT_COLOR")
#pragma pop_macro("SCOPED_RHI_CONDITIONAL_DRAW_EVENTF")
#pragma pop_macro("SCOPED_RHI_CONDITIONAL_DRAW_EVENTF_COLOR")
#pragma pop_macro("SCOPED_COMPUTE_EVENT")
#pragma pop_macro("SCOPED_COMPUTE_EVENT_COLOR")
#pragma pop_macro("SCOPED_COMPUTE_EVENTF")
#pragma pop_macro("SCOPED_COMPUTE_EVENTF_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_COMPUTE_EVENT")
#pragma pop_macro("SCOPED_CONDITIONAL_COMPUTE_EVENT_COLOR")
#pragma pop_macro("SCOPED_CONDITIONAL_COMPUTE_EVENTF")
#pragma pop_macro("SCOPED_CONDITIONAL_COMPUTE_EVENTF_COLOR")
#pragma pop_macro("DECLARE_GPU_STAT")
#pragma pop_macro("DECLARE_GPU_DRAWCALL_STAT")
#pragma pop_macro("DECLARE_GPU_DRAWCALL_STAT_EXTERN")
#pragma pop_macro("DECLARE_GPU_STAT_NAMED")
#pragma pop_macro("DECLARE_GPU_DRAWCALL_STAT_NAMED")
#pragma pop_macro("DECLARE_GPU_STAT_NAMED_EXTERN")
#pragma pop_macro("DEFINE_GPU_STAT")
#pragma pop_macro("DEFINE_GPU_DRAWCALL_STAT")
#pragma pop_macro("SCOPED_GPU_STAT")
#pragma pop_macro("GPU_STATS_BEGINFRAME")
#pragma pop_macro("GPU_STATS_ENDFRAME")
#pragma pop_macro("IMPLEMENT_ATMOSPHERE_TEXTURE_PARAM_SET")
#pragma pop_macro("SHADER_VARIATION")
#pragma pop_macro("IMPLEMENT_BASEPASS_VERTEXSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_BASEPASS_VERTEXSHADER_ONLY_TYPE")
#pragma pop_macro("IMPLEMENT_BASEPASS_PIXELSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_BASEPASS_LIGHTMAPPED_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_CAPSULE_SHADOW_TYPE")
#pragma pop_macro("IMPLEMENT_CAPSULE_APPLY_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_GetDepthPassShaders")
#pragma pop_macro("VARIATION1")
#pragma pop_macro("IMPLEMENT_REMOVE_OBJECTS_FROM_BUFFER_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_CONETRACE_CS_TYPE")
#pragma pop_macro("IMPLEMENT_CONETRACE_GLOBAL_CS_TYPE")
#pragma pop_macro("VARIATION")
#pragma pop_macro("IMPLEMENT_GLOBALDF_COMPOSITE_CS_TYPE")
#pragma pop_macro("GROUPSHARED_COMPLEX_TRANSFORM")
#pragma pop_macro("GROUPSHARED_TWO_FOR_ONE_TRANSFORM")
#pragma pop_macro("GROUPSHARED_CONVOLUTION_WTEXTURE")
#pragma pop_macro("GET_COMPLEX_SHADER")
#pragma pop_macro("GET_TWOFORONE_SHADER")
#pragma pop_macro("GET_GROUP_SHARED_TEXTURE_FILTER")
#pragma pop_macro("IMPLEMENT_DENSITY_VERTEXSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_DENSITY_PIXELSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_DENSITY_LIGHTMAPPED_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_MOBILE_SHADING_BASEPASS_LIGHTMAPPED_VERTEX_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_MOBILE_SHADING_BASEPASS_LIGHTMAPPED_PIXEL_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_MOBILE_SHADING_BASEPASS_LIGHTMAPPED_SHADER_TYPE")
#pragma pop_macro("IMPLEMENT_MOBILE_SCENE_CAPTURECOPY")
#pragma pop_macro("SET_SPEEDTREE_TABLE_FLOAT4V")
#pragma pop_macro("FASTVRAM_CVAR")
#pragma pop_macro("CANVAS_HEADER")
#pragma pop_macro("CANVAS_LINE")
#pragma pop_macro("IMPLEMENT_MATERIAL_SHADER_SetParameters")
#pragma pop_macro("IMPLEMENT_SHADOW_DEPTH_SHADERMODE_SHADERS")
#pragma pop_macro("IMPLEMENT_SHADOWDEPTHPASS_PIXELSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_SHADOW_PROJECTION_PIXEL_SHADER")
#pragma pop_macro("IMPLEMENT_MODULATED_SHADOW_PROJECTION_PIXEL_SHADER")
#pragma pop_macro("IMPLEMENT_ONEPASS_POINT_SHADOW_PROJECTION_PIXEL_SHADER")
#pragma pop_macro("COPYMACRO")
#pragma pop_macro("IMPLEMENT_INJECTION_PIXELSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_RESOLVE_SHADER")
#pragma pop_macro("ENTRY")
#pragma pop_macro("IMPLEMENT_POST_PROCESS_PARAM_SET")
#pragma pop_macro("UPDATE_HISTORY_FLAGS")
#pragma pop_macro("ANY_CAPTURE_RENDERED_RECENTLY")
#pragma pop_macro("ANY_HIGHRES_CAPTURE_RENDERED_RECENTLY")
#pragma pop_macro("GET_STENCIL_BIT_MASK")
#pragma pop_macro("STENCIL_LIGHTING_CHANNELS_MASK")
#pragma pop_macro("GET_STENCIL_MOBILE_SM_MASK")
#pragma pop_macro("SCOPED_DRAW_OR_COMPUTE_EVENT")
#pragma pop_macro("IMPLEMENT_MATERIALCHS_TYPE")
#pragma pop_macro("IMPLEMENT_VIRTUALTEXTURE_SHADER_TYPE")
#pragma pop_macro("COMPARE_FIELD_BEGIN")
#pragma pop_macro("COMPARE_FIELD")
#pragma pop_macro("A")
#pragma pop_macro("TRACE_GPUPROFILER_DEFINE_EVENT_TYPE")
#pragma pop_macro("TRACE_GPUPROFILER_DECLARE_EVENT_TYPE_EXTERN")
#pragma pop_macro("TRACE_GPUPROFILER_EVENT_TYPE")
#pragma pop_macro("TRACE_GPUPROFILER_BEGIN_FRAME")
#pragma pop_macro("TRACE_GPUPROFILER_BEGIN_EVENT")
#pragma pop_macro("TRACE_GPUPROFILER_END_EVENT")
#pragma pop_macro("TRACE_GPUPROFILER_END_FRAME")
#pragma pop_macro("RHI_DRAW_CALL_INC")
#pragma pop_macro("RHI_DRAW_CALL_STATS")
#pragma pop_macro("RHISTAT")
#pragma pop_macro("FRHICOMMAND_MACRO")
#pragma pop_macro("ALLOC_COMMAND")
#pragma pop_macro("ALLOC_COMMAND_CL")
#pragma pop_macro("SCOPED_GPU_MASK")
#pragma pop_macro("SCOPED_UNIFORM_BUFFER_GLOBAL_BINDINGS")
#pragma pop_macro("INTERNAL_DECORATOR_COMPUTE")
#pragma pop_macro("IMPLEMENT_DDPSPI_SETTING_WITH_RETURN_TYPE")
#pragma pop_macro("IMPLEMENT_DDPSPI_SETTING")
#pragma pop_macro("GEOMETRY_SHADER")
#pragma pop_macro("TESSELLATION_SHADER")
#pragma pop_macro("RTACTION_MAKE_MASK")
#pragma pop_macro("DUMP_TRANSITION")
#pragma pop_macro("UE_LOG_RIGVMMEMORY")
#pragma pop_macro("BREAK_WHEN_AUDIBLE")
#pragma pop_macro("BREAK_WHEN_TOO_LOUD")
#pragma pop_macro("CHECK_SAMPLE")
#pragma pop_macro("CHECK_SAMPLE2")
#pragma pop_macro("UI_COMMAND_EXT")
#pragma pop_macro("UI_COMMAND")
#pragma pop_macro("APPEND_FONT")
#pragma pop_macro("APPEND_EDITOR_FONT")
#pragma pop_macro("APPEND_RANGE")
#pragma pop_macro("RETURN_TRUE_IF_CHAR_WITHIN_RANGE")
#pragma pop_macro("REGISTER_UNICODE_BLOCK_RANGE")
#pragma pop_macro("AddToNextFocusableWidgetCondidateDebugResults")
#pragma pop_macro("SLATE_CROSS_THREAD_CHECK")
#pragma pop_macro("DRAG_DROP_OPERATOR_TYPE")
#pragma pop_macro("UE_TRACE_SLATE_APPLICATION_TICK_AND_DRAW_WIDGETS")
#pragma pop_macro("UE_TRACE_SLATE_WIDGET_ADDED")
#pragma pop_macro("UE_TRACE_SLATE_WIDGET_DEBUG_INFO")
#pragma pop_macro("UE_TRACE_SLATE_WIDGET_REMOVED")
#pragma pop_macro("UE_TRACE_SCOPED_SLATE_WIDGET_PAINT")
#pragma pop_macro("UE_TRACE_SLATE_WIDGET_UPDATED")
#pragma pop_macro("UE_TRACE_SLATE_WIDGET_INVALIDATED")
#pragma pop_macro("UE_TRACE_SLATE_ROOT_INVALIDATED")
#pragma pop_macro("UE_TRACE_SLATE_ROOT_CHILDORDER_INVALIDATED")
#pragma pop_macro("SLATE_METADATA_TYPE")
#pragma pop_macro("SNew")
#pragma pop_macro("SAssignNew")
#pragma pop_macro("SLATE_BEGIN_ARGS")
#pragma pop_macro("SLATE_USER_ARGS")
#pragma pop_macro("HACK_SLATE_SLOT_ARGS")
#pragma pop_macro("SLATE_END_ARGS")
#pragma pop_macro("SLATE_ATTRIBUTE")
#pragma pop_macro("SLATE_ARGUMENT")
#pragma pop_macro("SLATE_ARGUMENT_DEFAULT")
#pragma pop_macro("SLATE_STYLE_ARGUMENT")
#pragma pop_macro("SLATE_SUPPORTS_SLOT")
#pragma pop_macro("SLATE_SUPPORTS_SLOT_WITH_ARGS")
#pragma pop_macro("SLATE_NAMED_SLOT")
#pragma pop_macro("SLATE_DEFAULT_SLOT")
#pragma pop_macro("SLATE_EVENT")
#pragma pop_macro("SCOPE_CYCLE_SWIDGET")
#pragma pop_macro("IMPLEMENT_SLATE_VERTEXMATERIALSHADER_TYPE")
#pragma pop_macro("IMPLEMENT_SLATE_MATERIALSHADER_TYPE")
#pragma pop_macro("SLATE_DRAW_EVENT")
#pragma pop_macro("IMPLEMENT_SLATE_PIXELSHADER_TYPE")
#pragma pop_macro("UE_TRACE_EVENT_DEFINE")
#pragma pop_macro("UE_TRACE_EVENT_BEGIN")
#pragma pop_macro("UE_TRACE_EVENT_BEGIN_EXTERN")
#pragma pop_macro("UE_TRACE_EVENT_FIELD")
#pragma pop_macro("UE_TRACE_EVENT_END")
#pragma pop_macro("UE_TRACE_LOG")
#pragma pop_macro("UE_TRACE_LOG_SCOPED")
#pragma pop_macro("UE_TRACE_LOG_SCOPED_T")
#pragma pop_macro("UE_TRACE_CHANNEL")
#pragma pop_macro("UE_TRACE_CHANNEL_EXTERN")
#pragma pop_macro("UE_TRACE_CHANNEL_MODULE_EXTERN")
#pragma pop_macro("UE_TRACE_CHANNEL_DEFINE")
#pragma pop_macro("UE_TRACE_CHANNELEXPR_IS_ENABLED")
#pragma pop_macro("TRACE_PRIVATE_FIELD")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL_DECLARE")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL_IMPL")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL_MODULE_EXTERN")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL_DEFINE")
#pragma pop_macro("TRACE_PRIVATE_CHANNEL_EXTERN")
#pragma pop_macro("TRACE_PRIVATE_CHANNELEXPR_IS_ENABLED")
#pragma pop_macro("TRACE_PRIVATE_EVENT_DEFINE")
#pragma pop_macro("TRACE_PRIVATE_EVENT_BEGIN")
#pragma pop_macro("TRACE_PRIVATE_EVENT_BEGIN_EXTERN")
#pragma pop_macro("TRACE_PRIVATE_EVENT_BEGIN_IMPL")
#pragma pop_macro("TRACE_PRIVATE_EVENT_FIELD")
#pragma pop_macro("TRACE_PRIVATE_EVENT_END")
#pragma pop_macro("TRACE_PRIVATE_LOG_PRELUDE")
#pragma pop_macro("TRACE_PRIVATE_LOG_EPILOG")
#pragma pop_macro("TRACE_PRIVATE_LOG")
#pragma pop_macro("TRACE_PRIVATE_LOG_SCOPED")
#pragma pop_macro("TRACE_PRIVATE_LOG_SCOPED_T")
#pragma pop_macro("IMPLEMENT_TYPED_UMG_LIST")
#pragma pop_macro("BIND_UOBJECT_ATTRIBUTE")
#pragma pop_macro("BIND_UOBJECT_DELEGATE")
#pragma pop_macro("OPTIONAL_BINDING")
#pragma pop_macro("PROPERTY_BINDING")
#pragma pop_macro("BITFIELD_PROPERTY_BINDING")
#pragma pop_macro("PROPERTY_BINDING_IMPLEMENTATION")
#pragma pop_macro("GAME_SAFE_OPTIONAL_BINDING")
#pragma pop_macro("GAME_SAFE_BINDING_IMPLEMENTATION")
#pragma pop_macro("OPTIONAL_BINDING_CONVERT")
#pragma pop_macro("UA_SYSTEM_ERROR")
#pragma pop_macro("SOUND_CONVERT_CHECK")
#pragma pop_macro("DEBUG_AUDIO_CHECK_AUDIO_THREAD")
#pragma pop_macro("DEBUG_AUDIO_CHECK_MAIN_THREAD")
#pragma pop_macro("DEBUG_AUDIO_CHECK")
#pragma pop_macro("DEBUG_AUDIO_CHECK_MSG")
#pragma pop_macro("AUDIO_VOICE_CHECK_ERROR")
#pragma pop_macro("AUDIO_VOICE_CHECK_SUSPEND")
#pragma pop_macro("VOICE_CHECK_INITIALIZATION")
#pragma pop_macro("UA_DEVICE_PLATFORM_ERROR")
#pragma pop_macro("AU_DEVICE_PARAM_ERROR")
#pragma pop_macro("AU_DEVICE_WARNING")
#pragma pop_macro("VectorIntShuffle")
#pragma pop_macro("VK_DYNAMICAPI_TO_VULKANRHI")
#pragma pop_macro("VULKAN_REPORT_LOG")
#pragma pop_macro("VKSWITCHCASE")
#pragma pop_macro("AppendBitFieldName")
#pragma pop_macro("VULKAN_SET_DEBUG_NAME")
#pragma pop_macro("VERIFYVULKANRESULT_INIT")
#pragma pop_macro("LLM_SCOPE_VULKAN")
#pragma pop_macro("LLM_PLATFORM_SCOPE_VULKAN")
#pragma pop_macro("LLM_TRACK_VULKAN_HIGH_LEVEL_ALLOC")
#pragma pop_macro("LLM_TRACK_VULKAN_HIGH_LEVEL_FREE")
#pragma pop_macro("LLM_TRACK_VULKAN_SPARE_MEMORY_GPU")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_INSTANCE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_SURFACE_INSTANCE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_BASE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_OPTIONAL_BASE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_OPTIONAL_INSTANCE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_ALL")
#pragma pop_macro("DECLARE_VK_ENTRYPOINTS")
#pragma pop_macro("VULKAN_LOGMEMORY")
#pragma pop_macro("VULKAN_FILL_TRACK_INFO")
#pragma pop_macro("VULKAN_FREE_TRACK_INFO")
#pragma pop_macro("VULKAN_TRACK_STRING")
#pragma pop_macro("VKSWITCH")
#pragma pop_macro("LRUPRINT")
#pragma pop_macro("LRUPRINT_DEBUG")
#pragma pop_macro("VKERRORCASE")
#pragma pop_macro("DEFINE_VK_ENTRYPOINTS")
#pragma pop_macro("CHECK_VK_ENTRYPOINTS")
#pragma pop_macro("GET_VK_ENTRYPOINTS")
#pragma pop_macro("GETINSTANCE_VK_ENTRYPOINTS")
#pragma pop_macro("CLEAR_VK_ENTRYPOINTS")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_PLATFORM_BASE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_PLATFORM_INSTANCE")
#pragma pop_macro("ENUM_VK_ENTRYPOINTS_OPTIONAL_PLATFORM_INSTANCE")
#pragma pop_macro("VULKAN_SIGNAL_UNIMPLEMENTED")
#pragma pop_macro("VULKAN_TRACK_OBJECT_CREATE")
#pragma pop_macro("VULKAN_TRACK_OBJECT_DELETE")
#pragma pop_macro("VERIFYVULKANRESULT")
#pragma pop_macro("VERIFYVULKANRESULT_EXPANDED")
#pragma pop_macro("XAUDIO2_GOTO_CLEANUP_ON_FAIL")
#pragma pop_macro("XAUDIO2_RETURN_ON_FAIL")
#pragma pop_macro("NVAFTERMATH_ON_ERROR")
#pragma pop_macro("D3D11_STATE_CACHE_VERIFY")
#pragma pop_macro("D3D11_STATE_CACHE_VERIFY_PRE")
#pragma pop_macro("D3D11_STATE_CACHE_VERIFY_POST")
#pragma pop_macro("CACHE_NV_AFTERMATH_ENABLED")
#pragma pop_macro("START_NV_AFTERMATH")
#pragma pop_macro("STOP_NV_AFTERMATH")
#pragma pop_macro("VERIFYD3D11RESULT_EX")
#pragma pop_macro("VERIFYD3D11RESULT")
#pragma pop_macro("VERIFYD3D11RESULT_NOEXIT")
#pragma pop_macro("VERIFYD3D11SHADERRESULT")
#pragma pop_macro("VERIFYD3D11CREATETEXTURERESULT")
#pragma pop_macro("VERIFYD3D11RESIZEVIEWPORTRESULT")
#pragma pop_macro("CLEANUP_ON_FAIL")
#pragma pop_macro("RETURN_FALSE_ON_FAIL")
#pragma pop_macro("PI")

#ifdef LIBCARLA_INCLUDED_FROM_UE4
#undef LIBCARLA_INCLUDED_FROM_UE4
#else
#error "This file does not support nesting guards (preprocessor can't do math) - instead, in LibCarla, enable macros, include Unreal header, and disable macros."
#endif
