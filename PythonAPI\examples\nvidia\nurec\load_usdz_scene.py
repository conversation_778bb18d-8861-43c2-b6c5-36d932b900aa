#load_usdz_scene.py
# SPDX-FileCopyrightText: © 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# SPDX-License-Identifier: MIT
"""
NUREC USDZ Scene Loader for CARLA
This script loads a NUREC-generated .usdz file and sets up the corresponding
scene in CARLA, which is built on Unreal Engine. It demonstrates how to:
- Connect to a CARLA server.
- Load the OpenDRIVE map and mesh data from the .usdz file.
- Set up the CARLA world with the appropriate map and assets.
Example usage:
    python load_usdz_scene.py --usdz-filename /path/to/your/scenario.usdz
"""
import carla
import argparse
import logging
import zipfile
import os
# Set up logging
logging.basicConfig(
    format="%(asctime)s %(levelname)-8s %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("usdz_scene_loader")
def load_and_configure_world(client: carla.Client, usdz_filename: str) -> None:
    """
    Loads a new world in CARLA and configures it based on the .usdz file content.
    Args:
        client: The CARLA client object.
        usdz_filename: Path to the .usdz file.
    """
    try:
        with zipfile.ZipFile(usdz_filename, 'r') as usdz_file:
            # Extract the map.xodr from the .usdz file
            xodr_data = usdz_file.read('map.xodr').decode('utf-8')
            # Extract the mesh.ply from the .usdz file
            ply_data = usdz_file.read('mesh.ply')
        # Load the world with the OpenDRIVE map
        vertex_distance = 2.0  # in meters
        max_road_length = 500.0 # in meters
        wall_height = 1.0      # in meters
        extra_lane_width = 0.6 # in meters
        settings = carla.OpendriveGenerationParameters(
            vertex_distance=vertex_distance,
            max_road_length=max_road_length,
            wall_height=wall_height,
            additional_width=extra_lane_width,
            smooth_junctions=True,
            enable_mesh_visibility=True
        )
        world = client.generate_opendrive_world(xodr_data, settings)
        logger.info("Successfully loaded the OpenDRIVE map into CARLA.")
        # At this point, you would typically use the Unreal Engine API
        # to import the 'mesh.ply' and other assets, and place them in the scene.
        # This part of the process is highly dependent on the specifics of
        # your Unreal Engine project and is not directly achievable through
        # the CARLA Python API alone. The following is a conceptual placeholder:
        # --- Placeholder for Unreal Engine asset import and placement ---
        # 1. Save the 'mesh.ply' to a temporary file.
        # 2. Use an Unreal Engine Python script (executed within the UE editor)
        #    to import the .ply file as a static mesh.
        # 3. Place the imported mesh actor into the world at the correct coordinates.
        #    These coordinates might need to be adjusted to align with the
        #    OpenDRIVE map's origin.
        # --- End of placeholder ---
        logger.info("Unreal Engine scene created based on the .usdz file.")
    except FileNotFoundError:
        logger.error(f"The file '{usdz_filename}' was not found.")
    except KeyError as e:
        logger.error(f"The .usdz file is missing a required file: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
def main() -> None:
    """
    Main function to parse arguments and initiate the scene loading process.
    """
    argparser = argparse.ArgumentParser(
        description=__doc__)
    argparser.add_argument(
        '--host',
        metavar='H',
        default='127.0.0.1',
        help='IP of the host server (default: 127.0.0.1)')
    argparser.add_argument(
        '-p', '--port',
        metavar='P',
        default=2000,
        type=int,
        help='TCP port to listen to (default: 2000)')
    argparser.add_argument(
        '-u', '--usdz-filename',
        metavar='U',
        required=True,
        help='Path to the USDZ file containing the scene data')
    args = argparser.parse_args()
    try:
        client = carla.Client(args.host, args.port)
        client.set_timeout(10.0)
        load_and_configure_world(client, args.usdz_filename)
    except Exception as e:
        logger.error(f"Failed to connect to CARLA or load the scene: {e}")
if __name__ == '__main__':
    main()