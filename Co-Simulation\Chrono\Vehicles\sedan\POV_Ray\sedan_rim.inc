//wrap the file with the version
#local Temp_version = version;
#version 3.7;
//==================================================
//POV-Ray Geometry file
//==================================================
//This file has been created by PoseRay v3.13.29.645
//3D model to POV-Ray/Moray Converter.
//Author: FlyerX
//Email: <EMAIL>
//Web: https://sites.google.com/site/poseray/
//==================================================
//Files needed to run the POV-Ray scene:
//sedan_rim_POV_main.ini (initialization file - open this to render)
//sedan_rim_POV_scene.pov (scene setup of cameras, lights and geometry)
//sedan_rim.inc (geometry)
//sedan_rim_mat.inc (materials)
// 
//==================================================
//Model Statistics:
//Number of triangular faces..... 1812
//Number of vertices............. 1155
//Number of normals.............. 1269
//Number of UV coordinates....... 0
//Number of lines................ 0
//Number of materials............ 1
//Number of groups/meshes........ 1
//Number of subdivision faces.... 0
//Bounding Box....... from x,y,z=(-0.201885,-0.101713,-0.202452)
//                      to x,y,z=(0.204115,0.103291,0.203548)
//                 size dx,dy,dz=(0.406,0.205004,0.406)
//                  center x,y,z=(0.00111499999999999,0.000788999999999998,0.000548000000000007)
//                       diagonal 0.609670927645398
//Surface area................... 0.483239157551537
//             Smallest face area 7.01870834230444E-6
//              Largest face area 0.003012707346
//Memory allocated for geometry: 169 KBytes
// 
//==================================================
 
 
//Materials
#include "sedan_rim_mat.inc"
 
//Geometry
#declare sedan_rim_Material_001_=mesh2{
vertex_vectors{
1155,
<0.196377,0.103287,0.000503>,
<0.190167,0.100735,0.037971>,
<0.191914,0.103287,0.039881>,
<0.188743,0.100735,0.045971>,
<0.176939,0.103287,0.086009>,
<0.190618,0.103287,0.045582>,
<0.194754,0.100735,0.000504>,
<0.18444,0.100735,0.035529>,
<0.170151,0.100735,0.082317>,
<0.175562,0.100735,0.084928>,
<0.188749,0.100735,0.000505>,
<0.177457,0.091268,0.032674>,
<0.182938,0.100735,0.044323>,
<0.163568,0.091268,0.079139>,
<0.181443,0.091268,0.000507>,
<0.169635,0.091268,0.029452>,
<0.175939,0.091268,0.04227>,
<0.156132,0.091268,0.075549>,
<0.183687,0.103287,0.043139>,
<0.176238,0.103287,0.040514>,
<0.168137,0.091268,0.039762>,
<0.169751,0.103287,0.031096>,
<0.177652,0.103287,0.034227>,
<0.185039,0.103287,0.037156>,
<0.093204,0.103287,0.01438>,
<0.072746,0.095304,0.019963>,
<0.093204,0.095304,0.02078>,
<0.072746,0.103287,0.014258>,
<0.066201,0.095358,0.023564>,
<0.112154,0.103287,0.019035>,
<0.111516,0.095304,0.025435>,
<0.168273,0.103287,0.037707>,
<0.114087,0.103287,0.006867>,
<0.121433,0.094357,0.000548>,
<0.11133,0.103287,0.000548>,
<0.12419,0.094357,0.006867>,
<0.021833,0.103287,-0.053001>,
<0.016902,0.095358,-0.066807>,
<0.005733,0.103287,-0.056687>,
<0.196377,0.103287,0.000594>,
<0.190167,0.100735,-0.036874>,
<0.194754,0.100735,0.000593>,
<0.176939,0.103287,-0.084912>,
<0.188743,0.100735,-0.044874>,
<0.190618,0.103287,-0.044486>,
<0.18444,0.100735,-0.034433>,
<0.188749,0.100735,0.000592>,
<0.170151,0.100735,-0.08122>,
<0.182938,0.100735,-0.043227>,
<0.177457,0.091268,-0.031577>,
<0.181443,0.091268,0.00059>,
<0.163568,0.091268,-0.078042>,
<0.175939,0.091268,-0.041174>,
<0.169635,0.091268,-0.028355>,
<0.173191,0.091268,0.000588>,
<0.156132,0.091268,-0.074453>,
<0.168137,0.091268,-0.038665>,
<0.183687,0.103287,-0.042043>,
<0.176238,0.103287,-0.039418>,
<0.169751,0.103287,-0.029999>,
<0.177652,0.103287,-0.033131>,
<0.185039,0.103287,-0.036059>,
<0.191914,0.103287,-0.038784>,
<0.093204,0.103287,-0.013283>,
<0.072746,0.095304,-0.018866>,
<0.072746,0.103287,-0.013162>,
<0.066201,0.095358,-0.022467>,
<0.055799,0.103287,-0.016642>,
<0.112154,0.103287,-0.017938>,
<0.093204,0.095304,-0.019683>,
<0.168273,0.103287,-0.03661>,
<0.111516,0.095304,-0.024338>,
<0.114087,0.103287,-0.005771>,
<0.12419,0.094357,-0.005771>,
<0.069338,0.103287,0.064087>,
<0.047774,0.103287,0.081335>,
<0.069857,0.103287,0.086924>,
<0.122918,0.103287,0.153585>,
<0.08984,0.100735,0.172077>,
<0.08944,0.103287,0.174638>,
<0.082716,0.100735,0.175949>,
<0.044146,0.103287,0.191656>,
<0.084188,0.103287,0.177176>,
<0.121906,0.100735,0.152313>,
<0.088172,0.100735,0.166064>,
<0.042792,0.100735,0.184031>,
<0.04413,0.100735,0.189902>,
<0.118161,0.100735,0.147606>,
<0.086045,0.091268,0.158808>,
<0.080381,0.100735,0.170369>,
<0.041165,0.091268,0.176887>,
<0.113605,0.091268,0.141878>,
<0.083679,0.091268,0.150665>,
<0.077617,0.091268,0.163601>,
<0.039327,0.091268,0.168818>,
<0.081771,0.103287,0.170218>,
<0.079173,0.103287,0.162741>,
<0.074708,0.091268,0.155919>,
<0.08247,0.103287,0.151781>,
<0.084955,0.103287,0.159929>,
<0.087278,0.103287,0.167548>,
<0.030666,0.095304,0.068773>,
<0.042785,0.095304,0.085325>,
<0.035113,0.103287,0.065217>,
<0.023779,0.095358,0.065886>,
<0.05596,0.103287,0.099096>,
<0.050574,0.095304,0.102586>,
<0.076395,0.103287,0.154744>,
<0.06665,0.103287,0.093026>,
<0.076156,0.094357,0.094846>,
<0.07295,0.094357,0.100949>,
<0.05966,0.095358,-0.036091>,
<0.052984,0.103287,-0.024013>,
<0.063486,0.095358,-0.029117>,
<0.122848,0.103287,0.153642>,
<0.148183,0.100735,0.125412>,
<0.177381,0.103287,0.085087>,
<0.153531,0.100735,0.119308>,
<0.154397,0.103287,0.12102>,
<0.142709,0.100735,0.122443>,
<0.170271,0.100735,0.082067>,
<0.148627,0.100735,0.115782>,
<0.136129,0.091268,0.118748>,
<0.11354,0.091268,0.14193>,
<0.163689,0.091268,0.078886>,
<0.142663,0.091268,0.111574>,
<0.12874,0.091268,0.114623>,
<0.108458,0.091268,0.135408>,
<0.156255,0.091268,0.075293>,
<0.135843,0.091268,0.10702>,
<0.148171,0.103287,0.117108>,
<0.141481,0.103287,0.112904>,
<0.130094,0.103287,0.113689>,
<0.137461,0.103287,0.117932>,
<0.14435,0.103287,0.121899>,
<0.150761,0.103287,0.125591>,
<0.060934,0.095304,0.044563>,
<0.056488,0.103287,0.04812>,
<0.05966,0.095358,0.037186>,
<0.048634,0.103287,0.032662>,
<0.084781,0.103287,0.076044>,
<0.074327,0.095304,0.060096>,
<0.134326,0.103287,0.108408>,
<0.089372,0.095304,0.071553>,
<0.076502,0.103287,0.085146>,
<0.082801,0.094357,0.093069>,
<-0.042212,0.103287,0.191429>,
<-0.07725,0.100735,0.17702>,
<-0.079496,0.103287,0.178303>,
<-0.08471,0.100735,0.173848>,
<-0.121002,0.103287,0.153396>,
<-0.084749,0.103287,0.175767>,
<-0.041851,0.100735,0.189842>,
<-0.073603,0.100735,0.171963>,
<-0.115902,0.100735,0.14758>,
<-0.119645,0.100735,0.152289>,
<-0.040516,0.100735,0.18397>,
<-0.069273,0.091268,0.165771>,
<-0.081816,0.100735,0.168537>,
<-0.111349,0.091268,0.14185>,
<-0.038892,0.091268,0.176826>,
<-0.064401,0.091268,0.158839>,
<-0.078264,0.091268,0.16215>,
<-0.106205,0.091268,0.135378>,
<-0.080832,0.103287,0.169533>,
<-0.076623,0.103287,0.162834>,
<-0.074089,0.091268,0.15508>,
<-0.066024,0.103287,0.158587>,
<-0.070827,0.103287,0.165615>,
<-0.075317,0.103287,0.172187>,
<-0.032744,0.103287,0.087456>,
<-0.033618,0.095304,0.066209>,
<-0.038965,0.095304,0.086032>,
<-0.028073,0.103287,0.067479>,
<-0.035662,0.095358,0.059008>,
<-0.041485,0.103287,0.10495>,
<-0.047564,0.095304,0.102902>,
<-0.072122,0.103287,0.155669>,
<-0.030088,0.103287,0.109548>,
<-0.02558,0.094357,0.118136>,
<-0.023332,0.103287,0.108257>,
<-0.032336,0.094357,0.119427>,
<0.052624,0.103287,0.025859>,
<0.063151,0.095358,0.030913>,
<-0.0423,0.103287,0.191409>,
<-0.004498,0.100735,0.193674>,
<-0.041938,0.100735,0.189822>,
<0.045141,0.103287,0.191428>,
<0.003595,0.100735,0.194062>,
<0.0028,0.103287,0.195808>,
<-0.005597,0.100735,0.18753>,
<-0.040601,0.100735,0.183951>,
<0.043062,0.100735,0.18397>,
<0.003286,0.100735,0.188018>,
<-0.006819,0.091268,0.180067>,
<-0.038974,0.091268,0.176807>,
<0.041438,0.091268,0.176825>,
<0.002847,0.091268,0.180718>,
<-0.00821,0.091268,0.171701>,
<-0.037135,0.091268,0.168738>,
<0.039603,0.091268,0.168755>,
<0.002145,0.091268,0.172531>,
<-0.040597,0.103287,0.039806>,
<-0.042127,0.095358,0.05439>,
<-0.027685,0.103287,0.050133>,
<0.001968,0.103287,0.188487>,
<0.001074,0.103287,0.18062>,
<-0.006638,0.103287,0.172181>,
<-0.005352,0.103287,0.180603>,
<-0.004149,0.103287,0.188478>,
<-0.00303,0.103287,0.195807>,
<-0.005853,0.103287,0.093611>,
<0.004126,0.095304,0.074849>,
<-0.00142,0.103287,0.07358>,
<0.009082,0.095358,0.069251>,
<0.005734,0.103287,0.057783>,
<-0.005546,0.103287,0.113177>,
<0.000368,0.095304,0.095035>,
<0.000117,0.103287,0.172206>,
<0.000817,0.095304,0.113977>,
<-0.017803,0.103287,0.11236>,
<-0.020051,0.094357,0.122239>,
<-0.174668,0.103287,0.085536>,
<-0.185283,0.100735,0.049077>,
<-0.187683,0.103287,0.048116>,
<-0.187461,0.100735,0.041249>,
<-0.194146,0.103287,3.9E-5>,
<-0.188982,0.103287,0.042415>,
<-0.173206,0.100735,0.084829>,
<-0.179066,0.100735,0.048784>,
<-0.186434,0.100735,0.000412>,
<-0.192438,0.100735,0.000414>,
<-0.167797,0.100735,0.082215>,
<-0.17154,0.091268,0.048318>,
<-0.181517,0.100735,0.040207>,
<-0.179128,0.091268,0.00041>,
<-0.161215,0.091268,0.079034>,
<-0.163099,0.091268,0.047817>,
<-0.174323,0.091268,0.039011>,
<-0.170875,0.091268,0.000408>,
<-0.18168,0.103287,0.0416>,
<-0.173834,0.103287,0.040724>,
<-0.166209,0.091268,0.037876>,
<-0.163915,0.103287,0.046386>,
<-0.172387,0.103287,0.047003>,
<-0.18031,0.103287,0.04758>,
<-0.087717,0.103287,0.028134>,
<-0.0717,0.095304,0.014201>,
<-0.090486,0.095304,0.022368>,
<-0.069232,0.103287,0.019341>,
<-0.067361,0.095358,0.008109>,
<-0.106804,0.103287,0.032187>,
<-0.108997,0.095304,0.026144>,
<-0.165442,0.103287,0.039787>,
<-0.103282,0.103287,0.043991>,
<-0.107166,0.094357,0.052881>,
<-0.098064,0.103287,0.048484>,
<-0.112384,0.094357,0.048388>,
<-0.027686,0.103287,-0.049036>,
<-0.041522,0.095358,-0.053777>,
<-0.040598,0.103287,-0.038708>,
<-0.174708,0.103287,0.085454>,
<-0.152904,0.100735,0.11651>,
<-0.173245,0.100735,0.084749>,
<-0.120204,0.103287,0.154034>,
<-0.148161,0.100735,0.123098>,
<-0.150018,0.103287,0.123563>,
<-0.1488,0.100735,0.111817>,
<-0.167835,0.100735,0.082137>,
<-0.115687,0.100735,0.147753>,
<-0.143643,0.100735,0.119087>,
<-0.143745,0.091268,0.106206>,
<-0.161252,0.091268,0.078959>,
<-0.11113,0.091268,0.142025>,
<-0.138225,0.091268,0.114191>,
<-0.138091,0.091268,0.099899>,
<-0.153816,0.091268,0.075369>,
<-0.105983,0.091268,0.135555>,
<-0.132281,0.091268,0.108536>,
<-0.14483,0.103287,0.118346>,
<-0.139254,0.103287,0.112739>,
<-0.137485,0.103287,0.101431>,
<-0.143248,0.103287,0.10769>,
<-0.148637,0.103287,0.113544>,
<-0.153652,0.103287,0.118991>,
<-0.07575,0.103287,0.053058>,
<-0.054902,0.095304,0.049185>,
<-0.05737,0.103287,0.044046>,
<-0.047448,0.095358,0.049581>,
<-0.090809,0.103287,0.065499>,
<-0.072981,0.095304,0.058824>,
<-0.133292,0.103287,0.106743>,
<-0.087465,0.095304,0.070987>,
<-0.097815,0.103287,0.055378>,
<-0.106917,0.094357,0.059774>,
<-0.17471,0.103287,-0.084353>,
<-0.152907,0.100735,-0.115409>,
<-0.153655,0.103287,-0.11789>,
<-0.148164,0.100735,-0.121997>,
<-0.120208,0.103287,-0.152934>,
<-0.150021,0.103287,-0.122463>,
<-0.173247,0.100735,-0.083648>,
<-0.148803,0.100735,-0.110717>,
<-0.11569,0.100735,-0.146654>,
<-0.119435,0.100735,-0.151361>,
<-0.167837,0.100735,-0.081036>,
<-0.143747,0.091268,-0.105106>,
<-0.143646,0.100735,-0.117986>,
<-0.111133,0.091268,-0.140925>,
<-0.161254,0.091268,-0.077858>,
<-0.138094,0.091268,-0.098799>,
<-0.138228,0.091268,-0.113091>,
<-0.105987,0.091268,-0.134455>,
<-0.144833,0.103287,-0.117246>,
<-0.139257,0.103287,-0.111639>,
<-0.132284,0.091268,-0.107436>,
<-0.137487,0.103287,-0.10033>,
<-0.143251,0.103287,-0.10659>,
<-0.14864,0.103287,-0.112443>,
<-0.075751,0.103287,-0.05196>,
<-0.054904,0.095304,-0.048087>,
<-0.072982,0.095304,-0.057726>,
<-0.057371,0.103287,-0.042948>,
<-0.047449,0.095358,-0.048483>,
<-0.090811,0.103287,-0.0644>,
<-0.087467,0.095304,-0.069888>,
<-0.133295,0.103287,-0.105643>,
<-0.097816,0.103287,-0.054279>,
<-0.107168,0.094357,-0.051782>,
<-0.098065,0.103287,-0.047385>,
<-0.106919,0.094357,-0.058675>,
<0.013524,0.103287,0.056671>,
<0.016148,0.095358,0.068076>,
<-0.17467,0.103287,-0.084435>,
<-0.185284,0.100735,-0.047975>,
<-0.173208,0.100735,-0.083728>,
<-0.194146,0.103287,0.001062>,
<-0.187462,0.100735,-0.040148>,
<-0.188983,0.103287,-0.041314>,
<-0.179068,0.100735,-0.047683>,
<-0.167799,0.100735,-0.081114>,
<-0.186434,0.100735,0.000689>,
<-0.181518,0.100735,-0.039106>,
<-0.171542,0.091268,-0.047217>,
<-0.161217,0.091268,-0.077933>,
<-0.179128,0.091268,0.000691>,
<-0.174324,0.091268,-0.03791>,
<-0.1631,0.091268,-0.046716>,
<-0.153783,0.091268,-0.074341>,
<-0.170875,0.091268,0.000693>,
<-0.16621,0.091268,-0.036775>,
<-0.05547,0.103287,-0.007733>,
<-0.02147,0.103287,-0.010391>,
<-0.181681,0.103287,-0.040499>,
<-0.173835,0.103287,-0.039623>,
<-0.163916,0.103287,-0.045286>,
<-0.172389,0.103287,-0.045902>,
<-0.180311,0.103287,-0.046478>,
<-0.187684,0.103287,-0.047015>,
<-0.087718,0.103287,-0.027036>,
<-0.071701,0.095304,-0.013103>,
<-0.069233,0.103287,-0.018243>,
<-0.067362,0.095358,-0.007011>,
<-0.106804,0.103287,-0.031088>,
<-0.090486,0.095304,-0.021269>,
<-0.165443,0.103287,-0.038686>,
<-0.108998,0.095304,-0.025044>,
<-0.103283,0.103287,-0.042892>,
<-0.112386,0.094357,-0.047288>,
<-0.042305,0.103287,-0.190311>,
<-0.004503,0.100735,-0.192577>,
<-0.003035,0.103287,-0.19471>,
<0.00359,0.100735,-0.192965>,
<0.045136,0.103287,-0.190333>,
<0.002795,0.103287,-0.194712>,
<-0.041943,0.100735,-0.188724>,
<-0.005601,0.100735,-0.186433>,
<0.043057,0.100735,-0.182874>,
<0.044392,0.100735,-0.188745>,
<-0.040605,0.100735,-0.182853>,
<-0.006823,0.091268,-0.178971>,
<0.003281,0.100735,-0.186922>,
<0.041433,0.091268,-0.175729>,
<-0.038978,0.091268,-0.175709>,
<-0.008215,0.091268,-0.170605>,
<0.002843,0.091268,-0.179621>,
<0.039599,0.091268,-0.16766>,
<0.001963,0.103287,-0.187391>,
<0.001069,0.103287,-0.179523>,
<0.002141,0.091268,-0.171434>,
<-0.006642,0.103287,-0.171084>,
<-0.005356,0.103287,-0.179506>,
<-0.004154,0.103287,-0.187382>,
<-0.005856,0.103287,-0.092515>,
<0.004124,0.095304,-0.073753>,
<0.000365,0.095304,-0.093939>,
<-0.001421,0.103287,-0.072483>,
<0.00908,0.095358,-0.068154>,
<-0.005548,0.103287,-0.11208>,
<0.000814,0.095304,-0.11288>,
<0.000113,0.103287,-0.171109>,
<-0.017806,0.103287,-0.111263>,
<-0.025583,0.094357,-0.117039>,
<-0.023334,0.103287,-0.10716>,
<-0.020054,0.094357,-0.121142>,
<-0.067792,0.095358,0.00016>,
<-0.05547,0.103287,0.008831>,
<-0.042216,0.103287,-0.190331>,
<-0.077255,0.100735,-0.175921>,
<-0.041856,0.100735,-0.188744>,
<-0.121006,0.103287,-0.152296>,
<-0.084714,0.100735,-0.172749>,
<-0.084754,0.103287,-0.174668>,
<-0.073607,0.100735,-0.170864>,
<-0.040521,0.100735,-0.182873>,
<-0.115906,0.100735,-0.146481>,
<-0.081821,0.100735,-0.167438>,
<-0.069278,0.091268,-0.164672>,
<-0.038897,0.091268,-0.175728>,
<-0.111352,0.091268,-0.140751>,
<-0.078268,0.091268,-0.161052>,
<-0.064405,0.091268,-0.15774>,
<-0.037062,0.091268,-0.167658>,
<-0.106208,0.091268,-0.134278>,
<-0.074093,0.091268,-0.153981>,
<-0.080836,0.103287,-0.168434>,
<-0.076627,0.103287,-0.161735>,
<-0.066028,0.103287,-0.157488>,
<-0.070831,0.103287,-0.164517>,
<-0.075321,0.103287,-0.171089>,
<-0.0795,0.103287,-0.177204>,
<-0.032746,0.103287,-0.086358>,
<-0.03362,0.095304,-0.065112>,
<-0.028075,0.103287,-0.066381>,
<-0.035663,0.095358,-0.057911>,
<-0.041487,0.103287,-0.103852>,
<-0.038967,0.095304,-0.084934>,
<-0.072126,0.103287,-0.154571>,
<-0.047566,0.095304,-0.101804>,
<-0.03009,0.103287,-0.10845>,
<-0.032339,0.094357,-0.118329>,
<0.122844,0.103287,-0.152549>,
<0.14818,0.100735,-0.124319>,
<0.150758,0.103287,-0.124498>,
<0.153528,0.100735,-0.118215>,
<0.177379,0.103287,-0.083995>,
<0.154394,0.103287,-0.119927>,
<0.121832,0.100735,-0.151275>,
<0.142706,0.100735,-0.12135>,
<0.170269,0.100735,-0.080975>,
<0.175678,0.100735,-0.083589>,
<0.11809,0.100735,-0.146566>,
<0.136126,0.091268,-0.117655>,
<0.148624,0.100735,-0.114689>,
<0.163687,0.091268,-0.077794>,
<0.113536,0.091268,-0.140836>,
<0.128738,0.091268,-0.11353>,
<0.14266,0.091268,-0.110481>,
<0.156253,0.091268,-0.074201>,
<0.148168,0.103287,-0.116015>,
<0.141478,0.103287,-0.111811>,
<0.13584,0.091268,-0.105927>,
<0.130092,0.103287,-0.112596>,
<0.137459,0.103287,-0.116839>,
<0.144347,0.103287,-0.120806>,
<0.069336,0.103287,-0.062992>,
<0.060933,0.095304,-0.043468>,
<0.074325,0.095304,-0.059001>,
<0.056486,0.103287,-0.047025>,
<0.084779,0.103287,-0.07495>,
<0.08937,0.095304,-0.070459>,
<0.134323,0.103287,-0.107315>,
<0.0765,0.103287,-0.084052>,
<0.076154,0.094357,-0.093752>,
<0.069855,0.103287,-0.085829>,
<0.082799,0.094357,-0.091974>,
<0.122915,0.103287,-0.152492>,
<0.089836,0.100735,-0.170983>,
<0.121902,0.100735,-0.15122>,
<0.044141,0.103287,-0.19056>,
<0.082712,0.100735,-0.174854>,
<0.084184,0.103287,-0.176082>,
<0.088168,0.100735,-0.16497>,
<0.118158,0.100735,-0.146512>,
<0.042788,0.100735,-0.182936>,
<0.080377,0.100735,-0.169274>,
<0.086041,0.091268,-0.157714>,
<0.113601,0.091268,-0.140784>,
<0.041161,0.091268,-0.175792>,
<0.077613,0.091268,-0.162506>,
<0.083676,0.091268,-0.149571>,
<0.108454,0.091268,-0.134314>,
<0.039323,0.091268,-0.167723>,
<0.074705,0.091268,-0.154824>,
<0.081767,0.103287,-0.169123>,
<0.079169,0.103287,-0.161646>,
<0.082467,0.103287,-0.150687>,
<0.084951,0.103287,-0.158835>,
<0.087274,0.103287,-0.166454>,
<0.089436,0.103287,-0.173544>,
<0.047772,0.103287,-0.080239>,
<0.030665,0.095304,-0.067677>,
<0.035112,0.103287,-0.064121>,
<0.023777,0.095358,-0.06479>,
<0.055958,0.103287,-0.098001>,
<0.042783,0.095304,-0.084229>,
<0.076391,0.103287,-0.153649>,
<0.050571,0.095304,-0.101491>,
<0.066648,0.103287,-0.091931>,
<0.072947,0.094357,-0.099854>,
<-0.125825,0.103287,0.159465>,
<-0.080217,0.073436,0.164645>,
<-0.088145,0.103287,0.182718>,
<0.17564,-0.095496,0.041804>,
<0.160497,-0.090692,0.077002>,
<0.172551,-0.090692,0.041041>,
<-0.075288,0.073436,0.167025>,
<-0.082684,0.103287,0.185353>,
<-0.044024,0.103287,0.198975>,
<-0.003535,0.073436,0.18345>,
<-0.003207,0.103287,0.203547>,
<-0.040302,0.073436,0.179342>,
<-0.043932,0.103287,0.198996>,
<0.17945,0.073436,-0.036727>,
<0.178234,0.073436,-0.042077>,
<0.19813,0.103287,-0.046259>,
<0.04587,0.103287,0.199222>,
<0.078346,0.073436,0.165959>,
<0.087489,0.103287,0.184173>,
<0.001936,0.073436,0.183452>,
<0.045865,0.103287,-0.198127>,
<0.078342,0.073436,-0.165598>,
<0.041662,0.073436,-0.178979>,
<0.160463,0.103287,0.125804>,
<0.140816,0.073436,0.117553>,
<0.144228,0.073436,0.113264>,
<0.083275,0.073436,0.163577>,
<0.092948,0.103287,0.181534>,
<0.204115,0.103287,0.000501>,
<0.17945,0.073436,0.03709>,
<0.183637,0.073436,0.000139>,
<0.165796,0.073436,0.079546>,
<0.184353,0.103287,0.088457>,
<0.087484,0.103287,-0.183078>,
<0.083271,0.073436,-0.163216>,
<0.127671,0.103287,0.158727>,
<0.156684,0.103287,0.130555>,
<0.114689,0.073436,0.143595>,
<0.127594,0.103291,0.158546>,
<0.199477,0.103287,-0.040333>,
<0.183637,0.073436,0.000224>,
<0.092944,0.103287,-0.18044>,
<0.114686,0.073436,-0.143462>,
<0.156681,0.103287,-0.129463>,
<0.144225,0.073436,-0.112904>,
<0.140813,0.073436,-0.117193>,
<0.002848,0.103287,-0.202452>,
<0.001932,0.073436,-0.183088>,
<0.16046,0.103287,-0.124712>,
<0.165794,0.073436,-0.079187>,
<0.127667,0.103287,-0.158618>,
<0.114619,0.073436,-0.143515>,
<-0.088149,0.103287,-0.181619>,
<-0.113522,0.073436,-0.143861>,
<-0.125829,0.103287,-0.158365>,
<-0.003212,0.103287,-0.20245>,
<-0.003539,0.073436,-0.183087>,
<-0.156021,0.103287,-0.12733>,
<-0.141497,0.073436,-0.115268>,
<-0.075292,0.073436,-0.16666>,
<-0.080221,0.073436,-0.16428>,
<-0.044029,0.103287,-0.197877>,
<-0.040389,0.073436,-0.178959>,
<-0.196518,0.103287,-0.042985>,
<-0.182903,0.073436,0.000644>,
<-0.201885,0.103287,0.001061>,
<-0.159799,0.103287,-0.122577>,
<-0.144907,0.073436,-0.110977>,
<-0.201885,0.103287,4.1E-5>,
<-0.178056,0.073436,0.039488>,
<-0.196517,0.103287,0.044086>,
<-0.082689,0.103287,-0.184255>,
<-0.040306,0.073436,-0.178978>,
<-0.195168,0.103287,-0.04891>,
<-0.178057,0.073436,-0.03912>,
<-0.164664,0.073436,-0.079507>,
<-0.181683,0.103287,-0.087719>,
<-0.156018,0.103287,0.12843>,
<-0.113518,0.073436,0.144227>,
<-0.125031,0.103287,0.160101>,
<-0.176838,0.073436,0.044837>,
<-0.195167,0.103287,0.050012>,
<-0.164627,0.073436,-0.079584>,
<-0.176839,0.073436,-0.04447>,
<-0.159795,0.103287,0.123678>,
<-0.141494,0.073436,0.115635>,
<-0.164625,0.073436,0.079951>,
<-0.18164,0.103287,0.088906>,
<-0.181681,0.103287,0.088821>,
<-0.144904,0.073436,0.111344>,
<-0.076315,0.018754,-0.155737>,
<-0.107849,0.018754,-0.1364>,
<-0.156241,0.018754,0.075539>,
<0.173547,0.018753,4.1E-5>,
<0.169581,0.018753,-0.03495>,
<-0.071647,0.018754,-0.15799>,
<-0.107845,0.018754,0.136405>,
<0.040772,0.073436,0.179546>,
<0.073841,0.018754,0.156985>,
<-0.038517,0.018754,-0.169654>,
<-0.134337,0.018754,0.109329>,
<0.078508,0.018754,0.154729>,
<0.136225,0.018754,-0.107086>,
<0.132994,0.018754,-0.111148>,
<-0.164662,0.073436,0.079875>,
<-0.137566,0.018754,0.105266>,
<0.108256,0.018754,0.135807>,
<0.10819,0.018754,-0.136073>,
<-0.13434,0.018754,-0.109324>,
<0.132997,0.018754,0.111146>,
<0.136228,0.018754,0.107084>,
<0.03826,0.018754,0.169851>,
<-0.137569,0.018754,-0.105261>,
<0.073837,0.018754,-0.156984>,
<0.039103,0.018754,-0.169656>,
<-0.156278,0.018754,-0.07546>,
<-0.114236,0.073436,0.143653>,
<-0.076311,0.018754,0.15574>,
<0.078504,0.018754,-0.154729>,
<-0.173549,0.018754,0.000439>,
<-0.071643,0.018754,0.157994>,
<0.108253,0.018754,-0.136023>,
<-0.168961,0.018754,-0.037216>,
<-0.038513,0.018754,0.169657>,
<0.156652,0.018754,0.075155>,
<0.178234,0.073436,0.04244>,
<0.16843,0.018753,0.040018>,
<-0.156243,0.018754,-0.075533>,
<-0.167807,0.018754,-0.042282>,
<0.001484,0.018754,0.173549>,
<0.15665,0.018754,-0.075157>,
<0.169581,0.018753,0.034952>,
<0.00148,0.018754,-0.173547>,
<-0.040384,0.073436,0.179323>,
<-0.003696,0.018754,0.173548>,
<0.173547,0.018753,-3.9E-5>,
<-0.003701,0.018754,-0.173546>,
<-0.182903,0.073436,-0.000276>,
<-0.16896,0.018754,0.037222>,
<0.16843,0.018753,-0.040016>,
<-0.038595,0.018754,-0.169636>,
<-0.167806,0.018754,0.042288>,
<0.15665,-0.04001,-0.075157>,
<-0.038595,-0.04001,-0.169636>,
<-0.167806,-0.04001,0.042288>,
<0.16843,-0.04001,-0.040016>,
<-0.107849,-0.04001,-0.1364>,
<-0.156241,-0.04001,0.075539>,
<0.169581,-0.04001,-0.03495>,
<-0.076315,-0.04001,-0.155737>,
<-0.107845,-0.04001,0.136405>,
<0.073841,-0.04001,0.156985>,
<-0.071647,-0.04001,-0.15799>,
<-0.134337,-0.04001,0.109329>,
<0.078508,-0.04001,0.154729>,
<0.132994,-0.04001,-0.111148>,
<-0.156276,0.018754,0.075466>,
<-0.137566,-0.04001,0.105266>,
<0.108194,-0.04001,0.134986>,
<0.10819,-0.04001,-0.136073>,
<-0.13434,-0.04001,-0.109324>,
<0.136228,-0.04001,0.107084>,
<0.03826,-0.04001,0.169851>,
<-0.137569,-0.04001,-0.105261>,
<0.132997,-0.04001,0.111146>,
<0.039103,-0.04001,-0.169656>,
<-0.156278,-0.04001,-0.07546>,
<-0.108525,0.018754,0.135862>,
<-0.076311,-0.04001,0.15574>,
<0.073837,-0.04001,-0.156984>,
<-0.173549,-0.04001,0.000439>,
<-0.071643,-0.04001,0.157994>,
<0.078504,-0.04001,-0.154729>,
<-0.168961,-0.04001,-0.037216>,
<-0.038513,-0.04001,0.169657>,
<0.156652,-0.04001,0.075155>,
<0.16843,-0.04001,0.040018>,
<-0.167807,-0.04001,-0.042282>,
<0.001484,-0.04001,0.173549>,
<0.136225,-0.04001,-0.107086>,
<0.169581,-0.04001,0.034952>,
<0.00148,-0.04001,-0.173547>,
<-0.038591,0.018754,0.169639>,
<-0.003696,-0.04001,0.173548>,
<0.173547,-0.04001,-3.9E-5>,
<-0.003701,-0.04001,-0.173546>,
<-0.173549,0.018754,-0.000433>,
<-0.16896,-0.04001,0.037222>,
<0.192637,-0.095456,0.000307>,
<-0.003377,-0.095456,-0.19157>,
<-0.191208,-0.095456,-0.000128>,
<-0.186133,-0.095456,0.041514>,
<0.173951,-0.095456,-0.082764>,
<-0.041966,-0.095456,-0.187246>,
<-0.184857,-0.095456,0.047116>,
<0.186978,-0.095456,-0.043902>,
<-0.118552,-0.095456,-0.150491>,
<-0.172068,-0.095456,0.083888>,
<0.173547,-0.04001,4.1E-5>,
<0.188252,-0.095456,-0.038299>,
<-0.083679,-0.095456,-0.171875>,
<-0.147845,-0.095456,0.121255>,
<-0.118548,-0.095456,0.151198>,
<0.043027,-0.095456,0.188185>,
<0.082375,-0.095456,0.173956>,
<-0.038517,-0.04001,-0.169654>,
<-0.078517,-0.095456,-0.174367>,
<-0.151416,-0.095456,0.116762>,
<0.087536,-0.095456,0.171462>,
<0.147791,-0.095456,-0.122565>,
<-0.172107,-0.095456,0.083807>,
<0.120434,-0.095456,0.150536>,
<0.120361,-0.095456,-0.150129>,
<-0.147848,-0.095456,-0.120548>,
<0.147794,-0.095456,0.123265>,
<0.151367,-0.095456,0.118773>,
<0.002357,-0.095456,0.192274>,
<-0.151419,-0.095456,-0.116055>,
<0.043022,-0.095456,-0.187482>,
<-0.172109,-0.095456,-0.083099>,
<-0.119299,-0.095456,0.150597>,
<-0.083675,-0.095456,0.17258>,
<0.082371,-0.095456,-0.173255>,
<-0.186134,-0.095456,-0.040806>,
<-0.191208,-0.095456,0.000836>,
<-0.078512,-0.095456,0.175072>,
<0.108253,-0.04001,-0.136023>,
<0.087532,-0.095456,-0.17076>,
<-0.184858,-0.095456,-0.046408>,
<-0.041875,-0.095456,0.187971>,
<0.173953,-0.095456,0.083463>,
<0.186978,-0.095456,0.044605>,
<-0.156243,-0.04001,-0.075533>,
<-0.003372,-0.095456,0.192273>,
<0.151364,-0.095456,-0.118073>,
<0.188252,-0.095456,0.039003>,
<0.002352,-0.095456,-0.191571>,
<-0.041961,-0.095456,0.187951>,
<0.002346,-0.097898,0.192036>,
<0.173731,-0.097898,-0.082667>,
<0.151172,-0.097898,-0.117933>,
<0.186743,-0.097898,0.044547>,
<0.188015,-0.097898,0.038951>,
<0.002342,-0.097898,-0.191342>,
<-0.003376,-0.097898,0.192035>,
<0.192395,-0.097898,0.000303>,
<-0.003381,-0.097898,-0.19134>,
<-0.185915,-0.097898,0.04146>,
<0.186743,-0.097898,-0.043852>,
<-0.041923,-0.097898,-0.187022>,
<-0.18464,-0.097898,0.047055>,
<0.188015,-0.097898,-0.038257>,
<-0.083585,-0.097898,-0.17167>,
<-0.118416,-0.097898,-0.150312>,
<-0.171867,-0.097898,0.083782>,
<0.192395,-0.097898,0.000392>,
<-0.078429,-0.097898,-0.174159>,
<-0.118412,-0.097898,0.15101>,
<0.082267,-0.097898,0.173741>,
<-0.041836,-0.097898,-0.187042>,
<-0.147673,-0.097898,0.121104>,
<0.087422,-0.097898,0.171249>,
<0.147603,-0.097898,-0.12242>,
<-0.151239,-0.097898,0.116616>,
<0.120279,-0.097898,0.150349>,
<0.120206,-0.097898,-0.14995>,
<-0.147676,-0.097898,-0.120405>,
<0.147606,-0.097898,0.123111>,
<0.151175,-0.097898,0.118624>,
<0.042967,-0.097898,0.187952>,
<-0.151242,-0.097898,-0.115917>,
<0.082262,-0.097898,-0.173048>,
<0.042962,-0.097898,-0.187258>,
<-0.171907,-0.097898,-0.083002>,
<-0.083581,-0.097898,0.172366>,
<0.087417,-0.097898,-0.170557>,
<-0.190984,-0.097898,0.000831>,
<-0.078425,-0.097898,0.174855>,
<0.120276,-0.097898,-0.149895>,
<-0.185916,-0.097898,-0.04076>,
<-0.041832,-0.097898,0.187738>,
<0.173733,-0.097898,0.083358>,
<-0.171869,-0.097898,-0.083082>,
<-0.184641,-0.097898,-0.046355>,
<0.19813,0.103287,0.047356>,
<0.042202,-0.10143,0.185001>,
<0.085971,-0.10143,0.168556>,
<0.166963,-0.101713,-0.079684>,
<0.143377,-0.09917,-0.112264>,
<0.141815,-0.101713,-0.117944>,
<0.145226,-0.10143,0.121161>,
<-0.149006,-0.10143,0.114766>,
<-0.116685,-0.10143,0.148629>,
<-0.169355,-0.10143,-0.08177>,
<0.085967,-0.10143,-0.167973>,
<0.042197,-0.10143,-0.184417>,
<-0.116689,-0.10143,-0.148041>,
<-0.149009,-0.10143,-0.114178>,
<0.118318,-0.10143,-0.147631>,
<-0.041287,-0.10143,0.18479>,
<0.141818,-0.101713,0.118368>,
<0.14338,-0.09917,0.112603>,
<0.166965,-0.101713,0.080107>,
<-0.169317,-0.10143,-0.08185>,
<-0.181892,-0.10143,-0.045689>,
<-0.117424,-0.10143,0.148038>,
<-0.077316,-0.10143,0.172107>,
<-0.188137,-0.10143,-0.000179>,
<-0.181891,-0.10143,0.046279>,
<-0.041918,-0.097898,0.187718>,
<-0.003425,-0.10143,0.189021>,
<0.185011,-0.10143,0.0383>,
<0.189323,-0.10143,0.000249>,
<-0.07732,-0.10143,-0.17152>,
<0.040519,-0.09917,0.178505>,
<0.077877,-0.09917,0.164996>,
<0.041107,-0.101713,0.180775>,
<-0.00343,-0.10143,-0.188436>,
<-0.041377,-0.10143,-0.184185>,
<-0.188137,-0.10143,0.000769>,
<-0.169315,-0.10143,0.082439>,
<0.189323,-0.10143,0.000336>,
<0.185011,-0.10143,-0.037716>,
<0.043119,-0.10143,-0.184206>,
<-0.18406,-0.101713,-0.000247>,
<-0.177052,-0.09917,0.039252>,
<-0.177954,-0.101713,0.045167>,
<-0.041292,-0.10143,-0.184205>,
<-0.171905,-0.097898,0.083701>,
<0.118322,-0.10143,0.147979>,
<0.145223,-0.10143,-0.120579>,
<0.118249,-0.10143,-0.147685>,
<-0.165661,-0.101713,0.080515>,
<-0.075727,-0.101713,0.16817>,
<0.083893,-0.101713,0.164699>,
<0.170948,-0.10143,-0.081441>,
<-0.0035,-0.101713,-0.184278>,
<0.115514,-0.101713,-0.144388>,
<0.083889,-0.101713,-0.164274>,
<-0.1657,-0.101713,-0.080007>,
<0.17095,-0.10143,0.082021>,
<-0.169353,-0.10143,0.08236>,
<-0.165698,-0.101713,0.080438>,
<-0.145808,-0.101713,0.112117>,
<-0.040512,-0.101713,-0.180141>,
<-0.075731,-0.101713,-0.167741>,
<-0.140621,-0.090692,-0.107643>,
<-0.147364,-0.064382,-0.071159>,
<-0.159768,-0.090692,-0.077145>,
<-0.165663,-0.101713,-0.080084>,
<-0.177956,-0.101713,-0.044735>,
<-0.040093,-0.09917,-0.177958>,
<-0.074878,-0.09917,-0.165711>,
<0.042003,-0.101713,-0.180142>,
<0.001902,-0.09917,-0.182045>,
<-0.114935,-0.101713,0.144642>,
<-0.079775,-0.09917,0.16369>,
<-0.14409,-0.09917,0.110694>,
<0.115518,-0.101713,0.144584>,
<0.082777,-0.09917,0.162628>,
<0.113945,-0.09917,0.141898>,
<0.115447,-0.101713,-0.144442>,
<0.139985,-0.09917,-0.116529>,
<0.113942,-0.09917,-0.142699>,
<0.158008,-0.064383,0.037443>,
<0.17373,-0.090692,0.035856>,
<0.139988,-0.09917,0.116868>,
<-0.140699,-0.09917,0.114961>,
<-0.114213,-0.101713,0.14522>,
<-0.145811,-0.101713,-0.111686>,
<-0.163736,-0.09917,-0.079059>,
<0.077873,-0.09917,-0.164655>,
<0.041103,-0.101713,-0.180348>,
<-0.114216,-0.101713,-0.14479>,
<-0.140702,-0.09917,-0.114614>,
<0.114008,-0.09917,-0.142646>,
<0.082773,-0.09917,-0.162287>,
<-0.040089,-0.09917,0.178302>,
<-0.040507,-0.101713,0.180569>,
<0.080522,-0.090692,0.158441>,
<0.101418,-0.064383,0.127528>,
<0.110903,-0.090692,0.138235>,
<-0.1637,-0.09917,-0.079136>,
<-0.175841,-0.09917,-0.044223>,
<-0.040591,-0.101713,0.18055>,
<-0.003533,-0.09917,0.182387>,
<-0.003495,-0.101713,0.184705>,
<0.184925,-0.101713,0.000171>,
<0.178399,-0.09917,0.036868>,
<0.182563,-0.09917,0.000129>,
<-0.079779,-0.09917,-0.163345>,
<-0.114938,-0.101713,-0.144212>,
<0.159091,-0.064383,0.032678>,
<0.177788,-0.090692,4.5E-5>,
<-0.040595,-0.101713,-0.180122>,
<-0.003538,-0.09917,-0.182044>,
<-0.040175,-0.09917,-0.177939>,
<-0.177053,-0.09917,-0.038905>,
<-0.18406,-0.101713,0.000679>,
<-0.163698,-0.09917,0.079482>,
<0.18071,-0.101713,-0.036941>,
<0.182563,-0.09917,0.000214>,
<0.178399,-0.09917,-0.036525>,
<0.180968,-0.095496,0.000185>,
<0.176839,-0.095496,-0.036244>,
<-0.074302,-0.095496,-0.164341>,
<-0.079162,-0.095496,-0.161995>,
<-0.111988,-0.095496,0.142152>,
<-0.112884,-0.09917,0.143388>,
<0.077165,-0.095496,0.163577>,
<-0.03981,-0.095496,-0.176484>,
<-0.139568,-0.095496,0.113964>,
<0.082024,-0.095496,0.161229>,
<0.142113,-0.095496,-0.111345>,
<0.138749,-0.095496,-0.115573>,
<-0.163734,-0.09917,0.079406>,
<-0.14293,-0.095496,0.109734>,
<0.112929,-0.095496,0.140675>,
<0.112926,-0.095496,-0.141523>,
<-0.111991,-0.095496,-0.141864>,
<-0.139571,-0.095496,-0.113675>,
<0.138752,-0.095496,0.115855>,
<0.142116,-0.095496,0.111627>,
<0.001906,-0.09917,0.182388>,
<0.040122,-0.095496,0.176972>,
<-0.144093,-0.09917,-0.110348>,
<-0.142933,-0.095496,-0.109445>,
<0.040515,-0.09917,-0.178163>,
<0.077161,-0.095496,-0.163294>,
<0.040118,-0.095496,-0.176688>,
<-0.162411,-0.095496,-0.07842>,
<-0.113598,-0.09917,0.142818>,
<-0.079157,-0.095496,0.162282>,
<0.08202,-0.095496,-0.160946>,
<-0.180392,-0.095496,0.000599>,
<-0.18187,-0.09917,0.000631>,
<-0.074297,-0.095496,0.164628>,
<-0.074874,-0.09917,0.166056>,
<0.112991,-0.095496,-0.14147>,
<-0.175615,-0.095496,-0.038604>,
<-0.039806,-0.095496,0.176771>,
<0.163378,-0.095496,0.078385>,
<0.164824,-0.09917,0.07908>,
<0.17719,-0.09917,0.042187>,
<-0.162374,-0.095496,-0.078495>,
<-0.174413,-0.095496,-0.043877>,
<0.001835,-0.095496,0.180822>,
<0.163377,-0.095496,-0.078104>,
<0.176839,-0.095496,0.03653>,
<0.041,-0.095496,-0.176486>,
<0.00183,-0.095496,-0.180537>,
<-0.040171,-0.09917,0.178283>,
<-0.003559,-0.095496,0.180821>,
<0.180968,-0.095496,0.000101>,
<-0.003563,-0.095496,-0.180536>,
<-0.18187,-0.09917,-0.000284>,
<-0.175614,-0.095496,0.038894>,
<0.164822,-0.09917,-0.078741>,
<0.17564,-0.095496,-0.041518>,
<-0.039892,-0.095496,-0.176466>,
<-0.174412,-0.095496,0.044167>,
<-0.17584,-0.09917,0.04457>,
<0.17719,-0.09917,-0.041844>,
<-0.113601,-0.09917,-0.142472>,
<-0.112699,-0.095496,-0.141298>,
<-0.162372,-0.095496,0.078785>,
<0.17373,-0.090692,-0.035684>,
<0.172551,-0.090692,-0.040869>,
<-0.159733,-0.090692,-0.077219>,
<-0.171568,-0.090692,-0.043188>,
<0.160495,-0.090692,-0.076834>,
<0.139592,-0.090692,-0.109511>,
<0.040193,-0.090692,-0.173548>,
<0.001688,-0.090692,-0.17753>,
<-0.039888,-0.095496,0.176752>,
<-0.00361,-0.090692,0.177701>,
<-0.003614,-0.090692,-0.177529>,
<-0.180392,-0.095496,-0.000309>,
<-0.172748,-0.090692,0.03818>,
<0.001692,-0.090692,0.177702>,
<-0.039327,-0.090692,-0.173528>,
<-0.171566,-0.090692,0.043364>,
<-0.077931,-0.090692,-0.159302>,
<-0.110899,-0.090692,-0.138957>,
<-0.159731,-0.090692,0.077395>,
<0.177788,-0.090692,0.000127>,
<-0.073153,-0.090692,-0.161609>,
<-0.1102,-0.090692,0.139687>,
<0.075745,-0.090692,0.160749>,
<-0.039247,-0.090692,-0.173546>,
<-0.137313,-0.090692,0.111977>,
<0.136285,-0.090692,-0.113668>,
<-0.162409,-0.095496,0.078709>,
<-0.140618,-0.090692,0.107819>,
<0.1109,-0.090692,-0.139178>,
<-0.110204,-0.090692,-0.139513>,
<-0.137316,-0.090692,-0.111802>,
<0.136288,-0.090692,0.113836>,
<0.139595,-0.090692,0.109679>,
<0.075741,-0.090692,-0.16058>,
<0.039326,-0.090692,-0.173746>,
<-0.112695,-0.095496,0.141586>,
<-0.077927,-0.090692,0.159476>,
<0.080518,-0.090692,-0.158271>,
<-0.177444,-0.090692,0.000534>,
<-0.073149,-0.090692,0.161782>,
<0.110964,-0.090692,-0.139126>,
<-0.172748,-0.090692,-0.038004>,
<-0.039242,-0.090692,0.173719>,
<0.101415,-0.064383,-0.128115>,
<-0.036611,-0.064383,0.159362>,
<-0.072162,-0.064383,-0.146655>,
<0.124683,-0.064383,-0.104721>,
<0.146931,-0.064383,0.070488>,
<-0.101818,-0.064382,-0.12847>,
<0.039331,-0.090692,0.173917>,
<0.069052,-0.064383,0.147444>,
<0.03559,-0.064383,0.159544>,
<0.127724,-0.064383,0.100516>,
<-0.101815,-0.064382,0.12809>,
<0.14693,-0.064383,-0.070874>,
<-0.126729,-0.064382,0.102627>,
<-0.036615,-0.064383,-0.159744>,
<0.18071,-0.101713,0.037368>,
<-0.159766,-0.090692,0.07732>,
<-0.129766,-0.064382,0.098806>,
<-0.003872,-0.064383,-0.163403>,
<0.073441,-0.064383,0.145323>,
<-0.177444,-0.090692,-0.000358>,
<-0.15929,-0.064382,0.034814>,
<-0.067772,-0.064383,-0.148774>,
<0.159091,-0.064383,-0.03306>,
<-0.158205,-0.064382,0.039578>,
<0.001,-0.064383,-0.163404>,
<0.158008,-0.064383,-0.037825>,
<-0.147329,-0.064382,0.070849>,
<0.069048,-0.064383,-0.147828>,
<0.124685,-0.064383,0.104335>,
<0.001004,-0.064383,0.163022>,
<0.036383,-0.064383,-0.159745>,
<0.16282,-0.064383,-0.000153>,
<-0.039322,-0.090692,0.173701>,
<-0.003868,-0.064383,0.163021>,
<-0.163606,-0.064382,0.000221>,
<0.127721,-0.064383,-0.100901>,
<-0.159291,-0.064382,-0.035192>,
<-0.110895,-0.090692,0.139131>,
<-0.072159,-0.064383,0.146274>,
<-0.147331,-0.064382,-0.071227>,
<0.073438,-0.064383,-0.145707>,
<-0.067768,-0.064383,0.148393>,
<-0.126732,-0.064382,-0.103006>,
<-0.00658,-0.064383,-0.003224>,
<-0.129769,-0.064382,-0.099185>,
<-0.102457,-0.064382,-0.127959>,
<-0.102454,-0.064382,0.127579>,
<-0.163606,-0.064382,-0.000599>,
<0.173191,0.091268,0.000508>,
<0.055799,0.103287,0.017738>,
<0.175562,0.100735,-0.083832>,
<0.023716,0.103287,0.011981>,
<0.022177,0.103287,0.014504>,
<0.021834,0.103287,0.054098>,
<0.019727,0.103287,0.017584>,
<0.016852,0.103287,0.02026>,
<0.010072,0.103287,0.024187>,
<0.006318,0.103287,0.025349>,
<0.013611,0.103287,0.022477>,
<0.048633,0.103287,-0.031566>,
<0.17568,0.100735,0.084681>,
<-0.037058,0.091268,0.168756>,
<0.044397,0.100735,0.189841>,
<-0.153781,0.091268,0.075441>,
<-0.119431,0.100735,0.152461>,
<0.02515,0.103287,0.008313>,
<0.026023,0.103287,0.004472>,
<0.026316,0.103287,0.000548>,
<0.026023,0.103287,-0.003375>,
<0.024142,0.103287,-0.009996>,
<0.02515,0.103287,-0.007217>,
<0.184351,0.103287,-0.087365>,
<0.204115,0.103287,0.000596>,
<0.199477,0.103287,0.04143>,
<-0.153817,0.091268,-0.074269>,
<-0.192438,0.100735,0.000688>,
<-0.125035,0.103287,-0.159001>,
<-0.181642,0.103287,-0.087804>,
<-0.01706,0.103287,-0.016879>,
<-0.014181,0.103287,-0.01956>,
<-0.043937,0.103287,-0.197898>,
<0.046856,0.103287,-0.1979>,
<-0.004416,0.103287,-0.024032>,
<0.007276,0.103287,-0.024033>,
<-0.019509,0.103287,-0.013798>,
<0.127741,0.103287,-0.158559>,
<0.002853,0.103287,0.203548>,
<0.003392,0.103287,0.025716>,
<-0.01495,0.103287,0.020042>,
<-0.004415,0.103287,0.025129>,
<-0.000536,0.103287,0.025715>,
<0.022177,0.103287,-0.013408>,
<-0.008163,0.103287,0.023969>,
<0.019727,0.103287,-0.016488>,
<0.016851,0.103287,-0.019164>,
<-0.011703,0.103287,0.022261>,
<0.01361,0.103287,-0.02138>,
<-0.01706,0.103287,0.017976>,
<-0.019509,0.103287,0.014895>,
<0.010071,0.103287,-0.023091>,
<-0.02147,0.103287,0.011488>,
<0.003392,0.103287,-0.02462>,
<-0.000537,0.103287,-0.024619>,
<-0.022903,0.103287,0.007826>,
<-0.023778,0.103287,0.003984>,
<-0.008164,0.103287,-0.022873>,
<-0.024073,0.103287,5.5E-5>,
<-0.011704,0.103287,-0.021164>,
<-0.023778,0.103287,-0.002887>,
<-0.022903,0.103287,-0.006728>,
<-0.03714,0.091268,-0.16764>,
<-0.119649,0.100735,-0.15119>,
<0.108392,0.091268,-0.134363>,
<0.044125,0.100735,-0.188807>,
<-0.156276,-0.04001,0.075466>,
<-0.108525,-0.04001,0.135862>,
<-0.038591,-0.04001,0.169639>,
<-0.173549,-0.04001,-0.000433>,
<0.192637,-0.095456,0.000396>,
<-0.041879,-0.095456,-0.187266>,
<0.12043,-0.095456,-0.150074>,
<-0.17207,-0.095456,-0.08318>,
<0.043898,-0.097898,-0.187044>,
<-0.190984,-0.097898,-0.000131>,
<-0.119162,-0.097898,0.15041>,
<-0.041372,-0.10143,0.184771>,
<0.184925,-0.101713,0.000257>,
<0.041404,-0.09917,-0.17796>,
<-0.112888,-0.09917,-0.143043>,
<0.16282,-0.064383,-0.000229>,
<0.101356,-0.064383,-0.128162>,
<-0.147362,-0.064382,0.07078>,
<-0.036689,-0.064383,-0.159727>,
<0.035586,-0.064383,-0.159927>,
<-0.036685,-0.064383,0.159345>,
<-0.158206,-0.064382,-0.039956>
}
normal_vectors{
1269,
<-0.789409512441938,0.607307318097275,-0.0895010784944938>,
<-0.795263502219079,0.543275066962939,-0.269087650505664>,
<0,1,0>,
<-0.786997993157676,0.609298446290943,-0.0968997529059451>,
<-0.787705143731383,0.608303972237908,-0.0974006360282299>,
<-0.786785586220089,0.609588832434884,-0.0967982266727309>,
<-0.752034985481306,0.60862831404777,-0.253011770381344>,
<-0.751581924672065,0.609485342053783,-0.252293932403888>,
<-0.752353641396882,0.60816252617967,-0.253184399257962>,
<-0.309894826349061,0.361693961569717,0.879285320454111>,
<-0.448513556527122,0.140104234714492,0.882726680817148>,
<-0.302808101739145,0.145203884981915,0.941925201545906>,
<0.378015706878938,0.121905065260695,-0.917738132811644>,
<0.230693801340836,0.120996748861037,-0.965474058060585>,
<0.336809525108057,0.405411465198356,-0.8498240333635>,
<-0.031101084047177,0.625221792485372,0.779827181350117>,
<0.412196574660697,0.529695598247868,0.741293839873786>,
<-0.18890050058699,0.615701631611486,0.765002027258058>,
<-0.191993928288025,0.621680339670132,0.7593759851142>,
<-0.192704730959218,0.623115297668339,0.758018609585301>,
<-0.248209972035944,0.617924825628564,0.746029972356221>,
<0.636224100625406,0.719827267573353,-0.277610516085528>,
<0.363699194407177,0.411499088530528,-0.83569814893065>,
<0.120505557241911,0.841638813068818,-0.526424276615287>,
<-0.839356925307861,0.533772607492657,0.102794724710088>,
<-0.79538664557033,0.543090881580647,0.269095481924787>,
<-0.795226743925105,0.543318272100741,0.269109050289544>,
<-0.795183535895332,0.543388749252419,0.269094428457538>,
<-0.787909387996287,0.608007244449477,0.0976011629247844>,
<-0.751216583289116,0.610013466195901,0.252105565291781>,
<-0.309991580742999,0.36139018477587,-0.879376116469011>,
<-0.292999589800861,0.471799339481387,-0.831598835762445>,
<-0.302903327411328,0.145101593949764,-0.941910346941992>,
<0.378015706878938,0.121905065260695,0.917738132811644>,
<0.230691008993133,0.121095280403418,0.965462371837322>,
<0.336809525108057,0.405411465198356,0.8498240333635>,
<-0.00480001449606567,0.581401755835954,-0.813602457083131>,
<0.143399457951073,0.701197349479028,-0.698397360062968>,
<-0.188200054578024,0.614300178147077,-0.766300222227097>,
<-0.248209972035944,0.617924825628564,-0.746029972356221>,
<0.636224100625406,0.719827267573353,0.277610516085528>,
<0.363699194407177,0.411499088530528,0.83569814893065>,
<-0.422608374067899,0.608112049859653,-0.672013316075788>,
<-0.285298445127711,0.543997035224237,-0.789095699440157>,
<-0.415203934075913,0.610005779832146,-0.674906394768386>,
<-0.415210164469241,0.609214913763636,-0.675616539295325>,
<-0.415211981114575,0.61031761096875,-0.67461946642556>,
<-0.271005928319524,0.609513333249999,-0.745016297409761>,
<-0.271111249994733,0.610425330124621,-0.744230882501217>,
<-0.270898539860805,0.609096716977543,-0.745395982326483>,
<-0.880925630904102,0.361010503753412,0.306008903458571>,
<-0.969675632357535,0.139896484445518,0.200394964137826>,
<-0.925320236974876,0.144903169066962,0.350407663499402>,
<0.953175975502302,0.121596935187872,-0.276893020999357>,
<0.8989273907349,0.120803680944238,-0.421112831503467>,
<0.874659757830292,0.404781376437295,-0.266687729979809>,
<-0.63018668744684,0.624486807855525,0.46139025323385>,
<-0.32438230704958,0.529771104423142,0.783657256580628>,
<-0.716579914546489,0.614882765077639,0.329290770109069>,
<-0.714105819986149,0.620805059581854,0.323502636557232>,
<-0.713585086227545,0.622186996427661,0.321993270410972>,
<-0.738710940390041,0.617009137973001,0.271304018042261>,
<0.614397609997946,0.71989719960534,0.322898743926329>,
<0.880386882333179,0.410793879216799,-0.236996468778923>,
<0.492901091777127,0.827001831811086,-0.27040059893799>,
<-0.605790271086369,0.53179145949774,-0.591790495920953>,
<-0.707209727736708,0.543307473245692,-0.452406222890394>,
<-0.707203292038987,0.543402529544663,-0.452302105471201>,
<-0.568003106985493,0.609103331804337,-0.553503027669842>,
<-0.666300769577833,0.610200704782221,-0.428600495033858>,
<0.496109140895132,0.361306657136487,-0.789514546939542>,
<0.469107679355567,0.471707721918612,-0.746612222142116>,
<0.549429062818937,0.145107675673512,-0.822843525459449>,
<-0.483819931372618,0.121905021980823,0.866635701793118>,
<-0.61259608552352,0.120899227456405,0.78109500881884>,
<-0.456207443085151,0.405306612631328,0.792212925059309>,
<0.634281878825579,0.580683410111956,-0.510385418496887>,
<0.636312490936797,0.700413749256848,-0.323306346565875>,
<0.483205061599531,0.614006431751059,-0.624106537550221>,
<0.430093578750804,0.617690777945527,-0.658390170308135>,
<0.179102399092704,0.720609652630946,0.669808972151273>,
<0.179199190021492,0.720596742910083,0.669796972524526>,
<-0.42841752906181,0.41151683755587,0.804432914046031>,
<0.263611096942694,0.608325608005465,-0.748631514306906>,
<0.440696348845875,0.54379549467299,-0.714194082926534>,
<0.270483866118565,0.610063610790892,-0.744755576654739>,
<0.270998573196268,0.609296792060835,-0.745196076552985>,
<0.270200197246216,0.610500445665488,-0.744500543485595>,
<0.415210164469241,0.609214913763636,-0.675616539295325>,
<0.414409643424605,0.610314202176729,-0.675115710125364>,
<0.415503463235799,0.608805074411443,-0.675805632863425>,
<-0.789637757432079,0.361017262453116,-0.496123722722967>,
<-0.762517988011508,0.140103305075951,-0.631614899971238>,
<-0.851983552616274,0.144997200856056,-0.503090287935736>,
<0.812103467689211,0.121600519235326,0.570702436904608>,
<0.890767460858971,0.120895583764986,0.438083997083874>,
<0.75488880508203,0.405093992500636,0.515792350856155>,
<-0.754344677386567,0.624136965606465,-0.203512053358301>,
<-0.815389591618294,0.52889324872077,0.235396995176535>,
<-0.705036803881769,0.614832095073066,-0.353418448924563>,
<-0.698874131652766,0.620877018662473,-0.355086856703244>,
<-0.697398500594836,0.622298662059315,-0.355499235677465>,
<-0.673491807022001,0.617192491899004,-0.4067950513683>,
<0.130103163496881,0.720617522028075,0.681016559118955>,
<0.735299889705025,0.410999938350014,0.538899919165018>,
<0.519405136942207,0.826908178162323,0.215502131326618>,
<0.0864982479957301,0.534889165929665,-0.840482976189724>,
<-0.086305059782449,0.544131900667792,-0.834548926865048>,
<-0.0862978572508064,0.544086490500159,-0.8345792776538>,
<0.0799981744624879,0.609186098531845,-0.788981995636287>,
<-0.0795017884128457,0.611013744908789,-0.78761771765984>,
<-0.337509414956447,0.841423471835125,0.42201177218258>,
<0.926715999889858,0.360706227646781,-0.105301818051583>,
<0.876494123126606,0.470996841976762,-0.0995993321887165>,
<0.985861971107933,0.144694418520456,-0.0844967406010958>,
<-0.979135636289986,0.121604425873621,0.162805925429486>,
<-0.992643870865331,0.120705334690153,0.00940041546054212>,
<-0.904017556191414,0.404607857560892,0.13800268003807>,
<0.79509039139068,0.580392985993146,0.175997873078556>,
<0.650202743861369,0.70040295570671,0.294401242375864>,
<0.789770506571138,0.613277097594427,-0.0123995369479389>,
<0.783424987738482,0.6169196769669,-0.0752023986187565>,
<-0.413403999703046,0.720306969003638,0.557005389053209>,
<-0.89629217092208,0.410696412582504,0.167298538653647>,
<0.750297805382129,0.607398223362795,-0.26099923657835>,
<0.833453723766696,0.543169841331817,-0.10159435912981>,
<0.751388755551409,0.609390880533709,-0.253096212443521>,
<0.752213389517501,0.608410829809157,-0.253004503520244>,
<0.7511910458561,0.609592733697921,-0.253196981909963>,
<0.787439778525144,0.608630745758703,-0.0975049255857271>,
<0.786726580006516,0.609520593001108,-0.0977033009617854>,
<0.787876844639799,0.608082128728851,-0.0973971375401909>,
<-0.103404100053852,0.361714342258011,-0.926536737910002>,
<0.0200001390014491,0.140300975095165,-0.989906879876723>,
<-0.137094279175093,0.145193941183249,-0.979859111332408>,
<0.0587992332629973,0.121998409151117,0.99078708022071>,
<0.21229420550874,0.121096694710826,0.969673533122115>,
<0.0663025987812898,0.405615898426714,0.911635732262803>,
<-0.311202606332742,0.625005234440758,-0.715905995737822>,
<-0.69328537529926,0.529288834769794,-0.489089682761962>,
<-0.162700099247091,0.615700375577344,-0.77100047031043>,
<-0.157604374370117,0.621717256001914,-0.767221294522548>,
<-0.15629591604107,0.62308371903513,-0.766379974752886>,
<-0.101096092711524,0.617976115684684,-0.779669866341988>,
<-0.452703209677135,0.720205106272305,0.525703727252639>,
<0.0360002655029371,0.411703036321089,0.910606715749293>,
<-0.337598021681389,0.84139506943934,-0.421997527101737>,
<0.711899850501047,0.534099887839035,-0.45599990424003>,
<0.599979013101176,0.543580985869666,-0.586979467817317>,
<0.600011625337862,0.543510530618547,-0.587011373455542>,
<0.667735664714304,0.608332491906112,-0.42902291472583>,
<0.567493544797642,0.610493055680987,-0.55249371541973>,
<0.661190287186021,0.361294692619948,0.657490341537824>,
<0.625306984718029,0.471405265626226,0.621906946739393>,
<0.68201662776808,0.145003535229284,0.716817476223108>,
<-0.739105192232213,0.121700854951509,-0.662504654111543>,
<-0.627378023332757,0.120895765095522,-0.76927305283693>,
<-0.672699397934308,0.404899637614986,-0.619299445727244>,
<0.358303835663091,0.58110622077539,0.730707822269106>,
<0.174703584080791,0.701214385560681,0.691214180404368>,
<0.502811740791223,0.613914335067088,0.608514208972671>,
<0.547886845394761,0.617485174358943,0.564386449244028>,
<-0.693916869324142,0.719617494113926,0.0251006102032512>,
<-0.690708388704321,0.411104992900458,-0.594907225192125>,
<0.672590805746528,0.607691692911336,0.422294227277369>,
<0.599917604840425,0.543715955578828,0.586917223338632>,
<0.66728360170698,0.609485022089621,0.428089479830298>,
<0.667575003122003,0.608677208508633,0.428783944485793>,
<0.667095577170985,0.609895956403213,0.427797163714207>,
<0.568006259463469,0.609006711290938,0.553606100772846>,
<0.56781382927422,0.609814852221591,0.552913466371462>,
<0.568083380963781,0.6086821932629,0.553883796366552>,
<0.661194574920769,0.361097037210964,-0.657594604458406>,
<0.787198346885207,0.139999706000926,-0.600598738743973>,
<0.68201662776808,0.145003535229284,-0.716817476223108>,
<-0.739105192232213,0.121700854951509,0.662504654111543>,
<-0.627378023332757,0.120895765095522,0.76927305283693>,
<-0.672699397934308,0.404899637614986,0.619299445727244>,
<0.367278423026452,0.624963284485523,-0.688859530691323>,
<-0.0488022691142549,0.529924639418928,-0.846639365412464>,
<0.502776427879755,0.615271153688173,-0.607171533429967>,
<0.503109108872875,0.621311248941994,-0.600710875968865>,
<0.50312376309651,0.622729412204724,-0.599228302221087>,
<0.547886845394761,0.617485174358943,-0.564386449244028>,
<-0.693866934559082,0.719665705147962,-0.0250988039449963>,
<-0.690614996867487,0.411108927327286,0.595012920845866>,
<0.154897959232831,0.827489097902952,0.539692889593019>,
<0.801086069234383,0.53389071572118,0.270595294388745>,
<0.833574776408893,0.542983569565774,0.101596925723541>,
<0.83349899146683,0.543099342850193,0.101599877064223>,
<0.833507455757538,0.543104858094684,0.101500907929682>,
<0.752499898412521,0.608099917906517,0.252899965858507>,
<0.786404962230968,0.609903848505426,0.0979006177548471>,
<-0.103404100053852,0.361714342258011,0.926536737910002>,
<-0.0977963190158271,0.47198223492301,0.876167021693943>,
<-0.137094279175093,0.145193941183249,0.979859111332408>,
<0.058799950314063,0.121899896994631,-0.990799162775061>,
<0.21229420550874,0.121096694710826,-0.969673533122115>,
<0.066401493718402,0.405409119780725,-0.91172050938354>,
<-0.349609677329805,0.581216088283989,0.734820340108525>,
<-0.43289159549126,0.700886392422785,0.566888993957023>,
<-0.163897884911443,0.614392071321479,0.771790040113798>,
<-0.101004991790044,0.618030543824232,0.779638530688303>,
<-0.452703209677135,0.720205106272305,-0.525703727252639>,
<-0.452670606788441,0.720253231874783,-0.525665866995104>,
<-0.452591097620664,0.720285832117022,-0.525689659786087>,
<0.0360017475672414,0.411619980518793,-0.910644203742501>,
<0.0883994338034397,0.608396103235438,0.788694948425033>,
<-0.0863028678919509,0.544318087990601,0.834427728494134>,
<0.0804980700819044,0.61028536858368,0.788081105981974>,
<0.080100408112619,0.609403104916729,0.788804018966715>,
<0.0806017103864418,0.610612957344434,0.787816717648125>,
<-0.0801018443662,0.609514034222209,0.788718160444719>,
<-0.0795969331892452,0.610676471088845,0.787869643967416>,
<-0.0803025721325788,0.609219513613537,0.788925269681088>,
<0.926682569264807,0.360793213543479,0.105298019362883>,
<0.960889516752558,0.139898473715978,0.238997392552671>,
<0.985844891469223,0.144806593918385,0.084503847970328>,
<-0.97911969029496,0.121602445449767,-0.162903276017821>,
<-0.992643870865331,0.120705334690153,-0.00940041546054212>,
<-0.904017556191414,0.404607857560892,-0.13800268003807>,
<0.768127875866432,0.624122649821951,-0.143005189752506>,
<0.632692189463131,0.529393464678018,-0.565193022735201>,
<0.788716665759223,0.614612986909622,0.0133002810379075>,
<0.783919668791238,0.620615571440034,0.0175004390915253>,
<0.782797729889875,0.621998196207846,0.0185999460602346>,
<0.783469496208986,0.616875982401179,0.0750970761522589>,
<-0.413403999703046,0.720306969003638,-0.557005389053209>,
<-0.89629217092208,0.410696412582504,-0.167298538653647>,
<-0.541126510437135,0.840941198718512,0>,
<0.28780529854432,0.534709844098846,0.79451462714893>,
<0.440727824025824,0.54383433334523,0.714145085402407>,
<0.440703853972055,0.543904756467893,0.714106244886417>,
<0.271213217898269,0.60902968178483,0.745336324850959>,
<0.41411587957786,0.61072341863849,0.674925880529093>,
<-0.789616388658222,0.360807488637141,0.496310301027198>,
<-0.746771309597417,0.471381889855681,0.46918197437481>,
<-0.851995901909568,0.144899303036029,0.503097580106459>,
<0.812023061782437,0.121603453587124,-0.570816211410609>,
<0.890788981008457,0.120698506968703,-0.438094580803553>,
<0.754984579597441,0.404791732213303,-0.515889463065324>,
<-0.793011796138202,0.580308632155105,0.185402757886535>,
<-0.71378341186626,0.700183727919243,0.0159996281729618>,
<-0.706402751444075,0.613502389596461,0.353001374943033>,
<-0.673491807022001,0.617192491899004,0.4067950513683>,
<0.130004851871609,0.720626894297552,-0.681025416342816>,
<0.735299889705025,0.410999938350014,-0.538899919165018>,
<-0.562800489636639,0.60790052887369,0.560100487287636>,
<-0.70717130357074,0.543377950170164,0.4523816427254>,
<-0.567289692439932,0.609688922052928,0.553589941362147>,
<-0.567910205438088,0.608910942227949,0.553809952054258>,
<-0.566979231931095,0.610077653264834,0.553479726408926>,
<-0.667507549553078,0.608806885644815,0.428704848679258>,
<-0.666781900450965,0.609683450367357,0.4285883661267>,
<-0.667807733258327,0.608407045394379,0.428804965590252>,
<0.496109140895132,0.361306657136487,0.789514546939542>,
<0.41258456756287,0.14019475611322,0.900066333648423>,
<0.549429062818937,0.145107675673512,0.822843525459449>,
<-0.483896520796523,0.121899123548453,-0.866593769213199>,
<-0.612636526478595,0.121007214665214,-0.781046567384563>,
<-0.456207443085151,0.405306612631328,-0.792212925059309>,
<0.591501354539653,0.624601430338913,0.509901167675011>,
<0.836969631107893,0.528880809907962,0.140594898606654>,
<0.48180565161344,0.615307217595994,0.623907318475769>,
<0.475471936097183,0.621363325112071,0.622763242484387>,
<0.474014789492152,0.622819432269435,0.622419419788851>,
<0.430093578750804,0.617690777945527,0.658390170308135>,
<0.179102399092704,0.720609652630946,-0.669808972151273>,
<-0.428482334037544,0.411383039026944,-0.804466832516229>,
<-0.443413950022305,0.534616819309707,0.719422633392075>,
<-0.285405825192339,0.543911101338869,0.789116106024088>,
<-0.285289910900698,0.544180755387873,0.788972098495096>,
<-0.415227138056441,0.608839792024954,0.675944177775405>,
<-0.271109155510775,0.610920631138076,0.74382511939843>,
<-0.880979821388284,0.360891733869503,-0.3059929913108>,
<-0.833283276172469,0.470990547314572,-0.289494189909912>,
<-0.925367029760084,0.144894837488909,-0.350287519478018>,
<0.953202363944794,0.121600301569122,0.276800686466554>,
<0.8989273907349,0.120803680944238,0.421112831503467>,
<0.874659757830292,0.404781376437295,0.266687729979809>,
<-0.640266390098005,0.580669518553664,-0.502873602343099>,
<-0.458003606792605,0.700805518865192,-0.546904306888375>,
<-0.71722447211649,0.613420930279218,-0.330611280649347>,
<-0.73868539633407,0.617087800294781,-0.271194638534994>,
<0.614397609997946,0.71989719960534,-0.322898743926329>,
<0.880423045374823,0.410710750494593,0.237006203718575>,
<-0.439401834506489,-0.54770228666182,0.712002972618616>,
<-0.439377768047391,-0.54777228342367,0.711963975534234>,
<-0.791308886448692,-0.550906186711215,-0.265202978246169>,
<-0.362617739693749,-0.551226966682831,0.751436761185558>,
<-0.3627914309676,-0.551186981117257,0.751382252560789>,
<-0.362590491189051,-0.551185545348607,0.751480292687733>,
<-0.0926008852686948,-0.555405309700141,0.826407900497293>,
<-0.277000168970155,-0.551700336537308,0.786700479887439>,
<0.805977976952665,-0.56288461938791,-0.183194994265172>,
<0.281502530719127,-0.561105044357023,0.778406997910367>,
<0.281486735250145,-0.561173555319294,0.778363320492762>,
<-0.000200000746004174,-0.558502083216656,0.829503094052311>,
<-0.000300001111506177,-0.558502069254,0.82950307331458>,
<0.287192411040806,-0.545485585733843,-0.787379193779703>,
<0.645002483264341,-0.566302180267591,0.513101975446408>,
<0.359490716272127,-0.563785440428999,0.743580797273863>,
<0.359403637183213,-0.563805705742613,0.743607525346235>,
<0.820514256559063,-0.564009799755407,0.0930016159171149>,
<0.69508814537277,-0.564890365876964,0.444692415835521>,
<0.36301236078133,-0.551118765913474,-0.751325583071663>,
<0.36309918121227,-0.551098757273704,-0.751298305824231>,
<0.575297252962176,-0.563897307396786,0.592497170832764>,
<0.462289704922906,-0.548687780859179,0.696584487236202>,
<0.821604958400886,-0.562403394114725,-0.0931005618635863>,
<0.44410328415593,-0.551404077648231,-0.70620522240693>,
<0.649603787201119,-0.557703251419434,-0.516703012387344>,
<0.086302552435733,-0.544016089513775,-0.834624684390067>,
<0.699392348689558,-0.557393902144066,-0.447395105524318>,
<0.58979771748725,-0.554697853323461,-0.586897728710185>,
<-0.442811440181338,-0.534313804175449,-0.720018601920874>,
<-0.441802354812827,-0.535302853171811,-0.719903837097678>,
<-0.444404364072283,-0.533005234137099,-0.720007070504149>,
<-0.000299988791128225,-0.544179667106601,-0.838968652521937>,
<-0.000199992532418257,-0.544179680710076,-0.838968673494586>,
<-9.99962677089604E-5,-0.544179688872162,-0.838968686078177>,
<-0.604681394239744,-0.533283591116348,-0.591581797308141>,
<-0.366401978576026,-0.538202906303541,-0.759004098633199>,
<-0.366488549244166,-0.538183184729086,-0.758976286156403>,
<-0.0936025900194993,-0.541114972858451,-0.835723124778798>,
<-0.840016128464501,-0.532810230054627,-0.102401966136625>,
<-0.840045562611165,-0.532765475252028,-0.102393364613002>,
<-0.661699649299279,-0.534399716768225,-0.525899721273222>,
<-0.661734447483269,-0.534427820364302,-0.525827372656344>,
<-0.838893431490147,-0.534595814131163,0.102299199000408>,
<-0.838902009172718,-0.5346012803716,0.102200244769879>,
<-0.279903915883175,-0.53800752677795,-0.795111123682431>,
<-0.824299118000416,-0.534099428513917,-0.187799799054323>,
<-0.824314594619098,-0.534109456491642,-0.187703323316759>,
<-0.71689933328393,-0.532499504775691,-0.449999581500584>,
<-0.600011625337862,-0.543510530618547,0.587011373455542>,
<-0.599979013101176,-0.543580985869666,0.586979467817317>,
<-0.822276425672809,-0.537384593404557,0.187294630339921>,
<-0.799582785167951,-0.532288539951101,-0.278094012700359>,
<-0.657074157781552,-0.543578621473218,0.522279459152799>,
<-0.657108473468398,-0.543607009857588,0.522206733899251>,
<-0.796623974759286,-0.537216167763857,0.277108339700977>,
<-0.712600826617438,-0.540400626865091,0.447400518984903>,
<-0.515283547259,-0.16809463282406,-0.840373167313145>,
<-0.930923990220346,-0.168904352721255,0.323808344648564>,
<0.97740615767819,-0.180001134010716,-0.110800698046597>,
<-0.428514786157799,-0.169305841998869,-0.887530624772571>,
<-0.428395114181584,-0.169298069166532,-0.887589877095177>,
<-0.704177044202532,-0.17159440611354,0.688977539698303>,
<0.334488351645977,-0.17939375272134,0.925167781592999>,
<0.334519298975053,-0.179410350481688,0.925153373637732>,
<-0.327314714763761,-0.169207606898956,-0.929641792986227>,
<-0.771301288074227,-0.171600286572718,0.612901023545564>,
<0.427981229154925,-0.180592079405092,0.885561160139256>,
<0.428019130742582,-0.180608072458202,0.885539580076067>,
<0.770120943723851,-0.177804835468252,-0.61261666033662>,
<-0.834527418848777,-0.170205592196599,0.524017216868495>,
<0.527920982636441,-0.180307166450749,0.829932986342076>,
<0.697703478060507,-0.176500879859079,-0.694303461111381>,
<-0.704711212044576,-0.167202660215486,-0.689510970206805>,
<0.76951141578653,-0.181802697063017,0.612209082189102>,
<0.769599303512945,-0.181799835471223,0.612099446050252>,
<0.0984971106296389,-0.178094775666383,0.979071279365274>,
<-0.771824922629134,-0.167705415295291,-0.613319804416232>,
<-0.771777584045602,-0.1676951293657,-0.61338218457317>,
<0.694289981467851,-0.1808973896695,0.696589948279569>,
<0.337499402626586,-0.173499692905815,-0.925198362400348>,
<-0.835042338142191,-0.16688847591418,-0.524263798213328>,
<-0.835086112633419,-0.166897224522234,-0.524191282771451>,
<-0.517206462535124,-0.173402166673609,0.838110472255777>,
<0.428400252756224,-0.174900103191091,-0.886500523035463>,
<-0.978712145893096,-0.16700207250858,-0.11930148054056>,
<-0.428109810248704,-0.175004010262843,0.886620317137353>,
<0.524092647031743,-0.17499754480167,-0.833488306241094>,
<-0.961265803577296,-0.167494041505458,-0.218892213048029>,
<-0.961237157340489,-0.167506475087944,-0.219008465935879>,
<-0.961286840073239,-0.167497706972087,-0.218797004689509>,
<-0.326893079746752,-0.175196291133775,0.9286803400453>,
<0.828498562556241,-0.181099685792318,0.529899080625893>,
<0.932483210790933,-0.180996741183012,0.312594371789003>,
<-0.931275256345154,-0.166795568300625,-0.323891394319978>,
<-0.000399997720019494,-0.178198984268685,0.983994391247955>,
<-0.000199998872009543,-0.178198994960503,0.98399445028695>,
<0.828984920901432,-0.177696767725192,-0.530290354106187>,
<0.958924846064647,-0.181604705438878,0.217905646008433>,
<0.1013009654028,-0.171801637277405,-0.979909338580495>,
<-0.109602739554713,-0.176804419281691,0.97812444852614>,
<-0.10950393673729,-0.176806356302766,0.978135164591262>,
<0.977278294890103,-0.1806959867867,0.110797539213981>,
<-0.000200002834060238,-0.171902435874775,-0.985113959163703>,
<-0.97859327708728,-0.167698847912872,0.119299180417446>,
<0.932828857507067,-0.179005537621961,-0.312709673823392>,
<-0.109701585747883,-0.170602466076471,-0.979214154642909>,
<-0.961037649387387,-0.168906617046337,0.218808571993715>,
<-0.96101661612094,-0.16890292035674,0.218903784879161>,
<0.959170899196371,-0.180194532980803,-0.217993386180993>,
<0.959191803741055,-0.180198460210736,-0.217898138068365>,
<0.948151936444767,0,-0.317817408925374>,
<-0.111397304217856,0,-0.993775950912978>,
<-0.975022713418667,0,0.222105174000293>,
<0.97513580276826,0,-0.2216081364921>,
<-0.522780111208996,0,-0.852467568488274>,
<-0.944503541894923,0,0.328501231881929>,
<0.993639885505522,0,-0.112604520036153>,
<-0.434690339114569,0,-0.900579984832253>,
<-0.714764477088211,0,0.699365242411157>,
<0.340008133091816,0,0.94042249517513>,
<-0.33211384113275,0,-0.943239310317403>,
<-0.782874459137399,0,0.622179701718342>,
<0.435213245916712,0,0.900327401881471>,
<0.782612126668855,0,-0.622509645861695>,
<-0.846923502453294,0,0.531714755289192>,
<0.55371237837858,-0.0122002727401457,0.832618613397156>,
<0.708804749007728,0,-0.705404726227499>,
<-0.714814464417033,0,-0.699314150765013>,
<0.782612126668855,0,0.622509645861695>,
<0.100097747325544,0,0.994977608280886>,
<-0.782874459137399,0,-0.622179701718342>,
<0.69296881710483,0,0.720967557189873>,
<0.342714454286921,0,-0.939439621701586>,
<-0.846923502453294,0,-0.531714755289192>,
<-0.525118114261783,0,0.851029356764001>,
<0.435093001585354,0,-0.900385517415428>,
<-0.992665207694195,0,-0.120895762677776>,
<-0.434810589940879,0,0.900521932478752>,
<0.532280079790764,0,-0.84656831777355>,
<-0.975022713418667,0,-0.222105174000293>,
<-0.332082516315762,0,0.943250339176929>,
<0.842378532256655,0,0.538886266658489>,
<0.948162036352081,0,0.317787276052195>,
<-0.944503541894923,0,-0.328501231881929>,
<-0.000199999996,0,0.999999980000001>,
<0.842378532256655,0,-0.538886266658489>,
<0.97513580276826,0,0.2216081364921>,
<0.102899151085505,0,-0.994691793826551>,
<-0.111298545893997,0,0.993787016257448>,
<0.993639885505522,0,0.112604520036153>,
<-0.000199999996,0,-0.999999980000001>,
<-0.992653202467878,0,0.120994295858379>,
<0.939935365733466,0.324312202476182,0.106504007288663>,
<-0.000200005615236473,0.309108678347969,-0.951026700449428>,
<-0.946308620910805,0.302002751257596,0.115301050397354>,
<0.897825229243418,0.321409031720689,-0.301008458456526>,
<-0.106005306758488,0.306815359561359,-0.945847350303564>,
<-0.928850502875518,0.304083795806271,0.21158872473728>,
<0.922743949621119,0.323380356856816,-0.209687262934058>,
<0.922709739252697,0.323403413541045,-0.209802214474061>,
<-0.498203293134651,0.302702000866839,-0.81250537067825>,
<-0.899816894220785,0.304005707760745,0.312905874862951>,
<0.940337021797337,0.323112721198255,-0.106604197089861>,
<0.940348597248859,0.323082339186629,-0.10659417318878>,
<-0.414000414000621,0.304600304600457,-0.857800857801287>,
<-0.414106172298497,0.30470454165504,-0.857712784304326>,
<-0.679882751593385,0.308692168579023,0.665183124518194>,
<0.321899660396037,0.322099660185038,0.890299060734986>,
<-0.316308001112084,0.304607705149354,-0.898422725890281>,
<-0.74461783753395,0.308707395174228,0.59181417707842>,
<0.411714288792343,0.324111248476071,0.851729559787318>,
<0.411596170115456,0.324096984291592,0.851792074111625>,
<0.741611521024469,0.319404961994627,-0.589909164310052>,
<-0.806193320716007,0.306297462336037,0.506195806185119>,
<0.505806370671358,0.33460421436662,0.795110014473699>,
<0.672190024774048,0.317195292856781,-0.668990072260991>,
<-0.681601574501456,0.30110069554341,-0.666901540544338>,
<0.739808322890449,0.326103668686909,0.588506620736725>,
<0.0947996994854289,0.319898985921822,0.942697011655209>,
<-0.746346681713107,0.301918884107178,-0.593137098920064>,
<-0.746302410560679,0.301900975141725,-0.593201916045283>,
<0.655584341555001,0.323992261537249,0.682083708625177>,
<0.323504415865415,0.311704254792117,-0.893412195159696>,
<0.325294050426226,0.3104943211108,-0.893183663820182>,
<0.325800441459897,0.310200420321854,-0.89310121015296>,
<-0.807803433171887,0.300501277133142,-0.507102155188739>,
<-0.498986477649681,0.311791550563468,0.808578087830726>,
<0.413014775867915,0.314411248263614,-0.85473057853343>,
<0.413180645006016,0.314385272966824,-0.85465996439168>,
<-0.946710158254497,0.300703226562932,-0.11540123826193>,
<-0.412800592369275,0.314500451308471,0.85480122664064>,
<-0.412612351735627,0.314409411986624,0.854925592580676>,
<0.505281251886967,0.314588327416663,-0.803570184081469>,
<-0.92961024436134,0.301603323686941,-0.211802334074582>,
<-0.929629929917398,0.301609710480946,-0.211706816010664>,
<-0.315198082025506,0.314798084459484,0.895294552149225>,
<0.796680859972256,0.324992192156374,0.509587757301194>,
<0.896733049705582,0.3248119711658,0.300611079225491>,
<-0.900891955070762,0.300397317463932,-0.313297202268476>,
<-0.00020000332008267,0.320005312132273,0.947415727231609>,
<0.798305580175508,0.319202231231394,-0.51070356983043>,
<0.92194473567918,0.325780471674921,0.209487442651614>,
<0.921921116845025,0.325707460414822,0.209704803343531>,
<0.0956019020187612,0.310606179571414,-0.945718815262996>,
<0.097501880829422,0.309405968498699,-0.945918246938977>,
<0.0977988435355127,0.309096344957331,-0.945988813748416>,
<-0.105503729622764,0.317611227755355,0.942333312071383>,
<-0.000199996887072679,-0.0969984902302493,0.995284508517186>,
<0.838448992005946,-0.0967056506762583,-0.536331338755712>,
<0.838494889389224,-0.0964994118378773,-0.536296731281384>,
<0.838478690334874,-0.096697542463187,-0.53628637045509>,
<0.970344690253838,-0.0988045505483657,0.220610160434914>,
<0.970354273432312,-0.098705520754168,0.220612339193206>,
<0.970335097655235,-0.0989035774070934,0.220607979534932>,
<0.0996043124620641,-0.0933040396858492,-0.990642890812457>,
<-0.110801611067137,-0.0958013929623806,0.9892143832817>,
<-0.110797356406614,-0.0961977047501466,0.989176398532693>,
<0.988832093066379,-0.0982031872361635,0.112103638382627>,
<-0.000300000129000083,-0.093700040291026,-0.995600428108276>,
<-0.000200003835110308,-0.0935017929140688,-0.995619091179112>,
<-0.000299997316536005,-0.0937991609702577,-0.99559109447749>,
<-0.988550842477277,-0.090804670204286,0.120506197793133>,
<-0.988541860691405,-0.0909038494050063,0.120505102896626>,
<0.943622453763434,-0.0976023224748953,-0.316307526627145>,
<0.943650043088713,-0.0973051602294741,-0.316316774723357>,
<-0.11089986969273,-0.092699891077692,-0.989498837339549>,
<-0.970916112486581,-0.0916015201398402,0.221203670905378>,
<-0.970907213867399,-0.0917006813385935,0.221201643534317>,
<-0.970919777837314,-0.0918018700231387,0.221104503944618>,
<0.970436426014888,-0.0979036748834064,-0.220608280687226>,
<0.970426920016147,-0.0980027186331228,-0.220606119698642>,
<-0.520607025639217,-0.0912012307689139,-0.848911456137401>,
<-0.520602275036913,-0.0913003989836153,-0.848903709717317>,
<-0.940526546736428,-0.0917025883420845,0.327109232788395>,
<-0.940535167267368,-0.0916034251160988,0.327112230954977>,
<0.988863397898728,-0.0978963764326883,-0.11209585084887>,
<-0.432917552998037,-0.0919037263121265,-0.896736358912773>,
<-0.432913572053229,-0.0920028843356366,-0.896728112867014>,
<-0.711686897964812,-0.0931982842353807,0.696287181470983>,
<0.3384086075324,-0.0976024825507159,0.935923805524744>,
<0.338503754027448,-0.0974010801839689,0.93591037930366>,
<-0.330684691306566,-0.0918957457849211,-0.939256518125967>,
<-0.779393172545712,-0.0929991853307047,0.619594572375319>,
<-0.779458927493945,-0.0932950839450739,0.619467358027581>,
<-0.779426949152658,-0.0932032225571308,0.619521420323418>,
<0.433096704146622,-0.0980992534675217,0.895993181517833>,
<0.433006951982422,-0.0983015782445082,0.896014385626443>,
<0.433002693285128,-0.0984006120537105,0.896005573171998>,
<0.778915730362014,-0.0968019549352201,-0.619612513201057>,
<-0.843281266714737,-0.0922979496238234,0.529488237549453>,
<-0.843286891207164,-0.0927985574576364,0.529391770668887>,
<0.534099428513917,-0.0981998949261686,0.839699101522442>,
<0.70551822024331,-0.0963024870438423,-0.702118132434908>,
<-0.711837368883545,-0.090904772171276,-0.696436560396882>,
<-0.711800718919089,-0.0907000916071388,-0.696500703466066>,
<0.778805140130887,-0.0990006534064687,0.619404088080472>,
<0.0995988421701896,-0.0969988723946626,0.990288487963241>,
<0.0995998077725565,-0.0968998129835414,0.990298088726533>,
<-0.779607979328503,-0.0911009324228151,-0.619606341703361>,
<-0.779593759376934,-0.0912992691522756,-0.619595040161555>,
<-0.779600873153467,-0.0912001021441716,-0.619600693953166>,
<0.702510098655253,-0.0986014174055629,0.704810131718466>,
<0.702517022193674,-0.0985023867417465,0.7048170779247>,
<0.338512469336466,-0.09420347004873,-0.936234486832495>,
<-0.843470116383155,-0.0905967902125831,-0.529481240811951>,
<-0.843425956833239,-0.0908027944990017,-0.529516296114773>,
<-0.5228153396271,-0.094202763949642,0.847224857942003>,
<-0.522805479030131,-0.0944009893275523,0.847208878795575>,
<0.43310165878253,-0.0952003646180947,-0.896303432848722>,
<0.43309753351657,-0.0952994572711362,-0.896294895615104>,
<-0.988580001228861,-0.0904981692405542,-0.12049756235897>,
<-0.988588942694513,-0.0903989888929638,-0.120498652230112>,
<-0.988532869264364,-0.0910030259009176,-0.120504006824841>,
<-0.432699009120404,-0.0951997819927489,0.896497947022052>,
<-0.432703126291381,-0.0951006871049465,0.896506477282698>,
<0.529909988897437,-0.095001790800635,-0.84271588534416>,
<-0.97097502684346,-0.0909976595702934,-0.221194310955482>,
<-0.970983857527552,-0.0908984888251848,-0.221196322641704>,
<-0.970957337071893,-0.0911959929361036,-0.221190281112567>,
<-0.330586710681341,-0.0951961731907551,0.93896225447604>,
<-0.330611450925908,-0.0954033043506703,0.938932520491031>,
<0.838308680731334,-0.098601021018859,0.536205552437243>,
<0.943557757864749,-0.0982955994045198,0.316285840199894>,
<0.943518917743953,-0.0986019769894581,0.316306342005736>,
<-0.940623863930146,-0.0907023011465705,-0.327108298842814>,
<0.782502492274407,-0.564701798578093,0.262300835429491>,
<0.24820279850233,-0.708607989600124,0.660507447263452>,
<-0.680234322088603,-0.580829306482006,0.447122560137922>,
<0.533589837878305,-0.654787529690244,0.53528980550273>,
<0.533511924124758,-0.654914637505724,0.535211962121032>,
<0.533476983632051,-0.654971741853783,0.535176910290297>,
<-0.52052033972967,-0.694427135270476,0.496819413597887>,
<-0.662191378324381,-0.623391883490515,-0.415794586389728>,
<-0.662218909930963,-0.623417801949505,-0.415711870821959>,
<-0.66217762539605,-0.623478933002775,-0.415685954208907>,
<0.251697338314721,-0.698492613479668,-0.669892915919871>,
<-0.526009928531102,-0.686512958054376,-0.502009475518276>,
<0.408008966095546,-0.642214112810195,-0.648914260047549>,
<-0.254495874655307,-0.642389586949193,0.722888282075921>,
<-0.254497920760481,-0.642494750839328,0.722794094796368>,
<-0.254585284122965,-0.642162880847478,0.72305820482842>,
<-0.674383154119212,-0.590585247364779,-0.443188929278818>,
<-0.738798729267279,-0.622998928442765,-0.25699955796114>,
<-0.368296644832848,-0.698293638573929,0.61379440835841>,
<-0.719513516188353,-0.687712918808521,0.0967018165606863>,
<-0.0850003204518122,-0.646102435810775,0.758502859561171>,
<0.751139668733386,-0.654634572164658,0.0851044944870339>,
<-0.372313558045079,-0.686825011188182,-0.624222731484658>,
<-0.250504355056069,-0.676311757782114,-0.69271204290355>,
<-0.0862965598562106,-0.631974806826479,-0.770169297812902>,
<-0.720963574235594,-0.686165332372351,-0.0968951044985147>,
<-0.734929772608145,-0.628125445877229,0.255610354985225>,
<-0.734883608603913,-0.628185988467789,0.255594299032739>,
<0.752472930273241,-0.653076505995288,-0.085296931498083>,
<0.065003325655213,-0.695235569161601,-0.715836623138484>,
<0.826606753404763,-0.551704507444239,-0.111100907698124>,
<-0.258195206650482,-0.628988322940176,-0.733286386664596>,
<-0.656898420161199,-0.631198481969476,0.412399008181578>,
<-0.656800101804024,-0.631300097851523,0.412400063922015>,
<0.406115605291956,-0.653725119870356,0.63852453577669>,
<0.406199723784282,-0.653599555552453,0.638599565752443>,
<0.541375733376615,-0.645471067407841,-0.538775849913779>,
<-0.0650016055594862,-0.997624641632976,0.0228005631808659>,
<-0.0653004851844074,-0.99760741225061,0.0227001686628798>,
<-0.0650017534459491,-0.997626911348905,0.0227006123572776>,
<-0.0347010641244473,-0.997730595877841,0.0577017694518908>,
<-0.0347014639121334,-0.997742090637909,0.0575024257909991>,
<-0.0218999648505846,-0.997798398534856,0.0625998995272419>,
<0.0228995691486597,-0.997881225041375,0.0608988541988373>,
<0.0227000901195367,-0.997903961686592,0.0606002405834327>,
<0.0549021395780698,-0.997838886539127,-0.0361014068992408>,
<0.0548026338778778,-0.9978479577253,-0.0360017302847372>,
<0.0040000220001815,-0.998005489045285,-0.0630003465028586>,
<-0.00480016011201085,-0.998033289955589,-0.0625020847918079>,
<-0.00759982391411984,-0.99767688409439,-0.0676984314455149>,
<0.0269001466061985,-0.998205440234474,-0.0535002915773836>,
<0.0354991826407294,-0.997777026448446,-0.056398701434849>,
<0.0270000743853074,-0.998202750052365,-0.0535001473931091>,
<-0.0590980034076817,-0.997566297791934,-0.0369987500183456>,
<-0.0593027700970884,-0.997546596489808,-0.0372017377337553>,
<0.061401152510449,-0.997918731110376,0.0197003697794112>,
<0.061102269685962,-0.997937069060908,0.0197007317972742>,
<-0.0581025095015792,-0.99764308913555,0.0365015765371367>,
<-0.0578980274478067,-0.997666010098044,0.036198766729026>,
<-0.0293006151728734,-0.997620945271621,-0.0624013101292594>,
<-0.0229003643476952,-0.997615872194791,-0.0651010357657186>,
<-0.0294007554621175,-0.998025644598411,-0.0555014261274668>,
<0.751324482307141,-0.461515038712559,0.471715371095805>,
<-0.068998352339019,-0.997576178165295,-0.00899978508769813>,
<-0.0661031578232756,-0.997547653989674,-0.0230010987887343>,
<-0.0608013181868676,-0.998021637343649,-0.0160003468912809>,
<0.251211102520019,-0.653928901026435,0.713631539642857>,
<-0.0748013995472784,-0.563510543380901,0.82271539314901>,
<0.062298478066771,-0.704882779924027,0.706582738394549>,
<0.423183121677781,-0.568777314769192,-0.705271870792389>,
<0.638996124500257,-0.656296019576712,-0.401197566744137>,
<0.639038062240514,-0.656239086764046,-0.401223897607033>,
<-0.350707542046788,-0.773916643256372,-0.5273113399523>,
<-0.526099605425444,-0.670199497350565,0.523499607375442>,
<0.592078821719314,-0.705674758465326,-0.389186079062923>,
<0.673573929825559,-0.706672648764434,-0.216591616983694>,
<-0.847396373151284,-0.494797882269596,-0.192599175677292>,
<-0.847380048671628,-0.494788350345435,-0.192695463038734>,
<-0.508985972539893,-0.678581298557114,-0.529585404827362>,
<0.597888708978345,-0.562989368046176,-0.570589224524241>,
<0.644708887373269,-0.648508939757352,0.404705578904858>,
<0.644566824999254,-0.648666613988545,0.404679171699035>,
<0.644724599936425,-0.648424741118005,0.40481544602802>,
<-0.289195682340694,-0.568891506513211,0.769888505650415>,
<0.604190774077319,-0.549891603219328,0.576691193992701>,
<-0.396608763167434,-0.666914735643878,0.630813937987941>,
<0.247397963923135,-0.667094509834776,-0.702694216850393>,
<-0.487609708405942,-0.474109439612915,-0.733114596456925>,
<0.719072179635544,-0.648374914859807,0.250090324192532>,
<0.587086808307624,-0.711684008639987,0.385791331366175>,
<0.0826009226574592,-0.670707491844526,-0.737108233544953>,
<-0.729481566233729,-0.678982842320359,-0.0826979102502116>,
<0.428800023584002,-0.552700030398502,0.714600039303003>,
<-0.86456214597514,-0.492878419790824,-0.097995709351797>,
<0.0839004677464116,-0.65700366280563,0.749204176824929>,
<0.828186161124871,-0.54929082142706,0.111298140223615>,
<0.715312686182985,-0.653011581263091,-0.248804412585386>,
<0.715234925774159,-0.653131893209037,-0.248712144910561>,
<-0.730729196868353,-0.677627075130691,0.0828033084722863>,
<-0.912658649329269,-0.395425410853378,0.10340664512453>,
<0.403107082653664,-0.37450658013842,0.835014671336664>,
<0.661406280082444,-0.379203600555281,-0.64710614430201>,
<0.661413625261022,-0.379007807641257,-0.647213332731983>,
<-0.312492304971734,-0.394390288258726,-0.864178719861032>,
<-0.312504628227816,-0.39430583971273,-0.864212799086332>,
<0.307914945014543,-0.374418172827038,0.874642451801622>,
<0.724407791047689,-0.379204078361794,-0.575706191753389>,
<-0.39941714135444,-0.396617021184704,-0.826535471530907>,
<-0.720221646586887,-0.391211758046085,0.572917219285792>,
<-0.720104068599481,-0.391302210863737,0.573003237477438>,
<0.784627901864294,-0.376413385497987,-0.492617517790404>,
<-0.508596630558484,-0.395497379838538,-0.764794933250351>,
<-0.508521147291624,-0.395716456211004,-0.764731802033245>,
<-0.508710983188692,-0.395308534803401,-0.764816512566761>,
<-0.653126262735047,-0.388615626548522,0.649926134055286>,
<0.663790345239642,-0.370694608286133,0.649590551774136>,
<-0.717706534747748,-0.39870363021308,-0.570905198115493>,
<-0.71770259091103,-0.3990014403978,-0.570702060238156>,
<-0.0919996885815812,-0.391898673425236,-0.915396901386733>,
<-0.0920988404828974,-0.391895066072177,-0.915388475331643>,
<0.726831439773906,-0.371416065949407,0.577724990034928>,
<0.7268044407887,-0.371502269885804,0.57770352977935>,
<-0.636226804799941,-0.396316697174185,-0.66192788760937>,
<-0.636176366486969,-0.396485270845777,-0.66187541178517>,
<-0.636116835054813,-0.396510493789079,-0.661917517878919>,
<-0.314110752195075,-0.382713100493649,0.868829740551037>,
<0.786871035810248,-0.369886384732762,0.493981816863976>,
<0.786929234964128,-0.369713735120394,0.494018353122734>,
<0.485089119573071,-0.382891411841948,-0.786182366127289>,
<-0.401491741399818,-0.385492070509664,0.83078291097128>,
<-0.401476263417617,-0.385577203421752,0.830750883305994>,
<0.922172053999349,-0.37008878462932,0.112396593872833>,
<0.922206178802097,-0.370002479024914,0.112400753087569>,
<0.401094252360545,-0.385894470171863,-0.8307880948919>,
<-0.490991100866944,-0.386193000315303,0.780885846572295>,
<0.905329885432785,-0.371512263822247,0.205806793794397>,
<0.905303861129202,-0.371301583604631,0.206300879875129>,
<0.306308059071053,-0.386310163954122,-0.870022890603382>,
<0.306293240182783,-0.386191476848158,-0.870080797528695>,
<-0.773076363551521,-0.397287853109584,-0.494484881355875>,
<-0.772911184105752,-0.397705754843909,-0.494407154123281>,
<-0.773114588809938,-0.397307497263211,-0.494409329592075>,
<-0.870027488952777,-0.397512559607734,-0.291609213538655>,
<-0.869992900886893,-0.397596755623711,-0.291597620573124>,
<0.877541539411925,-0.36981750572596,0.305214447667829>,
<0.000200004154129422,-0.392108144170733,-0.919919106918278>,
<-0.775405505398633,-0.390802774709551,0.496003521637505>,
<-0.775335317328075,-0.390917806711653,0.49602259434377>,
<-0.894362213994671,-0.398483164441946,-0.203291411119316>,
<-0.0952034635650079,-0.379613810601649,0.920233478702944>,
<0.102499082637316,-0.389296515811775,-0.915391807279987>,
<0.102504474417965,-0.38941699842298,-0.915339955461103>,
<-0.912068894420775,-0.396786467828268,-0.103396473723394>,
<0.000200001786023924,-0.379703390766419,0.92510826125366>,
<0.921613778228977,-0.37150555404955,-0.11230167892265>,
<0.921579536553584,-0.371591748896823,-0.112297506461553>,
<-0.871368753276751,-0.393985871919945,0.292389515099979>,
<-0.871605072756285,-0.393602290771998,0.292201700618846>,
<-0.871663399622259,-0.393483477975632,0.292187731294739>,
<0.103095935007422,-0.376985135769139,0.920463706831545>,
<0.904231264336456,-0.374012931720675,-0.206107126277089>,
<0.904197441124862,-0.374098941301494,-0.206099416739476>,
<-0.895486187232092,-0.395793894926256,0.203596859542662>,
<-0.895450742609436,-0.395878223337885,0.203588800888086>,
<0.487486187274573,-0.372089456994602,0.789877619134738>,
<0.875991546722361,-0.373896391917227,-0.304697059687561>,
<-0.702889453222881,-0.551191729430149,-0.449593253903837>,
<-0.703017519414878,-0.551013731433283,-0.449611204450824>,
<-0.814970702329867,-0.549080260919424,0.185293338824202>,
<0.807572329008212,-0.518582231084273,0.280890375456175>,
<-0.706987988376115,-0.54369076277241,0.452292315618836>,
<-0.0871963862206581,-0.530378018938498,0.843265051604139>,
<0.0936007169842382,-0.541604148703669,-0.835406399237527>,
<-0.829671716973244,-0.550281241232224,-0.0939967957038508>,
<0.000200003784107393,-0.530610039236914,0.847616037047133>,
<0.847296386288619,-0.520997777949216,-0.103199559854816>,
<0.847359980133185,-0.520875399635799,-0.103295121486616>,
<0.000199988419005973,-0.544668459162768,-0.838651435101549>,
<0.0945977344113909,-0.527487366828845,0.84427977974141>,
<0.830587371015035,-0.523692037323108,-0.18939712023868>,
<0.830603098155334,-0.523701953411929,-0.189300706092951>,
<0.448011001045198,-0.521712810815356,0.726017827586637>,
<0.804724806024474,-0.523516137633668,-0.279908628316454>,
<0.804802168944768,-0.523401410568702,-0.279900754333549>,
<-0.830781137328413,-0.548587544461203,0.0940978635322625>,
<0.370098508506016,-0.52459788587478,0.766696910217678>,
<0.370213187228598,-0.524618687250466,0.766627307751062>,
<0.6061878581788,-0.529889386421884,-0.593088120563916>,
<-0.284598161501815,-0.547396463830266,-0.786994916029264>,
<0.28278565747517,-0.524373404455372,0.803159264795108>,
<0.663891864055059,-0.529893506194872,-0.527693533155376>,
<-0.363301654842807,-0.550002505267117,-0.752003425383404>,
<-0.363388451698505,-0.54998252183318,-0.751976102579184>,
<-0.656612699012399,-0.544010521265223,0.522410103509104>,
<-0.65666958048626,-0.543974800951005,0.52237580150148>,
<0.719909754843268,-0.526707136930058,-0.452006124724485>,
<-0.463000949152919,-0.548701124838459,-0.696101427009388>,
<-0.462896977292607,-0.548796416371102,-0.696095454511523>,
<-0.596193537297083,-0.540994135655354,0.593193569816555>,
<0.610716825480316,-0.519614315571594,0.597516461805287>,
<-0.652203649089625,-0.552603091822948,-0.518902903269866>,
<-0.652237491688564,-0.552631766186907,-0.518829823195381>,
<0.668325149548575,-0.520819598810262,0.531119986421141>,
<0.668290339932954,-0.520892470553757,0.531092323115954>,
<-0.57878718289975,-0.549887822868992,-0.602186664723963>,
<-0.287400948424695,-0.534201762868726,0.795002623512986>,
<0.72399874748325,-0.51889910230533,0.45449921371704>,
<0.444006160628218,-0.534007409404209,-0.719509983270277>,
<-0.366889296160924,-0.537484319123731,0.759277848391905>,
<-0.366975828098279,-0.537464598372819,0.759249989850199>,
<-0.36699555020593,-0.537393484143507,0.75929079365494>,
<0.84838186178969,-0.51918889985998,0.103397789378894>,
<0.366601801852284,-0.537502641831977,-0.759403732478518>,
<-0.448820103102637,-0.537724085201176,0.713731968770838>,
<0.832589588532293,-0.520393492520064,0.189697627845996>,
<0.832499775225091,-0.520499859465057,0.189799948754021>,
<0.279903915883175,-0.53800752677795,-0.795111123682431>,
<-0.466924771016171,-0.480225476637321,0.742539392759707>,
<0.291209096058175,-0.480515009120718,-0.827225838802619>,
<0.465110188350265,-0.464310170825689,0.75371651034099>,
<-0.62041616825602,-0.483612603108658,0.617416090072964>,
<-0.824598730118933,-0.493599239857756,-0.276399574344983>,
<0.000100000365001998,-0.673702459018463,-0.739002697364768>,
<0.633816561843144,-0.462312080372492,0.620116203848113>,
<-0.296396295069467,-0.489993875114841,-0.819789752692137>,
<-0.0874047033736362,-0.487126212966799,-0.868946759283209>,
<-0.679879651506541,-0.495185179329371,-0.540883811589775>,
<-0.679789096270343,-0.495192057183104,-0.540991322568778>,
<0.629966861464856,-0.472475146098642,-0.616367576836408>,
<-0.736107191802396,-0.486204750243615,0.470904600760424>,
<-0.732589593638733,-0.493692987140926,-0.468593343678829>,
<-0.732553425733741,-0.493768607189901,-0.468570209253113>,
<0.689998509604829,-0.472398979619306,-0.548398815459838>,
<0.689965913076076,-0.472476657867313,-0.548372908305681>,
<0.293689500788017,-0.46698330564523,0.834070182523953>,
<-0.766106462135262,-0.593605007079352,-0.246402078410297>,
<0.747889847464231,-0.469293629382221,-0.469493626667277>,
<0.0982978998878031,-0.469989958771795,0.877181259222593>,
<-0.37859589604273,-0.492594660302823,-0.783591505914114>,
<-0.378681556763906,-0.492576009669659,-0.783561837550031>,
<0.879527683569517,-0.463614592498952,-0.107203374279309>,
<0.384392416012447,-0.467090784389735,0.796284289465952>,
<0.384477633664223,-0.467072828828501,0.796253679289522>,
<-0.849104797455658,-0.491702778128545,0.193001090459242>,
<-0.849088405776978,-0.49169328597402,0.193097363273507>,
<0.862502803138665,-0.466301515482388,-0.196600638953115>,
<0.862519756241278,-0.466310680968473,-0.196504500987143>,
<0.862485842411094,-0.466292345873963,-0.196696771249>,
<0.000200010996906923,-0.473026007684873,0.881048441374997>,
<-0.826964656285869,-0.489179092932343,0.277188153231491>,
<0.835592412855337,-0.466195766961654,-0.290597361387938>,
<-0.298897460876855,-0.476695950485101,0.826692977272987>,
<-0.603095437600272,-0.492596273523286,-0.627395253772858>,
<0.000199988578978373,-0.487272172680805,-0.873250130109064>,
<-0.0906036364499248,-0.47291898098421,0.876435176431723>,
<-0.865436150022995,-0.491320522886871,0.0981040978937553>,
<0.097401624185625,-0.484208074236957,-0.869514499275164>,
<0.880476764524772,-0.461787813580397,0.107297168465086>,
<0.670701361525146,-0.709701440695387,0.215600437669333>,
<-0.683805767925979,-0.486504103679422,0.543804587011037>,
<-0.683715332488248,-0.486510910129491,0.543912197367791>,
<0.864202532117129,-0.463101356888964,0.196700576333533>,
<0.864185529334466,-0.463092245585271,0.19679670466677>,
<-0.769847331668971,-0.588236165871251,0.247615223851958>,
<0.461707373525633,-0.476607611484334,-0.748111947443202>,
<0.837979922241592,-0.461388945253306,0.291393018306921>,
<-0.381692957829891,-0.479991144245082,0.789885426748314>,
<-0.381796705108652,-0.479895858516611,0.789893183251243>,
<0.381321032341574,-0.480026476590495,-0.790043576055189>,
<0.381290906320325,-0.479988552409536,-0.790081156789113>,
<0.693699580311881,-0.463399719643254,0.551399666403303>,
<0.693789700768338,-0.463393120980178,0.551291816133734>,
<0,-1,0>,
<-0.839356925307861,0.533772607492657,-0.102794724710088>,
<-0.795349905795558,0.54313408001706,-0.26911688626881>,
<-0.787909387996287,0.608007244449477,-0.0976011629247844>,
<-0.751216583289116,0.610013466195901,-0.252105565291781>,
<-0.241697203579532,0.46589460963055,0.851190151786915>,
<-0.292999589800861,0.471799339481387,0.831598835762445>,
<-0.327893670073798,0.162796857237006,0.930582035287212>,
<0.365993183440437,0.113397887984004,-0.923682796568119>,
<0.328896360781902,0.451595003128935,-0.829390822841318>,
<0.351597545857695,0.443196906496389,-0.824594244352262>,
<-0.00490001242154723,0.581401473854604,0.813602062483843>,
<0.143399416365563,0.701297145726425,0.698297157936351>,
<-0.188200054578024,0.614300178147077,0.766300222227097>,
<-0.230694183119508,0.166495801861283,0.958675827293768>,
<0.44838237667702,0.116895405516378,-0.886165169962479>,
<0.0943032299409392,0.831528480338186,-0.547418749413257>,
<0.156503065142546,0.831316281488811,-0.533310444987349>,
<-0.789409512441938,0.607307318097275,0.0895010784944938>,
<-0.786785586220089,0.609588832434884,0.0967982266727309>,
<-0.752353641396882,0.60816252617967,0.253184399257962>,
<-0.241499567716161,0.465799166220239,-0.851298476177091>,
<-0.448513556527122,0.140104234714492,-0.882726680817148>,
<-0.328013434065294,0.162806667883628,-0.930538110968768>,
<0.366109442084269,0.113502927278242,0.923623820565503>,
<0.328809593159823,0.451413170171362,0.829524201721633>,
<0.351512067616425,0.443015208973191,0.824728313409007>,
<-0.031101084047177,0.625221792485372,-0.779827181350117>,
<0.412196574660697,0.529695598247868,-0.741293839873786>,
<-0.192704730959218,0.623115297668339,-0.758018609585301>,
<-0.230694183119508,0.166495801861283,-0.958675827293768>,
<0.448302479119564,0.116900646462362,0.886204900726652>,
<-9.99999950000004E-5,0.999999950000004,-0.000299999985000001>,
<-0.443486085842332,0.534483230851694,-0.719477426749848>,
<-0.285305435120309,0.544110365401192,-0.789015030879509>,
<-0.415199069955125,0.608798636292582,-0.675998485765088>,
<-0.271109155510775,0.610920631138076,-0.74382511939843>,
<-0.816707031877817,0.465004003701708,0.341702942074997>,
<-0.833283276172469,0.470990547314572,0.289494189909912>,
<-0.932070593636649,0.162494873367617,0.323789784593442>,
<0.95037223052913,0.11319669244097,-0.289791532415132>,
<0.853941152415726,0.450521711164404,-0.260412549583154>,
<0.864274344396874,0.442186873877471,-0.239792881853952>,
<-0.640207381633666,0.5807066955868,0.502905798537286>,
<-0.458003606792605,0.700805518865192,0.546904306888375>,
<-0.71722447211649,0.613420930279218,0.330611280649347>,
<-0.89362278320329,0.166204237431051,0.416910629271992>,
<0.614479691781774,0.719876208484458,0.322789331988864>,
<0.972295328132172,0.116599439741038,-0.202599026514016>,
<0.521201300398867,0.829402069360745,-0.201100501746378>,
<0.520835342481287,0.826756101438709,-0.212614427441478>,
<0.52089211896186,0.827287483235067,-0.210396816720244>,
<0.482903443113824,0.830305920102316,-0.278201983587214>,
<0.521204052377261,0.829906452547753,-0.199001547243045>,
<-0.562868809489109,0.607866316021371,-0.560068964638213>,
<-0.567109836605425,0.61011058245983,-0.553309597238197>,
<-0.667891764945307,0.608392498566739,-0.42869471422676>,
<0.516601472316294,0.465701327250674,-0.718502047733754>,
<0.41258456756287,0.14019475611322,-0.900066333648423>,
<0.525002769396913,0.162700858249291,-0.835404406769869>,
<-0.495875858871452,0.113394479524143,0.860958085275903>,
<-0.445295304385773,0.451195242171257,0.773391844625997>,
<-0.42749609484101,0.442895954163938,0.788092800805147>,
<0.591471193097043,0.624569581079312,-0.509975162264568>,
<0.837002121803068,0.528801340513097,-0.140700356675856>,
<0.473985285355221,0.622780666074328,-0.622480675387394>,
<0.607324144651315,0.166406615626509,-0.776830883525674>,
<-0.415398508722031,0.11689958033126,0.902096761478439>,
<0.287789908823768,0.534781248224291,-0.794472142322736>,
<0.440630765914152,0.544037986058326,-0.714049856701553>,
<0.271193003310771,0.608984288408036,-0.74538076942422>,
<0.41411587957786,0.61072341863849,-0.674925880529093>,
<-0.777358906007622,0.465075414438056,-0.423577608161601>,
<-0.74688606697038,0.471091211875413,-0.469291245453473>,
<-0.835505635504517,0.16250109607359,-0.524903540486321>,
<0.820299450399552,0.113299924089076,0.560599624398377>,
<0.737036156195485,0.450822115621336,0.503524701010077>,
<0.727355570841891,0.44247297236395,0.524567957744922>,
<-0.793011796138202,0.580308632155105,-0.185402757886535>,
<-0.71378341186626,0.700183727919243,-0.0159996281729618>,
<-0.706402751444075,0.613502389596461,-0.353001374943033>,
<-0.884191861051378,0.166098471070611,-0.43659598115249>,
<0.765890430258861,0.116698541860829,0.632292099559574>,
<0.489811184966115,0.827318892042602,0.275006279840101>,
<0.481101431278887,0.830002469261019,0.282200839548746>,
<0.482706190746595,0.829510638542159,0.280903602611805>,
<0.491506699281967,0.826811269514406,0.273503727881217>,
<0.519197487090244,0.830195981861172,0.202999017487133>,
<0.0883002145697821,0.608401478417389,-0.788701916547986>,
<0.0805023567409909,0.610617876099988,-0.787823063857797>,
<-0.0803025721325788,0.609219513613537,-0.788925269681088>,
<-0.323095381384534,0.831288116821305,0.452293534510136>,
<-0.372997894432829,0.830995309044721,0.412697670328226>,
<0.884165659672654,0.464981940452142,-0.0451982445342728>,
<0.960879985078364,0.139797088056983,-0.239095019702609>,
<0.980404818701526,0.162400798201885,-0.11150054802654>,
<-0.98218473696778,0.113198240913004,0.149997669054334>,
<-0.882492212040593,0.450596023507639,0.134798810405747>,
<-0.882867285958817,0.442283611484409,0.157894149340692>,
<0.768127875866432,0.624122649821951,0.143005189752506>,
<0.632692189463131,0.529393464678018,0.565193022735201>,
<0.782749040782496,0.622059502134377,-0.0185987891652458>,
<0.986050092337583,0.166091593486738,-0.0108994483383832>,
<-0.964215037050755,0.116601818419537,0.238103713256363>,
<0.801086069234383,0.53389071572118,-0.270595294388745>,
<0.833574776408893,0.542983569565774,-0.101596925723541>,
<0.752499898412521,0.608099917906517,-0.252899965858507>,
<0.786404962230968,0.609903848505426,-0.0979006177548471>,
<-0.152798770738834,0.46599625107524,-0.871492988867107>,
<-0.0977963190158271,0.47198223492301,-0.876167021693943>,
<-0.109494360090764,0.162891609669273,-0.980549493196375>,
<0.0722007978232239,0.113501254195788,0.99091094962649>,
<0.0648008650973236,0.451506027645704,0.889911880402906>,
<0.0421991834537002,0.443091426263852,0.895482672577927>,
<-0.349609677329805,0.581216088283989,-0.734820340108525>,
<-0.43289159549126,0.700886392422785,-0.566888993957023>,
<-0.163907954646063,0.614329814149339,-0.77183745818079>,
<-0.209290894997665,0.166592752539948,-0.963558081317489>,
<-0.0182993330929579,0.116995736168092,0.992963812093291>,
<-0.370387834811345,0.831172700580967,-0.414686379849527>,
<-0.32060145393089,0.831103769064139,-0.454402060718018>,
<0.672590805746528,0.607691692911336,-0.422294227277369>,
<0.667107718480955,0.609807055508449,-0.427904950888923>,
<0.568086488223565,0.608585524965431,-0.5539868235801>,
<0.587577784103967,0.465482400443153,0.661874974980285>,
<0.787198346885207,0.139999706000926,0.600598738743973>,
<0.699693933679893,0.162598590276333,0.695693968359441>,
<-0.730907053287096,0.113301093360826,-0.673006494544009>,
<-0.65661811962702,0.451012445860168,-0.604516681868007>,
<-0.675008825798096,0.4426057871085,-0.590307718323876>,
<0.367278423026452,0.624963284485523,0.688859530691323>,
<-0.0488022691142549,0.529924639418928,0.846639365412464>,
<0.503198447635184,0.622698078979389,0.599198151476554>,
<0.62438796191614,0.166396791900778,0.76318528592953>,
<-0.788614770892989,0.116702185852412,-0.603711307618689>,
<0.711932311781222,0.534124241778832,0.4559206924302>,
<0.599979013101176,0.543580985869666,0.586979467817317>,
<0.667735664714304,0.608332491906112,0.42902291472583>,
<0.567493544797642,0.610493055680987,0.55249371541973>,
<0.587697945999268,0.465298373785025,-0.661897686671628>,
<0.62542682825921,0.471220213424592,-0.621926678117049>,
<0.699693933679893,0.162598590276333,-0.695693968359441>,
<-0.730907053287096,0.113301093360826,0.673006494544009>,
<-0.656694519907097,0.450796238121089,0.604594954676155>,
<-0.675028716332402,0.44241882089697,0.590425117218741>,
<0.358303835663091,0.58110622077539,-0.730707822269106>,
<0.174703584080791,0.701214385560681,-0.691214180404368>,
<0.502706419601968,0.613907839653169,-0.608607771970872>,
<0.62438796191614,0.166396791900778,-0.76318528592953>,
<-0.788614770892989,0.116702185852412,0.603711307618689>,
<0.08990119659289,0.827911019569006,0.553607368563114>,
<0.0786991445449482,0.830590971525209,0.551294007466708>,
<0.0807019614850111,0.830120176316081,0.551713409557381>,
<0.0921004987255509,0.827404480407392,0.554002999934367>,
<0.164597803456969,0.83078891319593,0.531692904605532>,
<0.75031738505522,0.607414073947142,0.260906045263104>,
<0.7511910458561,0.609592733697921,0.253196981909963>,
<0.787884514282056,0.608088048146869,0.0972980876248814>,
<-0.152798770738834,0.46599625107524,0.871492988867107>,
<0.0200001390014491,0.140300975095165,0.989906879876723>,
<-0.109494360090764,0.162891609669273,0.980549493196375>,
<0.0720018339100648,0.113502890955449,-0.990925239187267>,
<0.0647011963361805,0.451708352164648,-0.889816452858321>,
<0.0420994009297871,0.443293691975645,-0.895387258729962>,
<-0.311183156111627,0.62506616608412,0.715861251479158>,
<-0.69328537529926,0.529288834769794,0.489089682761962>,
<-0.156307894529575,0.623131472049764,0.766338704913712>,
<-0.209290894997665,0.166592752539948,0.963558081317489>,
<-0.452727008235288,0.720242967375866,-0.525631357473973>,
<-0.0182995470918141,0.116897106832408,-0.992975424162374>,
<0.0864982479957301,0.534889165929665,0.840482976189724>,
<-0.0862981700667054,0.544388456365173,0.834382307110765>,
<0.0799981744624879,0.609186098531845,0.788981995636287>,
<-0.079397565310989,0.611081261480421,0.787575849356863>,
<0.884165659672654,0.464981940452142,0.0451982445342728>,
<0.876494123126606,0.470996841976762,0.0995993321887165>,
<0.980415745603313,0.162402608206832,0.1114017891271>,
<-0.98218473696778,0.113198240913004,-0.149997669054334>,
<-0.882605732542849,0.4504029253765,-0.134700874885024>,
<-0.88302038034557,0.442010201713185,-0.157803642150092>,
<0.795136537363357,0.580326666623011,-0.176008087757453>,
<0.650183599325558,0.700382333078469,-0.294492571518574>,
<0.789770506571138,0.613277097594427,0.0123995369479389>,
<0.986050092337583,0.166091593486738,0.0108994483383832>,
<-0.964203789328338,0.116700458633704,-0.238100935738516>,
<-0.555786580695012,0.830779941060482,0.0300992732618205>,
<-0.555822541840286,0.83063368703228,-0.0333013505636587>,
<0.263591362252586,0.608280066988802,0.748675466306947>,
<0.270200197246216,0.610500445665488,0.744500543485595>,
<0.415478167195956,0.608868004826997,0.67576448950909>,
<-0.777312849087594,0.465307691599714,0.42340699897554>,
<-0.762438637556962,0.140007095039316,0.631732013830972>,
<-0.835505635504517,0.16250109607359,0.524903540486321>,
<0.820308740436194,0.113201206165278,-0.560605973288468>,
<0.737077939587397,0.450686511154579,-0.503584927928657>,
<0.727381764767733,0.442288911955964,-0.524686846265644>,
<-0.754344677386567,0.624136965606465,0.203512053358301>,
<-0.815365653445258,0.528977717282979,-0.235290088613771>,
<-0.697398500594836,0.622298662059315,0.355499235677465>,
<-0.884153256052996,0.166091218989372,0.436676913501859>,
<0.765890430258861,0.116698541860829,-0.632292099559574>,
<-0.604393046498,0.534593849533141,0.59069320411378>,
<-0.707209727736708,0.543307473245692,0.452406222890394>,
<-0.568284858251675,0.60858378450109,0.553785244588734>,
<-0.666300769577833,0.610200704782221,0.428600495033858>,
<0.516601472316294,0.465701327250674,0.718502047733754>,
<0.469107679355567,0.471707721918612,0.746612222142116>,
<0.525002769396913,0.162700858249291,0.835404406769869>,
<-0.495875858871452,0.113394479524143,-0.860958085275903>,
<-0.445409912707912,0.451210041791221,-0.773317210366027>,
<-0.42749609484101,0.442895954163938,-0.788092800805147>,
<0.634222106196774,0.580720241356775,0.510417790922159>,
<0.636288489645333,0.700487328298846,0.323194153470645>,
<0.483205061599531,0.614006431751059,0.624106537550221>,
<0.607387257149016,0.16639650903786,0.776783703248857>,
<0.179199190021492,0.720596742910083,-0.669796972524526>,
<-0.415398508722031,0.11689958033126,-0.902096761478439>,
<-0.422608374067899,0.608112049859653,0.672013316075788>,
<-0.285281363177296,0.544364437832877,0.788848466212999>,
<-0.415211981114575,0.61031761096875,0.67461946642556>,
<-0.270898539860805,0.609096716977543,0.745395982326483>,
<-0.816707031877817,0.465004003701708,-0.341702942074997>,
<-0.969675632357535,0.139896484445518,-0.200394964137826>,
<-0.932085734536999,0.16239751452506,-0.323795044354769>,
<0.950399767152086,0.11319997226601,0.289699929023526>,
<0.853941152415726,0.450521711164404,0.260412549583154>,
<0.864295064889269,0.442197475059626,0.239698631324723>,
<-0.63018668744684,0.624486807855525,-0.46139025323385>,
<-0.324292829964793,0.529788286510477,-0.783682672967649>,
<-0.713563659128237,0.622268308682038,-0.321883606885341>,
<-0.893637631873034,0.166106994912837,-0.416917556767981>,
<0.972295328132172,0.116599439741038,0.202599026514016>,
<-0.813011618019033,-0.552107889678116,-0.184902642277637>,
<-0.813026647385059,-0.552118095967148,-0.184806057117785>,
<-0.362818691086319,-0.551228397262346,0.751338706210451>,
<0.785316071657861,-0.560311466891506,-0.263305388599917>,
<0.785336746766092,-0.560326218277144,-0.263212315992405>,
<0.0829984172352748,-0.558289353523541,0.825484258165294>,
<-0.000100005959532698,-0.558433278030586,0.829549434323731>,
<0.284511098994462,-0.547821370928528,-0.786730690962893>,
<0.587590451732738,-0.554290992844549,0.589490420858491>,
<0.443474771437859,-0.563167962285912,0.697260333987867>,
<-0.441481789251782,-0.535477911991686,-0.719970301837561>,
<-0.000300013959974344,-0.54422532339346,-0.838939036741591>,
<-0.66164342839168,-0.534435078797633,-0.525934520845201>,
<-0.82229182234849,-0.537394655636724,0.187198138323771>,
<0.428100809111294,-0.180600341334968,0.885501673599745>,
<0.0983980822400655,-0.178096528932476,0.979080917898863>,
<0.958903945897856,-0.181600747288613,0.218000897075537>,
<0.977289117946257,-0.180697987939106,0.110698767376088>,
<-0.109703456810387,-0.170505372708942,-0.979230856050419>,
<0.53668783610804,0,0.843780875923168>,
<0.705916091540712,-0.0106002416352621,0.708216143971005>,
<-0.899788734715562,0.303996193991477,0.312996081313593>,
<0.524095173105683,0.323097024290109,0.787992742620261>,
<0.672211343662137,0.317105351197953,-0.66901128966077>,
<0.665376802042188,0.334088352212647,0.667576725343199>,
<0.323097177758478,0.311897275589196,-0.89349219537976>,
<-0.498911372814366,0.311807107724032,0.808618432667261>,
<0.0951005163972061,0.310801687657746,-0.945705135192826>,
<-0.105602618449387,0.317607875184899,0.942323365197514>,
<0.838440879181621,-0.0968047198291757,-0.536326149218873>,
<0.970342434002479,-0.0986043120608517,0.220709651844117>,
<0.101897980911512,-0.140097224001009,-0.984880484786539>,
<-0.110796290048341,-0.0962967755564548,0.989166878301611>,
<0.9888223773036,-0.0983022246045145,0.112102536909116>,
<-0.000200005704244029,-0.0934026638819614,-0.995628395726775>,
<-0.970894630967536,-0.0915994934562018,0.221298776221151>,
<0.970405507066879,-0.0980005561547343,-0.220701252483162>,
<0.988873073352814,-0.0977973370147691,-0.112096947641673>,
<-0.330681650984266,-0.091994895344882,-0.939247882580953>,
<-0.779473455483452,-0.0930968296414489,0.61947890400513>,
<0.43310095065763,-0.0980002151107083,0.896001966726475>,
<-0.843294712558728,-0.0926994187764664,0.529396680693218>,
<0.705588791811062,-0.095998475076335,-0.702088847407238>,
<0.338502887441945,-0.094500806095314,-0.93620798588818>,
<-0.843402943481409,-0.0911003179406644,-0.529501847964674>,
<0.433012156986963,-0.0954026784677975,-0.896325164682253>,
<-0.988520951923616,-0.0910019288063218,-0.120602556198268>,
<-0.432780280815747,-0.0951956625084545,0.896459153769216>,
<0.529899909917023,-0.0951999838160041,-0.842699856741036>,
<-0.970944706273422,-0.0910948122981552,-0.221287398041512>,
<-0.940640909362742,-0.0905039361017735,-0.327114226507073>,
<0.80394931978383,-0.565934718330227,0.182711208762913>,
<0.329115807811883,-0.65433142829327,0.680832701179975>,
<0.25789872469396,-0.651796776872908,0.71319647325216>,
<-0.578972308416717,-0.672867817501915,0.460477975865109>,
<-0.623303157038486,-0.672703407251387,0.398702019430843>,
<-0.552503660348875,-0.634504203604273,0.540503580848085>,
<-0.605163055923119,-0.634461267321909,0.480870643743272>,
<0.261604027425004,-0.63870983301357,-0.723611140079253>,
<0.333605496195825,-0.64201057721139,-0.690311372973555>,
<-0.61099042585504,-0.625290201779307,-0.485492392393816>,
<-0.558398676596705,-0.62429852041426,-0.546298705273603>,
<0.408092315694043,-0.642187907715545,-0.648887781558109>,
<-0.617803731545808,-0.679804106029201,-0.395202387029627>,
<-0.572992857688543,-0.68119150900076,-0.455694319805705>,
<-0.738588751378974,-0.623290507357859,-0.256896087502381>,
<-0.333314207820919,-0.641927362737018,0.690529434444478>,
<-0.404099842401092,-0.638599750946146,0.654899744589149>,
<-0.75852162575983,-0.628317913599079,0.172804926738693>,
<-0.774495407255853,-0.625496290817993,0.0943994402129793>,
<-0.0849993642071336,-0.645995167974215,0.758594325735665>,
<0.751183245044577,-0.65458539963549,0.0850981019080052>,
<-0.407392410350092,-0.626488328631155,-0.664487620710938>,
<-0.337909593389536,-0.62911786090961,-0.700019873846332>,
<-0.285090116097009,-0.585779691370144,-0.758673697238867>,
<-0.31951607525813,-0.678734147973998,-0.661233267482552>,
<-0.0863032062236679,-0.632023480108437,-0.770128610809347>,
<-0.775869935622481,-0.623775829154921,-0.0944963383378327>,
<-0.761065456022909,-0.625071628642649,-0.173392129909831>,
<0.752429548488565,-0.65312564874785,-0.0853033499283288>,
<-0.00030000692123951,-0.635114652264044,-0.772417819884659>,
<0.0795006435603144,-0.635005140387417,-0.76840622027353>,
<0.738224225225433,-0.65322143581313,-0.168305523036359>,
<0.753865632049235,-0.650570341174203,-0.0918958105654923>,
<-0.258192511234816,-0.628881759549093,-0.733378728658458>,
<0.541299258420524,-0.645599115529818,-0.538699261982517>,
<-0.0690976831935231,-0.99756655215425,0.00929968818668256>,
<-0.0687992428684983,-0.997589021593226,0.00909989985615312>,
<-0.0221002813383722,-0.99781270223655,0.0623007930941442>,
<-0.0220995901669005,-0.997781496313725,0.062798835406396>,
<-0.0347008637152469,-0.997724833680169,0.0578014386957139>,
<0.0453016592036531,-0.997936550095483,0.0455016665290555>,
<0.034600863994361,-0.997924918496326,0.0543013559217863>,
<0.034500793182353,-0.997922942512176,0.0544012506991305>,
<0.00559982985175489,-0.997869680190394,0.0649980250650121>,
<0.00579985483145033,-0.997875023500739,0.0648983756139873>,
<-0.00730018842029472,-0.997825754215079,0.0655016906204526>,
<-0.00739964046020485,-0.997851515572759,0.0650968370215318>,
<0.0468007806435318,-0.997816643720427,-0.0466007773074483>,
<0.0449011849579068,-0.998026338262605,-0.0439011585668621>,
<0.0620981116251378,-0.997869655245169,-0.0199993918277416>,
<0.0596988690156397,-0.998181089638384,-0.00839984086652216>,
<0.0617993925149574,-0.997890190787637,-0.0198998043858843>,
<0.0643002510929708,-0.997903896822326,-0.00730002850666698>,
<0.00619987423682665,-0.997679762271281,-0.0677986247188462>,
<-0.00759966923159489,-0.997656577942397,-0.0679970404932174>,
<0.0237007586734279,-0.997731937910507,-0.0630020167268336>,
<-0.0506025428416683,-0.997550128153441,-0.0483024272579561>,
<-0.0549982785808202,-0.997568776585932,-0.0427986604228928>,
<0.0539010031070018,-0.997918571437423,0.0354006588123908>,
<0.0536022403524543,-0.997941709845413,0.0352014712762386>,
<0.0641010733814607,-0.997916710255221,0.00730012224157041>,
<0.0615983239324084,-0.998072842807416,0.00749979593332894>,
<-0.0492986642707871,-0.997672968417126,0.0470987238773646>,
<-0.0489998554506396,-0.997697056798024,0.0468998616456122>,
<-0.0489000951107775,-0.997701940532162,0.0469000912207661>,
<-0.0347006629624989,-0.99771906160476,-0.0579011062112013>,
<-0.0355993376804836,-0.997581440169957,-0.0596988893124964>,
<-0.0695026762520746,-0.997538410956035,-0.00930035811718408>,
<0.251194674729343,-0.653986135640885,0.713584872161064>,
<9.99974151002291E-5,-0.659982939661512,0.751280579648021>,
<-0.0772967095491102,-0.65967191836414,0.747568176700062>,
<0.0760984320079625,-0.648986627768301,0.756984402497078>,
<-0.000100000234500825,-0.649101522144854,0.760701783847775>,
<0.324004158620064,-0.666708557259249,-0.671208615017861>,
<0.392987660381193,-0.663379170221077,-0.63678000542174>,
<-0.394420711575361,-0.678135609835833,-0.620132564015927>,
<-0.526214892092172,-0.670018961804932,0.523614818509049>,
<0.595766906067526,-0.648463978826436,-0.47387367704834>,
<0.641605543495844,-0.64800559879256,-0.410403545901955>,
<0.719796926473686,-0.650897220674802,-0.241298969655599>,
<0.738096051196689,-0.653496503803057,-0.167799102277204>,
<-0.455387012547601,-0.764178205948345,-0.456786972621309>,
<0.537302141153299,-0.65950262812321,-0.525702094927022>,
<0.588599526177572,-0.659399469183641,-0.467699623501955>,
<-0.25439839602317,-0.663395817302558,0.703695563213461>,
<-0.324294391377001,-0.666688469722623,0.671088393626598>,
<0.594595656494594,-0.65039524888006,0.472696546964337>,
<0.543597537508733,-0.649297058690986,0.531897590509373>,
<-0.396582312823263,-0.666970253790007,0.630771868202003>,
<-0.467205372892683,-0.492005658097602,-0.734608448045729>,
<0.719118801606855,-0.648316950468258,0.250106539120949>,
<0.636275869695204,-0.655375145368909,0.40698456540303>,
<0.59033075112976,-0.656334189338407,0.469824473794276>,
<-0.729385244685749,-0.679086262223871,-0.0826983270297661>,
<0.398515456721739,-0.651325262140198,0.645725044931561>,
<0.328890879982345,-0.654081862561423,0.68118111110968>,
<0.0839059801923201,-0.656946822268594,0.749253401192922>,
<0.755386931919116,-0.648788776051261,0.0919984084413008>,
<0.740834045810881,-0.65012987740504,0.168807757738764>,
<-0.730871898515743,-0.67747395162733,0.0827968165236058>,
<-0.912675353534862,-0.395389322655511,0.103397207796105>,
<0.403091984595579,-0.374592551301175,0.834983396520239>,
<0.307891882537524,-0.374590124061567,0.874576942082879>,
<0.724381249634027,-0.3795901744355,-0.575485103760881>,
<-0.720297943552307,-0.391098883414282,0.572898364377504>,
<-0.653075490536752,-0.388785409157386,0.649875610625991>,
<0.663895478887183,-0.370497476920773,0.649595576269188>,
<-0.717718887021021,-0.398810494836259,-0.570815021194927>,
<-0.491085508280473,-0.385888612595061,0.780976953710139>,
<0.905396532337922,-0.37129857792917,0.20589921140753>,
<0.877509082266002,-0.369903828524438,0.305203158869042>,
<-0.0951998491083588,-0.379699398176931,0.920198541486468>,
<0.103101537255381,-0.377105622686752,0.920413723470926>,
<-0.895504414847648,-0.39580195130843,0.203501003262419>,
<-0.793956380844477,-0.54663881820077,0.26611889777392>,
<-0.794036740930024,-0.546525288310149,0.266112313301611>,
<-0.815015448764248,-0.549010406590886,0.185303512461368>,
<-0.793912976613655,-0.54670893603059,0.266104349511139>,
<-0.829717370314973,-0.55021151879872,-0.0940019679517988>,
<-0.0839037879220105,-0.54452458311722,-0.834537676053847>,
<0.447987626752621,-0.521785588481066,0.725979948710721>,
<-0.83069675198205,-0.548697854595583,0.0941996316801602>,
<0.282800485003248,-0.524300899176813,0.803201377491544>,
<-0.363810243221599,-0.549915483088393,-0.751821167822974>,
<-0.596129076904856,-0.541026389205716,0.593228935447007>,
<0.668289651614866,-0.520791935599315,0.531191774559056>,
<-0.287508041712392,-0.534114939403786,0.795022237082961>,
<0.36648740221207,-0.537481524390143,-0.759473893533607>,
<-0.448788069127771,-0.537685705815514,0.713781024383697>,
<0.83240993487186,-0.520606213472237,0.189902266497076>,
<-0.466902346190184,-0.480302413525692,0.742503731090623>,
<-0.0712042773694162,-0.584935138109151,-0.807948534926283>,
<-0.0739990335789321,-0.673591202956334,-0.735390395864145>,
<-0.714133954306055,-0.680932375699472,-0.162307717103869>,
<-0.695483325987137,-0.679683704778515,-0.233094411628471>,
<0.862536701717504,-0.466319842331446,-0.196408357353412>,
<-0.620482781841703,-0.476986763800954,-0.622482726344013>,
<0.735579606338118,-0.656481799294418,0.167195364572775>,
<0.716243874862373,-0.655240137964014,0.240114708676984>,
<-0.699121830419988,-0.675521093475471,0.234307316360182>,
<-0.716791882377898,-0.677992321780434,0.162898155188839>,
<-0.381607523466488,-0.48000946347986,0.789915573339044>
}
face_indices{
1812,
<0,1,2>,
<3,4,5>,
<6,7,1>,
<3,8,9>,
<10,11,7>,
<12,13,8>,
<14,15,11>,
<16,17,13>,
<18,3,5>,
<12,19,16>,
<19,20,16>,
<21,11,15>,
<22,7,11>,
<1,23,2>,
<24,25,26>,
<27,28,25>,
<29,26,30>,
<30,31,29>,
<32,33,34>,
<21,35,32>,
<36,37,38>,
<39,40,41>,
<42,43,44>,
<41,45,46>,
<43,47,48>,
<46,49,50>,
<48,51,52>,
<50,53,54>,
<52,55,56>,
<43,57,44>,
<48,58,57>,
<56,58,52>,
<49,59,53>,
<45,60,49>,
<61,40,62>,
<63,64,65>,
<65,66,67>,
<68,69,63>,
<70,71,68>,
<33,72,34>,
<73,59,72>,
<74,75,76>,
<77,78,79>,
<80,81,82>,
<83,84,78>,
<80,85,86>,
<87,88,84>,
<89,90,85>,
<91,92,88>,
<93,94,90>,
<95,80,82>,
<89,96,93>,
<96,97,93>,
<98,88,92>,
<99,84,88>,
<78,100,79>,
<75,101,102>,
<103,104,101>,
<105,102,106>,
<106,107,105>,
<108,109,76>,
<98,110,108>,
<111,112,113>,
<114,115,83>,
<116,117,118>,
<83,119,87>,
<117,120,121>,
<87,122,123>,
<121,124,125>,
<123,126,127>,
<125,128,129>,
<117,130,118>,
<121,131,130>,
<129,131,125>,
<122,132,126>,
<119,133,122>,
<134,115,135>,
<74,136,137>,
<137,138,139>,
<140,141,74>,
<142,143,140>,
<109,144,76>,
<145,132,144>,
<146,147,148>,
<149,150,151>,
<152,153,147>,
<149,154,155>,
<156,157,153>,
<158,159,154>,
<160,161,157>,
<162,163,159>,
<164,149,151>,
<158,165,162>,
<165,166,162>,
<167,157,161>,
<168,153,157>,
<147,169,148>,
<170,171,172>,
<173,174,171>,
<175,172,176>,
<176,177,175>,
<178,179,180>,
<167,181,178>,
<182,183,28>,
<184,185,186>,
<187,188,189>,
<186,190,191>,
<188,192,193>,
<191,194,195>,
<193,196,197>,
<195,198,199>,
<197,200,201>,
<202,203,204>,
<188,205,189>,
<193,206,205>,
<201,206,197>,
<194,207,198>,
<190,208,194>,
<209,185,210>,
<211,212,213>,
<213,214,215>,
<216,217,211>,
<218,219,216>,
<179,220,180>,
<221,207,220>,
<222,223,224>,
<225,226,227>,
<228,229,223>,
<225,230,231>,
<232,233,229>,
<234,235,230>,
<236,237,233>,
<238,239,235>,
<240,225,227>,
<234,241,238>,
<241,242,238>,
<243,233,237>,
<244,229,233>,
<223,245,224>,
<246,247,248>,
<249,250,247>,
<251,248,252>,
<252,253,251>,
<254,255,256>,
<243,257,254>,
<258,259,260>,
<261,262,263>,
<264,265,266>,
<263,267,268>,
<265,269,270>,
<268,271,272>,
<270,273,274>,
<272,275,276>,
<274,277,278>,
<29,32,24>,
<265,279,266>,
<270,280,279>,
<278,280,274>,
<271,281,275>,
<267,282,271>,
<283,262,284>,
<285,286,287>,
<287,288,202>,
<289,290,285>,
<291,292,289>,
<255,293,256>,
<294,281,293>,
<295,296,297>,
<298,299,300>,
<301,302,296>,
<298,303,304>,
<305,306,302>,
<307,308,303>,
<309,310,306>,
<311,312,308>,
<313,298,300>,
<307,314,311>,
<314,315,311>,
<316,306,310>,
<317,302,306>,
<296,318,297>,
<319,320,321>,
<322,323,320>,
<324,321,325>,
<325,326,324>,
<327,328,329>,
<316,330,327>,
<331,332,104>,
<333,334,335>,
<336,337,338>,
<335,339,340>,
<337,341,342>,
<340,343,344>,
<342,345,346>,
<344,347,348>,
<346,349,350>,
<260,351,352>,
<337,353,338>,
<342,354,353>,
<350,354,346>,
<343,355,347>,
<339,356,343>,
<357,334,358>,
<359,360,361>,
<361,362,351>,
<363,364,359>,
<365,366,363>,
<328,367,329>,
<368,355,367>,
<369,370,371>,
<372,373,374>,
<375,376,370>,
<372,377,378>,
<379,380,376>,
<381,382,377>,
<383,384,380>,
<385,386,382>,
<387,372,374>,
<381,388,385>,
<388,389,385>,
<390,380,384>,
<391,376,380>,
<370,392,371>,
<393,394,395>,
<396,397,394>,
<398,395,399>,
<399,400,398>,
<401,402,403>,
<390,404,401>,
<351,405,406>,
<407,408,409>,
<410,411,412>,
<409,413,414>,
<411,415,416>,
<414,417,418>,
<416,419,420>,
<418,421,422>,
<420,423,424>,
<411,425,412>,
<416,426,425>,
<424,426,420>,
<417,427,421>,
<413,428,417>,
<429,408,430>,
<431,432,433>,
<433,434,258>,
<435,436,431>,
<437,438,435>,
<402,439,403>,
<440,427,439>,
<441,442,443>,
<444,445,446>,
<447,448,442>,
<444,449,450>,
<451,452,448>,
<453,454,449>,
<455,456,452>,
<457,458,454>,
<459,444,446>,
<453,460,457>,
<460,461,457>,
<462,452,456>,
<463,448,452>,
<442,464,443>,
<465,466,467>,
<468,111,466>,
<469,467,470>,
<470,471,469>,
<472,473,474>,
<462,475,472>,
<476,477,478>,
<479,480,481>,
<478,482,483>,
<480,484,485>,
<483,486,487>,
<485,488,489>,
<487,490,491>,
<489,492,493>,
<480,494,481>,
<485,495,494>,
<493,495,489>,
<486,496,490>,
<482,497,486>,
<498,477,499>,
<500,501,502>,
<502,503,36>,
<504,505,500>,
<506,507,504>,
<473,508,474>,
<509,496,508>,
<510,511,512>,
<513,514,515>,
<512,516,517>,
<518,519,520>,
<517,521,522>,
<523,524,525>,
<526,527,528>,
<520,519,529>,
<530,531,532>,
<533,534,535>,
<528,536,537>,
<538,539,540>,
<533,541,542>,
<543,544,531>,
<545,534,546>,
<537,547,548>,
<549,550,523>,
<551,552,544>,
<553,554,555>,
<556,532,557>,
<558,559,554>,
<560,555,561>,
<562,563,564>,
<565,557,566>,
<567,563,568>,
<562,569,570>,
<571,566,572>,
<573,574,575>,
<576,568,577>,
<578,579,580>,
<581,582,569>,
<583,584,573>,
<576,585,586>,
<587,588,589>,
<580,590,591>,
<583,592,593>,
<594,595,587>,
<591,596,597>,
<598,599,594>,
<563,600,601>,
<590,602,596>,
<523,603,604>,
<570,605,600>,
<595,606,588>,
<607,608,527>,
<569,609,605>,
<599,610,595>,
<527,611,536>,
<555,612,613>,
<614,615,599>,
<536,616,547>,
<561,613,617>,
<568,601,618>,
<535,619,620>,
<529,621,607>,
<577,618,622>,
<547,619,534>,
<532,623,624>,
<577,625,585>,
<626,627,511>,
<531,628,623>,
<584,629,574>,
<511,630,516>,
<544,631,628>,
<593,632,584>,
<516,633,521>,
<535,634,541>,
<635,634,636>,
<593,637,638>,
<519,639,529>,
<554,640,612>,
<539,636,641>,
<557,624,642>,
<643,644,519>,
<540,641,645>,
<566,642,646>,
<647,648,579>,
<559,649,640>,
<572,646,650>,
<579,651,590>,
<524,604,649>,
<649,652,640>,
<646,653,650>,
<648,654,651>,
<604,655,649>,
<600,656,601>,
<651,657,602>,
<603,658,604>,
<605,659,600>,
<610,660,606>,
<621,661,608>,
<609,662,605>,
<615,663,610>,
<608,664,611>,
<612,665,613>,
<666,667,615>,
<616,664,668>,
<613,669,617>,
<601,670,618>,
<619,671,620>,
<639,672,621>,
<618,673,622>,
<619,668,674>,
<623,675,624>,
<622,676,625>,
<677,678,627>,
<628,679,623>,
<632,680,629>,
<627,681,630>,
<631,682,628>,
<638,683,632>,
<633,681,684>,
<620,685,634>,
<634,686,636>,
<637,687,638>,
<644,688,639>,
<612,652,689>,
<641,686,690>,
<624,691,642>,
<692,693,644>,
<641,694,645>,
<642,695,646>,
<696,697,648>,
<690,698,694>,
<691,699,695>,
<697,700,701>,
<655,702,652>,
<695,703,653>,
<654,701,704>,
<658,705,655>,
<659,706,656>,
<657,704,707>,
<708,709,658>,
<662,710,659>,
<660,711,712>,
<661,713,714>,
<715,716,662>,
<663,717,711>,
<664,714,718>,
<689,719,665>,
<667,720,717>,
<668,718,721>,
<665,722,669>,
<656,723,670>,
<671,724,725>,
<672,726,713>,
<670,727,673>,
<668,724,674>,
<679,728,675>,
<676,727,729>,
<678,730,731>,
<682,732,679>,
<680,733,734>,
<681,731,735>,
<736,737,682>,
<683,738,733>,
<684,735,739>,
<685,725,740>,
<685,741,686>,
<742,738,687>,
<688,743,726>,
<652,744,689>,
<686,745,690>,
<675,746,691>,
<693,747,743>,
<743,748,726>,
<744,749,750>,
<745,751,752>,
<728,753,746>,
<747,754,743>,
<698,752,755>,
<699,753,756>,
<700,757,701>,
<702,758,749>,
<703,756,759>,
<701,760,704>,
<705,761,758>,
<706,762,763>,
<704,764,707>,
<709,765,761>,
<710,766,762>,
<711,767,712>,
<713,768,714>,
<716,769,766>,
<717,770,711>,
<714,771,718>,
<719,750,772>,
<720,773,717>,
<718,774,721>,
<722,772,775>,
<723,763,776>,
<725,777,778>,
<726,779,713>,
<727,776,780>,
<721,777,724>,
<728,781,782>,
<727,783,729>,
<730,784,731>,
<732,785,781>,
<733,786,734>,
<731,787,735>,
<737,788,785>,
<738,789,733>,
<735,790,739>,
<725,791,740>,
<741,791,751>,
<738,792,793>,
<794,541,635>,
<768,795,796>,
<797,798,799>,
<774,800,777>,
<801,802,770>,
<780,803,783>,
<804,805,781>,
<776,806,807>,
<785,808,804>,
<787,809,790>,
<810,811,812>,
<793,813,814>,
<784,815,816>,
<757,817,818>,
<819,820,754>,
<755,821,822>,
<823,806,762>,
<824,825,826>,
<759,827,828>,
<814,829,789>,
<760,830,764>,
<761,831,832>,
<753,833,827>,
<834,835,836>,
<766,837,823>,
<838,801,773>,
<771,839,774>,
<775,840,841>,
<818,842,830>,
<815,843,816>,
<795,826,844>,
<845,797,840>,
<827,846,828>,
<804,847,848>,
<807,849,803>,
<850,812,821>,
<851,852,853>,
<823,854,855>,
<856,857,858>,
<814,859,860>,
<855,861,862>,
<863,864,846>,
<820,795,748>,
<865,866,843>,
<852,867,853>,
<868,869,870>,
<871,872,873>,
<750,845,840>,
<832,845,758>,
<874,875,515>,
<810,870,876>,
<853,877,878>,
<879,880,849>,
<848,881,882>,
<883,884,879>,
<848,885,886>,
<843,887,888>,
<889,890,891>,
<860,892,893>,
<778,800,850>,
<894,895,896>,
<897,898,899>,
<855,900,901>,
<902,903,875>,
<904,905,906>,
<860,907,908>,
<836,909,842>,
<910,911,912>,
<912,913,914>,
<900,915,916>,
<877,917,918>,
<824,919,825>,
<862,920,915>,
<867,921,877>,
<825,922,869>,
<872,923,924>,
<925,926,867>,
<869,927,870>,
<873,924,928>,
<884,929,930>,
<811,931,932>,
<933,934,824>,
<935,930,936>,
<870,931,876>,
<937,938,939>,
<935,940,880>,
<941,942,866>,
<881,943,938>,
<907,944,945>,
<866,946,947>,
<886,948,943>,
<893,949,907>,
<947,950,887>,
<811,951,952>,
<953,951,513>,
<893,954,955>,
<895,956,933>,
<798,957,923>,
<898,513,958>,
<864,959,960>,
<961,962,895>,
<899,958,963>,
<905,960,964>,
<965,966,835>,
<967,968,957>,
<906,964,969>,
<835,970,971>,
<972,914,968>,
<973,916,974>,
<971,975,909>,
<932,514,951>,
<976,977,968>,
<955,978,979>,
<923,980,981>,
<960,982,983>,
<984,985,962>,
<963,875,903>,
<964,983,986>,
<987,988,966>,
<962,985,989>,
<969,986,990>,
<966,991,970>,
<974,992,993>,
<970,994,975>,
<914,995,976>,
<916,996,992>,
<921,997,917>,
<934,998,919>,
<915,999,996>,
<926,1000,921>,
<919,889,922>,
<924,981,1001>,
<1002,1003,926>,
<922,891,927>,
<928,1001,1004>,
<930,1005,1006>,
<932,1007,1008>,
<936,1006,856>,
<927,1007,931>,
<939,1009,1010>,
<936,858,940>,
<1011,1012,942>,
<938,1013,1009>,
<949,1014,944>,
<942,1015,946>,
<943,1016,1013>,
<955,1017,949>,
<946,1018,950>,
<1019,1013,1016>,
<1015,1020,1018>,
<1021,993,992>,
<1022,1004,1001>,
<1023,515,514>,
<895,933,896>,
<1024,1006,1005>,
<1025,1026,998>,
<989,1027,1025>,
<1007,1028,1008>,
<1000,1029,997>,
<1030,981,980>,
<1008,1023,514>,
<1003,1031,1000>,
<1032,996,999>,
<812,953,1033>,
<1034,1035,1003>,
<1036,990,986>,
<998,1037,889>,
<1038,1039,988>,
<1040,992,996>,
<1041,977,976>,
<988,1042,991>,
<1043,986,983>,
<1044,980,977>,
<991,1045,994>,
<1046,1010,1009>,
<891,1047,1007>,
<985,1048,989>,
<1049,983,982>,
<1050,976,995>,
<1051,1052,985>,
<1017,1053,1014>,
<751,850,821>,
<1054,1001,981>,
<979,1055,1017>,
<910,972,797>,
<1056,1057,1012>,
<1058,979,978>,
<1059,1009,1013>,
<1012,1060,1015>,
<1061,856,1006>,
<1062,857,1063>,
<1062,1064,1021>,
<1028,1047,1062>,
<1059,1062,1046>,
<1057,1065,1062>,
<1035,1062,1031>,
<1062,1037,1026>,
<1039,1066,1062>,
<1022,1054,1062>,
<1043,1049,1062>,
<1062,1053,1055>,
<0,6,1>,
<3,9,4>,
<6,10,7>,
<3,12,8>,
<10,14,11>,
<12,16,13>,
<14,1067,15>,
<16,20,17>,
<18,12,3>,
<12,18,19>,
<19,31,20>,
<21,22,11>,
<22,23,7>,
<1,7,23>,
<24,27,25>,
<27,1068,28>,
<29,24,26>,
<30,20,31>,
<32,35,33>,
<21,15,35>,
<397,38,37>,
<36,503,37>,
<39,62,40>,
<42,1069,43>,
<41,40,45>,
<43,1069,47>,
<46,45,49>,
<48,47,51>,
<50,49,53>,
<52,51,55>,
<43,48,57>,
<48,52,58>,
<56,70,58>,
<49,60,59>,
<45,61,60>,
<61,45,40>,
<63,69,64>,
<65,64,66>,
<68,71,69>,
<70,56,71>,
<33,73,72>,
<73,53,59>,
<546,533,135>,
<542,116,533>,
<130,131,134>,
<533,116,118>,
<118,130,135>,
<533,118,135>,
<114,545,135>,
<133,134,131>,
<135,545,546>,
<139,182,1070>,
<139,1070,1071>,
<137,1072,103>,
<139,1071,1073>,
<74,137,103>,
<139,1073,1074>,
<139,1072,137>,
<1075,1076,331>,
<1074,1072,139>,
<140,74,144>,
<1075,331,1072>,
<1075,1072,1077>,
<1077,1072,1074>,
<74,103,75>,
<135,130,134>,
<132,133,142>,
<133,131,142>,
<107,96,99>,
<82,81,528>,
<96,100,99>,
<81,526,528>,
<528,537,79>,
<548,77,537>,
<79,100,95>,
<537,77,79>,
<95,100,96>,
<105,107,98>,
<528,79,82>,
<79,95,82>,
<107,99,98>,
<105,98,108>,
<132,142,140>,
<144,132,140>,
<75,105,108>,
<76,144,74>,
<75,108,76>,
<77,83,78>,
<80,86,81>,
<83,87,84>,
<80,89,85>,
<87,91,88>,
<89,93,90>,
<91,127,92>,
<93,97,94>,
<95,89,80>,
<89,95,96>,
<96,107,97>,
<98,99,88>,
<99,100,84>,
<78,84,100>,
<75,103,101>,
<103,1072,104>,
<105,75,102>,
<106,97,107>,
<108,110,109>,
<98,92,110>,
<66,113,112>,
<111,1078,112>,
<112,67,66>,
<114,135,115>,
<116,1079,117>,
<83,115,119>,
<117,1079,120>,
<87,119,122>,
<121,120,124>,
<123,122,126>,
<125,124,128>,
<117,121,130>,
<121,125,131>,
<129,142,131>,
<122,133,132>,
<119,134,133>,
<134,119,115>,
<74,141,136>,
<137,136,138>,
<140,143,141>,
<142,129,143>,
<109,145,144>,
<145,126,132>,
<146,152,147>,
<149,155,150>,
<152,156,153>,
<149,158,154>,
<156,160,157>,
<158,162,159>,
<160,1080,161>,
<162,166,163>,
<164,158,149>,
<158,164,165>,
<165,177,166>,
<167,168,157>,
<168,169,153>,
<147,153,169>,
<170,173,171>,
<173,204,174>,
<175,170,172>,
<176,166,177>,
<178,181,179>,
<167,161,181>,
<182,139,138>,
<138,183,182>,
<28,1068,182>,
<184,210,185>,
<187,1081,188>,
<186,185,190>,
<188,1081,192>,
<191,190,194>,
<193,192,196>,
<195,194,198>,
<197,196,200>,
<174,204,203>,
<202,288,203>,
<188,193,205>,
<193,197,206>,
<201,218,206>,
<194,208,207>,
<190,209,208>,
<209,190,185>,
<211,217,212>,
<213,212,214>,
<216,219,217>,
<218,201,219>,
<179,221,220>,
<221,198,207>,
<222,228,223>,
<225,231,226>,
<228,232,229>,
<225,234,230>,
<232,236,233>,
<234,238,235>,
<236,1082,237>,
<238,242,239>,
<240,234,225>,
<234,240,241>,
<241,253,242>,
<243,244,233>,
<244,245,229>,
<223,229,245>,
<246,249,247>,
<249,406,250>,
<251,246,248>,
<252,242,253>,
<254,257,255>,
<243,237,257>,
<323,260,259>,
<258,434,259>,
<261,284,262>,
<264,1083,265>,
<263,262,267>,
<265,1083,269>,
<268,267,271>,
<270,269,273>,
<272,271,275>,
<274,273,277>,
<1084,1070,1068>,
<1070,182,1068>,
<1085,1084,1068>,
<1085,1068,1086>,
<1086,1068,67>,
<1068,27,67>,
<1087,1086,67>,
<27,24,65>,
<112,1088,67>,
<1089,1087,67>,
<67,1088,1089>,
<65,67,27>,
<65,24,63>,
<63,24,34>,
<57,58,61>,
<1090,42,44>,
<44,57,62>,
<525,1090,44>,
<1091,549,39>,
<62,39,549>,
<59,60,70>,
<62,549,525>,
<61,62,57>,
<525,44,62>,
<58,60,61>,
<60,58,70>,
<72,59,68>,
<59,70,68>,
<34,72,63>,
<19,18,23>,
<5,4,542>,
<542,794,5>,
<1092,538,0>,
<1092,0,2>,
<5,794,2>,
<31,19,22>,
<794,1092,2>,
<23,22,19>,
<5,2,18>,
<18,2,23>,
<31,22,21>,
<72,68,63>,
<31,21,29>,
<32,34,24>,
<29,21,32>,
<265,270,279>,
<270,274,280>,
<278,291,280>,
<271,282,281>,
<267,283,282>,
<283,267,262>,
<285,290,286>,
<287,286,288>,
<289,292,290>,
<291,278,292>,
<255,294,293>,
<294,275,281>,
<295,301,296>,
<298,304,299>,
<301,305,302>,
<298,307,303>,
<305,309,306>,
<307,311,308>,
<309,1093,310>,
<311,315,312>,
<313,307,298>,
<307,313,314>,
<314,326,315>,
<316,317,306>,
<317,318,302>,
<296,302,318>,
<319,322,320>,
<322,260,323>,
<324,319,321>,
<325,315,326>,
<327,330,328>,
<316,310,330>,
<331,215,214>,
<214,332,331>,
<104,1072,331>,
<333,358,334>,
<336,1094,337>,
<335,334,339>,
<337,1094,341>,
<340,339,343>,
<342,341,345>,
<344,343,347>,
<346,345,349>,
<300,299,567>,
<1095,567,299>,
<576,586,295>,
<297,318,313>,
<576,295,297>,
<300,567,297>,
<326,314,317>,
<313,300,297>,
<318,314,313>,
<567,576,297>,
<314,318,317>,
<326,317,316>,
<324,326,316>,
<324,316,327>,
<319,324,327>,
<319,327,329>,
<322,319,359>,
<319,329,359>,
<260,322,361>,
<355,356,365>,
<329,367,359>,
<356,357,354>,
<358,333,583>,
<1096,583,333>,
<573,575,336>,
<338,353,358>,
<573,336,338>,
<358,583,573>,
<357,353,354>,
<573,338,358>,
<353,357,358>,
<367,355,363>,
<356,354,365>,
<355,365,363>,
<367,363,359>,
<322,359,361>,
<260,361,351>,
<260,1097,1098>,
<258,260,1098>,
<425,426,429>,
<564,410,562>,
<412,425,430>,
<562,410,412>,
<1099,581,407>,
<430,407,581>,
<427,428,437>,
<430,581,562>,
<428,426,437>,
<429,426,428>,
<562,412,430>,
<425,429,430>,
<439,427,435>,
<427,437,435>,
<439,435,431>,
<403,439,431>,
<403,431,393>,
<401,403,393>,
<431,396,393>,
<392,391,388>,
<390,401,398>,
<371,392,387>,
<571,369,565>,
<556,565,371>,
<373,1100,556>,
<388,387,392>,
<374,373,556>,
<565,369,371>,
<556,371,374>,
<387,374,371>,
<400,391,390>,
<391,400,388>,
<400,390,398>,
<398,401,393>,
<431,433,396>,
<396,433,38>,
<433,258,38>,
<38,258,1101>,
<36,38,1102>,
<253,241,244>,
<227,226,580>,
<226,578,580>,
<580,591,224>,
<597,222,591>,
<245,244,241>,
<591,222,224>,
<580,224,227>,
<240,227,224>,
<241,240,245>,
<224,245,240>,
<244,243,253>,
<251,253,243>,
<251,243,254>,
<251,254,246>,
<246,254,256>,
<246,256,285>,
<249,246,287>,
<406,249,202>,
<1103,1097,260>,
<502,36,468>,
<282,283,280>,
<284,261,594>,
<598,594,261>,
<587,589,264>,
<266,279,284>,
<587,264,266>,
<284,594,587>,
<281,282,291>,
<284,587,266>,
<279,283,284>,
<279,280,283>,
<282,280,291>,
<281,291,289>,
<494,495,498>,
<530,479,543>,
<543,479,481>,
<1104,551,476>,
<499,476,551>,
<497,498,495>,
<496,497,506>,
<499,551,543>,
<543,481,499>,
<481,494,499>,
<498,499,494>,
<508,496,504>,
<495,506,497>,
<496,506,504>,
<474,508,500>,
<508,504,500>,
<472,474,465>,
<474,500,465>,
<293,281,289>,
<293,289,285>,
<560,441,553>,
<558,553,443>,
<445,1090,558>,
<446,445,558>,
<553,441,443>,
<558,443,446>,
<459,446,443>,
<471,460,463>,
<460,464,463>,
<443,464,459>,
<464,460,459>,
<469,471,462>,
<471,463,462>,
<469,462,472>,
<465,469,472>,
<293,285,256>,
<465,500,502>,
<468,465,502>,
<177,165,168>,
<151,150,512>,
<165,164,169>,
<150,510,512>,
<512,517,148>,
<522,146,517>,
<169,168,165>,
<517,146,148>,
<148,169,164>,
<151,512,148>,
<164,151,148>,
<177,168,167>,
<175,177,167>,
<175,167,178>,
<170,175,178>,
<170,178,180>,
<173,170,213>,
<170,180,211>,
<204,173,215>,
<207,208,218>,
<180,220,211>,
<208,209,206>,
<210,184,520>,
<518,520,184>,
<1105,526,189>,
<189,205,210>,
<526,187,189>,
<210,520,1105>,
<209,205,206>,
<1105,189,210>,
<205,209,210>,
<220,207,216>,
<208,206,218>,
<207,218,216>,
<220,216,211>,
<170,211,213>,
<173,213,215>,
<215,331,1076>,
<215,1076,1106>,
<202,204,1107>,
<204,215,1108>,
<215,1106,1109>,
<287,202,249>,
<215,1109,1108>,
<1078,468,36>,
<1088,112,1110>,
<1110,112,1078>,
<285,287,246>,
<204,1108,1111>,
<1110,1078,1112>,
<1112,1078,1113>,
<204,1111,1114>,
<204,1114,1107>,
<1113,36,1115>,
<1113,1078,36>,
<202,1107,1116>,
<202,1116,1117>,
<1118,1115,36>,
<1102,1118,36>,
<202,1117,1119>,
<202,1119,406>,
<1120,1102,38>,
<1121,1120,38>,
<1119,1122,406>,
<1122,1123,406>,
<1101,1121,38>,
<1124,1101,258>,
<1123,1125,406>,
<1125,351,406>,
<1126,1124,258>,
<1098,1126,258>,
<1127,351,1125>,
<1128,351,1127>,
<352,1103,260>,
<352,351,1128>,
<337,342,353>,
<342,346,354>,
<350,365,354>,
<343,356,355>,
<339,357,356>,
<357,339,334>,
<359,364,360>,
<361,360,362>,
<363,366,364>,
<365,350,366>,
<328,368,367>,
<368,347,355>,
<369,375,370>,
<372,378,373>,
<375,379,376>,
<372,381,377>,
<379,383,380>,
<381,385,382>,
<383,1129,384>,
<385,389,386>,
<387,381,372>,
<381,387,388>,
<388,400,389>,
<390,391,380>,
<391,392,376>,
<370,376,392>,
<393,396,394>,
<396,38,397>,
<398,393,395>,
<399,389,400>,
<401,404,402>,
<390,384,404>,
<250,406,405>,
<351,362,405>,
<407,430,408>,
<410,1130,411>,
<409,408,413>,
<411,1130,415>,
<414,413,417>,
<416,415,419>,
<418,417,421>,
<420,419,423>,
<411,416,425>,
<416,420,426>,
<424,437,426>,
<417,428,427>,
<413,429,428>,
<429,413,408>,
<431,436,432>,
<433,432,434>,
<435,438,436>,
<437,424,438>,
<402,440,439>,
<440,421,427>,
<441,447,442>,
<444,450,445>,
<447,451,448>,
<444,453,449>,
<451,455,452>,
<453,457,454>,
<455,1131,456>,
<457,461,458>,
<459,453,444>,
<453,459,460>,
<460,471,461>,
<462,463,452>,
<463,464,448>,
<442,448,464>,
<465,468,466>,
<468,1078,111>,
<469,465,467>,
<470,461,471>,
<472,475,473>,
<462,456,475>,
<476,499,477>,
<479,1132,480>,
<478,477,482>,
<480,1132,484>,
<483,482,486>,
<485,484,488>,
<487,486,490>,
<489,488,492>,
<480,485,494>,
<485,489,495>,
<493,506,495>,
<486,497,496>,
<482,498,497>,
<498,482,477>,
<500,505,501>,
<502,501,503>,
<504,507,505>,
<506,493,507>,
<473,509,508>,
<509,490,496>,
<510,626,511>,
<875,958,515>,
<513,951,514>,
<515,958,513>,
<512,511,516>,
<518,643,519>,
<517,516,521>,
<1090,525,524>,
<549,523,525>,
<524,559,1090>,
<526,607,527>,
<607,526,1105>,
<1105,520,529>,
<529,607,1105>,
<530,543,531>,
<533,546,534>,
<528,527,536>,
<538,1092,539>,
<533,535,541>,
<543,551,544>,
<545,547,534>,
<537,536,547>,
<549,1091,550>,
<551,1104,552>,
<553,558,554>,
<556,1100,532>,
<558,1090,559>,
<560,553,555>,
<562,570,563>,
<565,556,557>,
<567,1095,563>,
<562,581,569>,
<571,565,566>,
<573,584,574>,
<576,567,568>,
<578,647,579>,
<581,1099,582>,
<583,593,584>,
<576,577,585>,
<587,595,588>,
<580,579,590>,
<583,1096,592>,
<594,599,595>,
<591,590,596>,
<598,614,599>,
<563,570,600>,
<590,651,602>,
<523,550,603>,
<570,569,605>,
<595,610,606>,
<607,621,608>,
<569,582,609>,
<599,615,610>,
<527,608,611>,
<555,554,612>,
<614,666,615>,
<536,611,616>,
<561,555,613>,
<568,563,601>,
<535,534,619>,
<529,639,621>,
<577,568,618>,
<547,616,619>,
<532,531,623>,
<577,622,625>,
<626,677,627>,
<531,544,628>,
<584,632,629>,
<511,627,630>,
<544,552,631>,
<593,638,632>,
<516,630,633>,
<535,620,634>,
<635,541,634>,
<593,592,637>,
<519,644,639>,
<554,559,640>,
<539,635,636>,
<557,532,624>,
<643,692,644>,
<540,539,641>,
<566,557,642>,
<647,696,648>,
<559,524,649>,
<572,566,646>,
<579,648,651>,
<524,523,604>,
<649,655,652>,
<646,695,653>,
<648,697,654>,
<604,658,655>,
<600,659,656>,
<651,654,657>,
<603,708,658>,
<605,662,659>,
<610,663,660>,
<621,672,661>,
<609,715,662>,
<615,667,663>,
<608,661,664>,
<612,689,665>,
<666,1133,667>,
<616,611,664>,
<613,665,669>,
<601,656,670>,
<619,674,671>,
<639,688,672>,
<618,670,673>,
<619,616,668>,
<623,679,675>,
<622,673,676>,
<677,1134,678>,
<628,682,679>,
<632,683,680>,
<627,678,681>,
<631,736,682>,
<638,687,683>,
<633,630,681>,
<620,671,685>,
<634,685,686>,
<637,742,687>,
<644,693,688>,
<612,640,652>,
<641,636,686>,
<624,675,691>,
<692,1135,693>,
<641,690,694>,
<642,691,695>,
<696,1136,697>,
<690,745,698>,
<691,746,699>,
<697,1136,700>,
<655,705,702>,
<695,699,703>,
<654,697,701>,
<658,709,705>,
<659,710,706>,
<657,654,704>,
<708,1137,709>,
<662,716,710>,
<660,663,711>,
<661,672,713>,
<715,1138,716>,
<663,667,717>,
<664,661,714>,
<689,744,719>,
<667,1133,720>,
<668,664,718>,
<665,719,722>,
<656,706,723>,
<671,674,724>,
<672,688,726>,
<670,723,727>,
<668,721,724>,
<679,732,728>,
<676,673,727>,
<678,1134,730>,
<682,737,732>,
<680,683,733>,
<681,678,731>,
<736,1139,737>,
<683,687,738>,
<684,681,735>,
<685,671,725>,
<685,740,741>,
<742,1140,738>,
<688,693,743>,
<652,702,744>,
<686,741,745>,
<675,728,746>,
<693,1135,747>,
<743,754,748>,
<744,702,749>,
<745,741,751>,
<728,1141,753>,
<747,819,754>,
<698,745,752>,
<699,746,753>,
<700,1142,757>,
<702,705,758>,
<703,699,756>,
<701,757,760>,
<705,709,761>,
<706,710,762>,
<704,760,764>,
<709,1137,765>,
<710,716,766>,
<711,770,767>,
<713,779,768>,
<716,1138,769>,
<717,773,770>,
<714,768,771>,
<719,744,750>,
<720,838,773>,
<718,771,774>,
<722,719,772>,
<723,706,763>,
<725,724,777>,
<726,748,779>,
<727,723,776>,
<721,774,777>,
<728,732,781>,
<727,780,783>,
<730,1143,784>,
<732,737,785>,
<733,789,786>,
<731,784,787>,
<737,1139,788>,
<738,793,789>,
<735,787,790>,
<725,778,791>,
<741,740,791>,
<738,1140,792>,
<539,1092,635>,
<794,542,541>,
<635,1092,794>,
<796,771,768>,
<768,779,795>,
<872,799,798>,
<797,967,798>,
<774,839,800>,
<767,770,802>,
<773,801,770>,
<780,807,803>,
<782,781,805>,
<785,804,781>,
<807,780,776>,
<776,763,806>,
<785,788,808>,
<787,816,809>,
<952,812,811>,
<810,876,811>,
<793,792,813>,
<816,787,784>,
<784,1143,815>,
<818,760,757>,
<757,1142,817>,
<819,1144,820>,
<755,752,821>,
<763,762,806>,
<766,823,762>,
<844,826,825>,
<825,869,844>,
<759,756,827>,
<786,789,829>,
<793,814,789>,
<760,818,830>,
<761,765,831>,
<827,756,753>,
<753,1141,833>,
<971,836,835>,
<834,965,835>,
<766,769,837>,
<838,851,801>,
<771,796,839>,
<775,772,840>,
<818,817,836>,
<836,842,818>,
<817,834,836>,
<809,816,888>,
<815,865,843>,
<843,888,816>,
<839,796,868>,
<795,820,826>,
<1144,894,896>,
<820,1144,896>,
<844,868,796>,
<820,896,826>,
<844,796,795>,
<841,840,799>,
<845,832,910>,
<831,1145,832>,
<832,1145,910>,
<799,871,841>,
<845,910,797>,
<799,840,797>,
<827,833,863>,
<846,904,828>,
<827,863,846>,
<882,805,848>,
<804,808,847>,
<848,805,804>,
<807,806,883>,
<879,849,807>,
<807,883,879>,
<850,800,812>,
<839,868,810>,
<800,839,810>,
<822,821,1033>,
<1033,897,822>,
<800,810,812>,
<1033,821,812>,
<878,802,801>,
<801,851,853>,
<853,878,801>,
<901,806,855>,
<823,837,854>,
<855,806,823>,
<856,1063,857>,
<908,829,814>,
<814,813,859>,
<860,908,814>,
<855,854,861>,
<905,846,864>,
<863,1146,864>,
<779,748,795>,
<754,820,748>,
<947,843,866>,
<865,941,866>,
<852,925,867>,
<868,844,869>,
<871,799,872>,
<840,772,750>,
<750,749,845>,
<749,758,845>,
<761,832,758>,
<874,902,875>,
<810,868,870>,
<918,878,877>,
<853,867,877>,
<879,935,880>,
<937,882,881>,
<848,886,881>,
<935,879,884>,
<883,1147,884>,
<848,847,885>,
<843,947,887>,
<889,1037,890>,
<860,859,892>,
<850,791,778>,
<778,777,800>,
<894,961,895>,
<897,1033,898>,
<973,901,900>,
<855,862,900>,
<902,1148,903>,
<904,846,905>,
<945,908,907>,
<860,893,907>,
<836,971,909>,
<910,1145,911>,
<912,911,913>,
<900,862,915>,
<877,921,917>,
<824,934,919>,
<862,861,920>,
<867,926,921>,
<825,919,922>,
<872,798,923>,
<925,1002,926>,
<869,922,927>,
<873,872,924>,
<884,1147,929>,
<811,876,931>,
<933,956,934>,
<935,884,930>,
<870,927,931>,
<937,881,938>,
<935,936,940>,
<941,1011,942>,
<881,886,943>,
<907,949,944>,
<866,942,946>,
<886,885,948>,
<893,955,949>,
<947,946,950>,
<811,932,951>,
<953,952,951>,
<893,892,954>,
<895,962,956>,
<798,967,957>,
<898,953,513>,
<864,1146,959>,
<961,984,962>,
<899,898,958>,
<905,864,960>,
<965,987,966>,
<967,972,968>,
<906,905,964>,
<835,966,970>,
<972,912,914>,
<973,900,916>,
<971,970,975>,
<932,1008,514>,
<957,968,977>,
<914,976,968>,
<977,980,957>,
<955,954,978>,
<923,957,980>,
<960,959,982>,
<984,1051,985>,
<963,958,875>,
<964,960,983>,
<987,1038,988>,
<1025,934,956>,
<956,962,989>,
<989,1025,956>,
<969,964,986>,
<966,988,991>,
<974,916,992>,
<970,991,994>,
<914,913,995>,
<916,915,996>,
<921,1000,997>,
<934,1025,998>,
<915,920,999>,
<926,1003,1000>,
<919,998,889>,
<924,923,981>,
<1002,1034,1003>,
<922,889,891>,
<928,924,1001>,
<930,929,1005>,
<932,931,1007>,
<936,930,1006>,
<927,891,1007>,
<939,938,1009>,
<936,856,858>,
<1011,1056,1012>,
<938,943,1013>,
<949,1017,1014>,
<942,1012,1015>,
<943,948,1016>,
<955,979,1017>,
<946,1015,1018>,
<1019,1059,1013>,
<1015,1060,1020>,
<1021,1064,993>,
<1022,1149,1004>,
<1023,874,515>,
<826,896,933>,
<933,824,826>,
<1024,1061,1006>,
<1025,1027,1026>,
<989,1048,1027>,
<1007,1047,1028>,
<1000,1031,1029>,
<1030,1054,981>,
<1008,1028,1023>,
<1003,1035,1031>,
<1032,1040,996>,
<898,1033,953>,
<812,952,953>,
<1034,1150,1035>,
<1036,1151,990>,
<998,1026,1037>,
<1038,1066,1039>,
<1040,1021,992>,
<1041,1044,977>,
<988,1039,1042>,
<1043,1036,986>,
<1044,1030,980>,
<991,1042,1045>,
<1046,1152,1010>,
<891,890,1047>,
<985,1052,1048>,
<1049,1043,983>,
<1050,1041,976>,
<1051,1153,1052>,
<1017,1055,1053>,
<821,752,751>,
<751,791,850>,
<1054,1022,1001>,
<979,1154,1055>,
<967,797,972>,
<910,912,972>,
<1056,1065,1057>,
<1058,1154,979>,
<1059,1046,1009>,
<1012,1057,1060>,
<1061,1063,856>,
<1063,1061,1062>,
<1024,1062,1061>,
<1021,1040,1062>,
<1032,1062,1040>,
<1062,1148,902>,
<902,874,1062>,
<1023,1028,1062>,
<1047,890,1062>,
<1062,874,1023>,
<1152,1046,1062>,
<1059,1019,1062>,
<1062,1020,1060>,
<1060,1057,1062>,
<1029,1031,1062>,
<1035,1150,1062>,
<1026,1027,1062>,
<1048,1052,1062>,
<1153,1062,1052>,
<890,1037,1062>,
<1027,1048,1062>,
<1062,1045,1042>,
<1042,1039,1062>,
<1044,1041,1062>,
<1050,1062,1041>,
<1149,1022,1062>,
<1054,1030,1062>,
<1044,1062,1030>,
<1062,1151,1036>,
<1036,1043,1062>,
<1055,1154,1062>,
<1058,1062,1154>
}
normal_indices{
1812,
<0,0,0>,
<1,1,1>,
<2,2,2>,
<2,2,2>,
<3,4,5>,
<6,7,8>,
<2,2,2>,
<2,2,2>,
<9,9,9>,
<10,10,10>,
<11,11,11>,
<12,12,12>,
<13,13,13>,
<14,14,14>,
<15,15,15>,
<16,16,16>,
<17,18,19>,
<20,20,20>,
<21,21,21>,
<22,22,22>,
<23,23,23>,
<24,24,24>,
<25,26,27>,
<2,2,2>,
<2,2,2>,
<28,28,28>,
<29,29,29>,
<2,2,2>,
<2,2,2>,
<30,30,30>,
<31,31,31>,
<32,32,32>,
<33,33,33>,
<34,34,34>,
<35,35,35>,
<36,36,36>,
<37,37,37>,
<38,38,38>,
<39,39,39>,
<40,40,40>,
<41,41,41>,
<2,2,2>,
<42,42,42>,
<43,43,43>,
<2,2,2>,
<2,2,2>,
<44,45,46>,
<47,48,49>,
<2,2,2>,
<2,2,2>,
<50,50,50>,
<51,51,51>,
<52,52,52>,
<53,53,53>,
<54,54,54>,
<55,55,55>,
<56,56,56>,
<57,57,57>,
<58,59,60>,
<61,61,61>,
<62,62,62>,
<63,63,63>,
<64,64,64>,
<65,65,65>,
<66,67,67>,
<2,2,2>,
<2,2,2>,
<68,68,68>,
<69,69,69>,
<2,2,2>,
<2,2,2>,
<70,70,70>,
<71,71,71>,
<72,72,72>,
<73,73,73>,
<74,74,74>,
<75,75,75>,
<76,76,76>,
<77,77,77>,
<78,78,78>,
<79,79,79>,
<80,81,81>,
<82,82,82>,
<83,83,83>,
<84,84,84>,
<2,2,2>,
<2,2,2>,
<85,86,87>,
<88,89,90>,
<2,2,2>,
<2,2,2>,
<91,91,91>,
<92,92,92>,
<93,93,93>,
<94,94,94>,
<95,95,95>,
<96,96,96>,
<97,97,97>,
<98,98,98>,
<99,100,101>,
<102,102,102>,
<103,103,103>,
<104,104,104>,
<105,105,105>,
<106,106,106>,
<107,108,108>,
<2,2,2>,
<2,2,2>,
<109,109,109>,
<110,110,110>,
<2,2,2>,
<2,2,2>,
<111,111,111>,
<112,112,112>,
<113,113,113>,
<114,114,114>,
<115,115,115>,
<116,116,116>,
<117,117,117>,
<118,118,118>,
<119,119,119>,
<120,120,120>,
<121,121,121>,
<122,122,122>,
<123,123,123>,
<124,124,124>,
<125,125,125>,
<2,2,2>,
<2,2,2>,
<126,127,128>,
<129,130,131>,
<2,2,2>,
<2,2,2>,
<132,132,132>,
<133,133,133>,
<134,134,134>,
<135,135,135>,
<136,136,136>,
<137,137,137>,
<138,138,138>,
<139,139,139>,
<140,141,142>,
<143,143,143>,
<144,144,144>,
<145,145,145>,
<146,146,146>,
<147,147,147>,
<148,149,149>,
<2,2,2>,
<2,2,2>,
<150,150,150>,
<151,151,151>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<152,152,152>,
<153,153,153>,
<154,154,154>,
<155,155,155>,
<156,156,156>,
<157,157,157>,
<158,158,158>,
<159,159,159>,
<160,160,160>,
<161,161,161>,
<162,162,162>,
<163,163,163>,
<164,164,164>,
<165,165,165>,
<2,2,2>,
<2,2,2>,
<166,167,168>,
<169,170,171>,
<2,2,2>,
<2,2,2>,
<172,172,172>,
<173,173,173>,
<174,174,174>,
<175,175,175>,
<176,176,176>,
<177,177,177>,
<178,178,178>,
<179,179,179>,
<180,181,182>,
<183,183,183>,
<184,184,184>,
<185,185,185>,
<186,186,186>,
<187,187,187>,
<188,189,190>,
<2,2,2>,
<2,2,2>,
<191,191,191>,
<192,192,192>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<193,193,193>,
<194,194,194>,
<195,195,195>,
<196,196,196>,
<197,197,197>,
<198,198,198>,
<199,199,199>,
<200,200,200>,
<201,201,201>,
<202,202,202>,
<203,204,205>,
<206,206,206>,
<207,207,207>,
<208,208,208>,
<2,2,2>,
<2,2,2>,
<209,210,211>,
<212,213,214>,
<2,2,2>,
<2,2,2>,
<215,215,215>,
<216,216,216>,
<217,217,217>,
<218,218,218>,
<219,219,219>,
<220,220,220>,
<221,221,221>,
<222,222,222>,
<223,224,225>,
<226,226,226>,
<227,227,227>,
<228,228,228>,
<229,229,229>,
<230,230,230>,
<231,232,232>,
<2,2,2>,
<2,2,2>,
<233,233,233>,
<234,234,234>,
<2,2,2>,
<2,2,2>,
<235,235,235>,
<236,236,236>,
<237,237,237>,
<238,238,238>,
<239,239,239>,
<240,240,240>,
<241,241,241>,
<242,242,242>,
<243,243,243>,
<244,244,244>,
<245,245,245>,
<246,246,246>,
<247,247,247>,
<248,248,248>,
<2,2,2>,
<2,2,2>,
<249,250,251>,
<252,253,254>,
<2,2,2>,
<2,2,2>,
<255,255,255>,
<256,256,256>,
<257,257,257>,
<258,258,258>,
<259,259,259>,
<260,260,260>,
<261,261,261>,
<262,262,262>,
<263,264,265>,
<266,266,266>,
<267,267,267>,
<268,268,268>,
<269,269,269>,
<270,271,271>,
<2,2,2>,
<2,2,2>,
<272,272,272>,
<273,273,273>,
<2,2,2>,
<2,2,2>,
<274,274,274>,
<275,275,275>,
<276,276,276>,
<277,277,277>,
<278,278,278>,
<279,279,279>,
<280,280,280>,
<281,281,281>,
<282,282,282>,
<283,283,283>,
<284,284,284>,
<285,285,285>,
<286,286,287>,
<288,288,288>,
<289,290,291>,
<292,292,292>,
<293,293,293>,
<294,294,294>,
<295,295,296>,
<297,298,298>,
<299,299,299>,
<300,300,300>,
<301,302,301>,
<303,303,303>,
<304,304,304>,
<305,306,306>,
<307,307,307>,
<308,308,308>,
<309,309,309>,
<310,310,310>,
<311,311,311>,
<312,312,312>,
<313,313,313>,
<314,314,314>,
<315,316,317>,
<318,319,320>,
<321,321,321>,
<322,323,323>,
<324,324,324>,
<325,326,326>,
<327,327,328>,
<329,329,330>,
<331,331,331>,
<332,333,332>,
<334,334,334>,
<335,335,336>,
<337,337,337>,
<338,338,338>,
<339,340,339>,
<341,341,341>,
<342,342,342>,
<343,343,343>,
<344,344,344>,
<345,345,345>,
<346,347,347>,
<348,348,348>,
<349,349,350>,
<351,351,351>,
<352,352,352>,
<353,354,353>,
<355,355,355>,
<356,356,356>,
<357,357,357>,
<358,358,358>,
<359,359,359>,
<360,361,361>,
<362,362,362>,
<363,364,364>,
<365,365,365>,
<366,366,366>,
<367,368,367>,
<369,369,369>,
<370,370,370>,
<371,371,371>,
<372,372,372>,
<373,373,373>,
<374,375,376>,
<377,377,377>,
<378,378,378>,
<379,379,379>,
<380,380,380>,
<381,382,381>,
<383,383,383>,
<384,384,384>,
<385,385,385>,
<386,386,387>,
<388,388,388>,
<389,389,389>,
<390,390,390>,
<391,391,391>,
<392,392,392>,
<393,394,393>,
<395,396,396>,
<397,397,397>,
<398,398,398>,
<399,399,399>,
<400,400,400>,
<401,401,401>,
<402,402,402>,
<403,403,403>,
<404,404,404>,
<405,405,405>,
<406,406,406>,
<407,407,407>,
<408,408,408>,
<409,409,409>,
<410,410,410>,
<411,411,411>,
<412,412,412>,
<413,413,413>,
<414,414,414>,
<415,415,415>,
<416,416,416>,
<417,417,417>,
<418,418,418>,
<419,419,419>,
<420,420,420>,
<421,421,421>,
<422,422,422>,
<423,423,423>,
<424,424,424>,
<425,425,425>,
<426,426,426>,
<427,427,427>,
<428,428,428>,
<429,429,429>,
<430,430,430>,
<431,431,431>,
<432,432,432>,
<433,433,433>,
<434,434,434>,
<435,435,435>,
<436,436,436>,
<437,437,437>,
<438,438,438>,
<439,439,439>,
<440,440,440>,
<441,441,441>,
<442,442,442>,
<443,443,443>,
<444,444,444>,
<445,446,445>,
<447,447,447>,
<448,448,448>,
<449,450,449>,
<451,452,451>,
<453,453,453>,
<454,454,454>,
<455,455,455>,
<456,456,456>,
<457,458,458>,
<459,459,459>,
<460,460,460>,
<461,461,461>,
<462,462,462>,
<463,463,463>,
<464,464,464>,
<465,465,465>,
<466,467,466>,
<468,468,468>,
<469,470,471>,
<472,472,472>,
<473,473,473>,
<474,475,474>,
<476,476,476>,
<477,478,478>,
<479,479,479>,
<480,481,481>,
<482,482,482>,
<483,483,483>,
<484,484,484>,
<485,485,485>,
<486,486,486>,
<487,487,487>,
<488,489,488>,
<490,491,492>,
<493,493,493>,
<494,494,494>,
<495,496,497>,
<498,499,500>,
<501,501,501>,
<502,503,503>,
<504,504,504>,
<505,506,507>,
<508,509,509>,
<510,511,510>,
<512,512,512>,
<513,514,515>,
<516,517,516>,
<518,519,518>,
<520,521,521>,
<522,522,522>,
<523,524,523>,
<525,525,525>,
<526,527,527>,
<528,528,528>,
<529,530,531>,
<532,533,534>,
<535,535,535>,
<536,536,537>,
<538,538,538>,
<539,539,539>,
<540,541,540>,
<542,542,542>,
<543,544,544>,
<545,546,547>,
<548,549,549>,
<550,550,550>,
<551,552,552>,
<553,554,554>,
<555,556,556>,
<557,558,559>,
<560,561,561>,
<562,562,562>,
<563,564,565>,
<566,567,567>,
<568,568,568>,
<569,570,569>,
<571,571,571>,
<572,572,572>,
<573,573,573>,
<574,574,574>,
<575,576,577>,
<578,578,578>,
<579,580,581>,
<582,582,582>,
<583,583,583>,
<584,584,584>,
<585,586,587>,
<588,588,588>,
<589,589,589>,
<590,590,590>,
<591,591,591>,
<592,592,592>,
<593,593,593>,
<594,594,594>,
<595,595,595>,
<596,596,596>,
<597,597,597>,
<598,599,599>,
<600,600,600>,
<601,601,601>,
<602,602,602>,
<603,603,603>,
<604,605,605>,
<606,607,607>,
<608,608,608>,
<609,610,611>,
<612,613,614>,
<615,615,616>,
<617,617,618>,
<619,620,621>,
<622,623,624>,
<625,626,626>,
<627,627,628>,
<629,629,630>,
<631,632,633>,
<634,634,634>,
<635,636,637>,
<638,638,638>,
<639,639,639>,
<640,640,640>,
<641,641,641>,
<642,643,643>,
<644,644,644>,
<645,645,645>,
<646,646,646>,
<647,647,647>,
<648,649,649>,
<650,650,650>,
<651,651,651>,
<652,653,654>,
<655,655,655>,
<656,656,656>,
<657,657,657>,
<658,658,658>,
<659,659,659>,
<660,660,660>,
<661,661,661>,
<662,662,662>,
<663,663,663>,
<664,664,664>,
<665,665,665>,
<666,666,666>,
<667,667,667>,
<668,669,669>,
<670,670,670>,
<671,671,671>,
<672,672,672>,
<673,674,674>,
<675,676,676>,
<677,677,677>,
<678,678,678>,
<679,679,679>,
<680,680,681>,
<682,682,682>,
<683,684,685>,
<686,686,686>,
<687,687,687>,
<688,688,689>,
<690,691,691>,
<692,692,693>,
<694,695,696>,
<697,697,697>,
<698,699,699>,
<700,700,700>,
<701,701,702>,
<703,704,704>,
<705,705,705>,
<706,706,706>,
<707,708,708>,
<709,710,710>,
<711,712,713>,
<714,715,714>,
<716,716,716>,
<717,717,717>,
<718,719,718>,
<720,720,720>,
<721,721,721>,
<722,723,723>,
<724,724,724>,
<725,725,725>,
<726,727,727>,
<728,729,730>,
<731,731,731>,
<732,732,733>,
<734,734,735>,
<736,736,736>,
<737,737,737>,
<738,739,739>,
<740,740,740>,
<741,741,741>,
<742,742,742>,
<743,743,743>,
<744,744,744>,
<745,745,745>,
<746,746,746>,
<747,748,748>,
<749,749,749>,
<750,750,750>,
<751,751,752>,
<753,753,753>,
<754,755,755>,
<756,756,756>,
<757,757,758>,
<759,759,759>,
<760,760,760>,
<761,761,761>,
<762,762,762>,
<763,763,764>,
<765,765,766>,
<767,767,767>,
<768,769,769>,
<770,770,770>,
<771,771,771>,
<772,772,773>,
<774,774,775>,
<776,776,776>,
<777,777,777>,
<778,778,778>,
<779,779,779>,
<780,781,782>,
<783,783,783>,
<784,784,784>,
<785,785,785>,
<786,786,787>,
<788,788,788>,
<789,789,789>,
<790,790,790>,
<791,791,791>,
<792,792,792>,
<793,793,793>,
<794,794,794>,
<795,795,795>,
<796,796,796>,
<797,797,797>,
<798,799,798>,
<800,800,800>,
<801,801,801>,
<802,802,803>,
<804,805,804>,
<806,806,806>,
<807,807,807>,
<808,808,808>,
<809,809,809>,
<810,811,810>,
<812,812,812>,
<813,813,814>,
<815,816,816>,
<817,818,819>,
<820,820,820>,
<821,821,821>,
<822,822,822>,
<823,823,823>,
<824,824,824>,
<825,825,825>,
<826,826,826>,
<827,827,827>,
<828,828,828>,
<829,829,829>,
<830,830,830>,
<831,832,832>,
<833,834,833>,
<835,835,835>,
<836,836,836>,
<837,837,837>,
<838,839,839>,
<840,841,840>,
<842,843,843>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<845,845,845>,
<846,846,846>,
<2,2,2>,
<2,2,2>,
<3,847,4>,
<6,848,7>,
<2,2,2>,
<2,2,2>,
<849,849,849>,
<850,850,850>,
<851,851,851>,
<852,852,852>,
<853,853,853>,
<854,854,854>,
<855,855,855>,
<856,856,856>,
<17,857,18>,
<858,858,858>,
<21,21,21>,
<859,859,859>,
<860,860,860>,
<861,861,861>,
<862,862,862>,
<25,25,26>,
<2,2,2>,
<2,2,2>,
<863,863,863>,
<864,864,864>,
<2,2,2>,
<2,2,2>,
<865,865,865>,
<866,866,866>,
<867,867,867>,
<868,868,868>,
<869,869,869>,
<870,870,870>,
<871,871,871>,
<872,872,872>,
<873,873,873>,
<874,874,874>,
<40,40,40>,
<875,875,875>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<876,876,876>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<877,877,877>,
<878,878,878>,
<2,2,2>,
<2,2,2>,
<44,879,45>,
<47,880,48>,
<2,2,2>,
<2,2,2>,
<881,881,881>,
<882,882,882>,
<883,883,883>,
<884,884,884>,
<885,885,885>,
<886,886,886>,
<887,887,887>,
<888,888,888>,
<58,889,59>,
<890,890,890>,
<891,891,891>,
<892,892,892>,
<893,894,895>,
<896,896,896>,
<895,897,893>,
<898,898,898>,
<66,66,67>,
<2,2,2>,
<2,2,2>,
<899,899,899>,
<900,900,900>,
<2,2,2>,
<2,2,2>,
<901,901,901>,
<902,902,902>,
<903,903,903>,
<904,904,904>,
<905,905,905>,
<906,906,906>,
<907,907,907>,
<908,908,908>,
<909,909,909>,
<910,910,910>,
<80,80,81>,
<911,911,911>,
<912,912,912>,
<913,913,913>,
<2,2,2>,
<2,2,2>,
<85,914,86>,
<88,915,89>,
<2,2,2>,
<2,2,2>,
<916,916,916>,
<917,917,917>,
<918,918,918>,
<919,919,919>,
<920,920,920>,
<921,921,921>,
<922,922,922>,
<923,923,923>,
<99,924,100>,
<925,925,925>,
<103,103,103>,
<926,926,926>,
<927,928,929>,
<929,930,927>,
<931,931,931>,
<932,932,932>,
<107,107,108>,
<2,2,2>,
<2,2,2>,
<933,933,933>,
<934,934,934>,
<2,2,2>,
<2,2,2>,
<935,935,935>,
<936,936,936>,
<937,937,937>,
<938,938,938>,
<939,939,939>,
<940,940,940>,
<941,941,941>,
<942,942,942>,
<943,943,943>,
<944,944,944>,
<945,945,945>,
<946,946,946>,
<122,122,122>,
<947,947,947>,
<948,948,948>,
<949,949,949>,
<2,2,2>,
<2,2,2>,
<126,950,127>,
<129,951,130>,
<2,2,2>,
<2,2,2>,
<952,952,952>,
<953,953,953>,
<954,954,954>,
<955,955,955>,
<956,956,956>,
<957,957,957>,
<958,958,958>,
<959,959,959>,
<140,960,141>,
<961,961,961>,
<144,144,144>,
<962,962,962>,
<963,963,963>,
<964,964,964>,
<965,965,965>,
<148,148,149>,
<2,2,2>,
<2,2,2>,
<966,966,966>,
<967,967,967>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<968,968,968>,
<969,969,969>,
<970,970,970>,
<971,971,971>,
<972,972,972>,
<973,973,973>,
<974,974,974>,
<975,975,975>,
<976,976,976>,
<977,977,977>,
<162,162,162>,
<978,978,978>,
<979,979,979>,
<980,980,980>,
<2,2,2>,
<2,2,2>,
<166,981,167>,
<169,982,170>,
<2,2,2>,
<2,2,2>,
<983,983,983>,
<984,984,984>,
<985,985,985>,
<986,986,986>,
<987,987,987>,
<988,988,988>,
<989,989,989>,
<990,990,990>,
<180,991,181>,
<992,992,992>,
<184,184,184>,
<993,993,993>,
<994,995,996>,
<996,997,994>,
<998,998,998>,
<999,999,999>,
<188,188,189>,
<2,2,2>,
<2,2,2>,
<1000,1000,1000>,
<1001,1001,1001>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<2,2,2>,
<1002,1002,1002>,
<1003,1003,1003>,
<1004,1004,1004>,
<1005,1005,1005>,
<1006,1006,1006>,
<1007,1007,1007>,
<1008,1008,1008>,
<1009,1009,1009>,
<1010,1010,1010>,
<1011,1011,1011>,
<203,1012,204>,
<1013,1013,1013>,
<1014,1014,1014>,
<1015,1015,1015>,
<2,2,2>,
<2,2,2>,
<209,1016,210>,
<212,1017,213>,
<2,2,2>,
<2,2,2>,
<1018,1018,1018>,
<1019,1019,1019>,
<1020,1020,1020>,
<1021,1021,1021>,
<1022,1022,1022>,
<1023,1023,1023>,
<1024,1024,1024>,
<1025,1025,1025>,
<223,1026,224>,
<1027,1027,1027>,
<227,227,227>,
<1028,1028,1028>,
<1029,1029,1029>,
<1030,1030,1030>,
<1031,1031,1031>,
<231,231,232>,
<2,2,2>,
<2,2,2>,
<1032,1032,1032>,
<1033,1033,1033>,
<2,2,2>,
<2,2,2>,
<1034,1034,1034>,
<1035,1035,1035>,
<1036,1036,1036>,
<1037,1037,1037>,
<1038,1038,1038>,
<1039,1039,1039>,
<1040,1040,1040>,
<1041,1041,1041>,
<1042,1042,1042>,
<1043,1043,1043>,
<245,245,245>,
<1044,1044,1044>,
<1045,1045,1045>,
<1046,1046,1046>,
<2,2,2>,
<2,2,2>,
<249,1047,250>,
<252,1048,253>,
<2,2,2>,
<2,2,2>,
<1049,1049,1049>,
<1050,1050,1050>,
<1051,1051,1051>,
<1052,1052,1052>,
<1053,1053,1053>,
<1054,1054,1054>,
<1055,1055,1055>,
<1056,1056,1056>,
<263,1057,264>,
<1058,1058,1058>,
<1059,1059,1059>,
<1060,1060,1060>,
<1061,1061,1061>,
<270,1062,271>,
<2,2,2>,
<2,2,2>,
<1063,1063,1063>,
<1064,1064,1064>,
<2,2,2>,
<2,2,2>,
<1065,1065,1065>,
<1066,1066,1066>,
<1067,1067,1067>,
<1068,1068,1068>,
<1069,1069,1069>,
<1070,1070,1070>,
<1071,1071,1071>,
<1072,1072,1072>,
<1073,1073,1073>,
<1074,1074,1074>,
<284,284,284>,
<1075,1075,1075>,
<286,286,286>,
<1076,1077,1076>,
<288,288,288>,
<1076,1077,1077>,
<289,1078,290>,
<292,292,292>,
<293,293,293>,
<1079,1080,1079>,
<294,294,294>,
<1079,1079,1079>,
<295,295,295>,
<1081,1081,1081>,
<1082,297,298>,
<1081,1081,1081>,
<1083,1083,1083>,
<300,300,300>,
<301,302,302>,
<303,303,303>,
<304,304,304>,
<305,305,306>,
<1084,1084,1084>,
<1085,1085,1085>,
<309,309,309>,
<310,310,310>,
<311,311,311>,
<312,312,312>,
<313,313,313>,
<314,314,314>,
<315,1086,316>,
<318,1087,319>,
<321,321,321>,
<322,322,323>,
<324,324,324>,
<325,325,326>,
<327,1088,327>,
<329,329,329>,
<331,331,331>,
<332,333,333>,
<334,334,334>,
<335,335,335>,
<337,1089,337>,
<338,338,338>,
<339,340,340>,
<341,341,341>,
<342,342,342>,
<343,343,343>,
<344,344,344>,
<345,345,345>,
<346,346,347>,
<348,348,348>,
<349,349,349>,
<351,351,351>,
<352,352,352>,
<353,1090,354>,
<355,355,355>,
<356,356,356>,
<357,357,357>,
<358,358,358>,
<359,359,359>,
<360,360,361>,
<362,1091,362>,
<363,363,364>,
<365,365,365>,
<366,366,366>,
<367,368,368>,
<369,369,369>,
<370,370,370>,
<371,371,371>,
<372,372,372>,
<373,373,373>,
<374,375,375>,
<377,377,377>,
<378,378,378>,
<379,379,379>,
<380,380,380>,
<381,382,382>,
<383,383,383>,
<384,1092,384>,
<385,385,385>,
<386,386,386>,
<1093,1093,1093>,
<389,389,389>,
<390,390,390>,
<391,391,391>,
<1094,1094,1094>,
<393,394,394>,
<395,395,396>,
<397,397,397>,
<398,398,398>,
<399,399,399>,
<400,400,400>,
<401,401,401>,
<402,402,402>,
<403,403,403>,
<404,404,404>,
<405,405,405>,
<406,406,406>,
<407,407,407>,
<408,408,408>,
<409,409,409>,
<410,410,410>,
<411,411,411>,
<1095,1095,1095>,
<413,413,413>,
<414,414,414>,
<415,415,415>,
<416,416,416>,
<417,417,417>,
<1096,1096,1096>,
<419,419,419>,
<420,420,420>,
<421,421,421>,
<422,422,422>,
<423,423,423>,
<424,424,424>,
<425,425,425>,
<426,426,426>,
<427,427,427>,
<428,428,428>,
<429,429,429>,
<430,430,430>,
<431,431,431>,
<432,432,432>,
<433,433,433>,
<434,434,434>,
<435,435,435>,
<436,436,436>,
<437,437,437>,
<438,438,438>,
<439,439,439>,
<440,440,440>,
<441,441,441>,
<442,442,442>,
<443,443,443>,
<444,444,444>,
<445,446,446>,
<447,447,447>,
<1097,1097,1097>,
<449,450,450>,
<451,452,452>,
<453,453,453>,
<454,454,454>,
<455,455,455>,
<456,456,456>,
<457,457,458>,
<459,459,459>,
<460,460,460>,
<1098,1098,1098>,
<462,1099,462>,
<463,463,463>,
<464,464,464>,
<465,465,465>,
<466,467,467>,
<1100,1100,1100>,
<469,1101,470>,
<472,472,472>,
<1102,1102,1102>,
<474,475,475>,
<476,476,476>,
<477,477,478>,
<479,479,479>,
<480,480,481>,
<482,482,482>,
<483,483,483>,
<484,484,484>,
<485,485,485>,
<486,486,486>,
<487,487,487>,
<488,489,489>,
<490,1103,491>,
<1104,1104,1104>,
<494,494,494>,
<495,1105,496>,
<498,1106,499>,
<1107,1107,1107>,
<502,1108,503>,
<1109,1109,1109>,
<505,1110,506>,
<508,508,509>,
<510,511,511>,
<512,512,512>,
<513,1111,514>,
<516,1112,517>,
<518,519,519>,
<520,520,521>,
<1113,1113,1113>,
<523,524,524>,
<525,525,525>,
<526,526,527>,
<1114,1114,1114>,
<529,1115,530>,
<532,1116,533>,
<535,535,535>,
<536,1117,536>,
<538,538,538>,
<1118,1118,1118>,
<540,541,541>,
<542,542,542>,
<543,543,544>,
<545,547,546>,
<548,548,549>,
<1119,1119,1119>,
<551,1120,552>,
<553,553,554>,
<555,1121,556>,
<557,1122,558>,
<560,1123,561>,
<1124,1124,1124>,
<563,1125,564>,
<566,566,567>,
<568,568,568>,
<569,570,570>,
<1126,1126,1126>,
<1127,1127,1127>,
<572,572,572>,
<1127,1127,1127>,
<1128,1128,1128>,
<1129,1129,1129>,
<1130,1130,1130>,
<1131,1131,1131>,
<575,575,576>,
<1132,1132,1132>,
<1133,1133,1133>,
<579,579,580>,
<1134,1134,1134>,
<1135,1135,1135>,
<1136,1136,1136>,
<1137,1137,1137>,
<1138,1138,1138>,
<585,585,586>,
<1139,1139,1139>,
<1140,1140,1140>,
<1141,1141,1141>,
<1142,1142,1142>,
<1143,1143,1143>,
<1144,1144,1144>,
<1145,1145,1145>,
<592,1146,592>,
<1147,1147,1147>,
<1148,1148,1148>,
<1149,1149,1149>,
<1150,1150,1150>,
<1151,1151,1151>,
<1152,1152,1152>,
<1153,1153,1153>,
<1154,1154,1154>,
<598,598,599>,
<1155,1155,1155>,
<1156,1156,1156>,
<1157,1157,1157>,
<1158,1158,1158>,
<1159,1159,1159>,
<1160,1160,1160>,
<604,604,605>,
<606,606,607>,
<1161,1161,1161>,
<609,1162,1163>,
<1163,610,609>,
<1162,1162,1163>,
<1164,614,1165>,
<612,1166,613>,
<613,1165,614>,
<1167,1168,1169>,
<1170,1171,1170>,
<1172,1172,1173>,
<1171,1172,1173>,
<616,1169,1168>,
<1171,1173,1170>,
<616,1168,615>,
<1174,618,1175>,
<1176,1177,1178>,
<1179,1179,1179>,
<1177,1179,1178>,
<1174,1174,1174>,
<1176,1178,1176>,
<1175,618,617>,
<1180,1180,1180>,
<1181,1181,1181>,
<619,1180,620>,
<1182,1182,1182>,
<623,623,623>,
<624,1182,622>,
<1183,1183,1183>,
<1184,626,625>,
<625,1183,1184>,
<1185,1186,1185>,
<1167,1169,1167>,
<1186,1167,1167>,
<1187,628,1188>,
<1187,1187,1187>,
<1186,1167,1185>,
<1188,628,627>,
<1189,1190,1191>,
<1191,629,630>,
<630,1189,1191>,
<1192,1192,1192>,
<632,632,632>,
<633,1193,631>,
<634,634,634>,
<1194,1194,635>,
<636,636,636>,
<637,1194,635>,
<1195,1195,1195>,
<1196,1196,1196>,
<1197,1197,1197>,
<1198,1198,1198>,
<1199,1199,1199>,
<1200,1200,1200>,
<1201,1201,1201>,
<642,642,643>,
<1202,1202,1202>,
<1203,1203,1203>,
<1204,1204,1204>,
<1205,1205,1205>,
<1206,1206,1206>,
<1207,1207,1207>,
<648,648,649>,
<1208,1208,1208>,
<1209,1209,1209>,
<1210,1210,1210>,
<652,652,653>,
<1211,1211,1211>,
<1212,1212,1212>,
<1213,1213,1213>,
<1214,1214,1214>,
<1215,1215,1215>,
<658,658,658>,
<1216,1216,1216>,
<1217,1217,1217>,
<1218,1218,1218>,
<1219,1219,1219>,
<662,662,662>,
<1220,1220,1220>,
<1221,1221,1221>,
<1222,1222,1222>,
<665,665,665>,
<1223,1223,1223>,
<1224,1224,1224>,
<1225,1225,1225>,
<668,668,669>,
<1226,1226,1226>,
<1227,1227,1227>,
<672,1228,672>,
<673,673,674>,
<675,675,676>,
<1229,1229,1229>,
<678,1230,678>,
<679,679,679>,
<680,1231,680>,
<682,682,682>,
<683,683,684>,
<1232,1232,1232>,
<1233,1233,1233>,
<688,1234,688>,
<690,690,691>,
<692,692,692>,
<694,694,695>,
<697,697,697>,
<698,698,699>,
<700,700,700>,
<701,701,701>,
<703,703,704>,
<705,705,705>,
<1235,1235,1235>,
<707,1236,708>,
<709,709,710>,
<711,711,712>,
<714,715,715>,
<1237,1237,1237>,
<717,717,717>,
<718,719,719>,
<720,720,720>,
<1238,1238,1238>,
<722,722,723>,
<724,724,724>,
<725,725,725>,
<726,726,727>,
<728,729,729>,
<1239,1239,1239>,
<732,732,732>,
<734,1240,734>,
<736,736,736>,
<737,737,737>,
<738,738,739>,
<1241,1242,1242>,
<1243,740,740>,
<1242,1244,1241>,
<741,741,741>,
<742,742,742>,
<743,743,743>,
<744,744,744>,
<1245,1245,1245>,
<746,746,746>,
<747,747,748>,
<1246,1246,1246>,
<749,749,749>,
<1246,1246,1246>,
<750,750,750>,
<751,751,751>,
<1247,1247,1247>,
<754,754,755>,
<1248,1248,1248>,
<757,757,757>,
<759,759,759>,
<760,760,760>,
<1249,1249,1249>,
<762,762,762>,
<763,1250,763>,
<765,765,765>,
<767,767,767>,
<768,768,769>,
<1251,1251,1251>,
<771,771,771>,
<772,772,772>,
<774,1252,774>,
<776,776,776>,
<1253,1253,1253>,
<778,778,778>,
<779,779,779>,
<780,780,781>,
<783,783,783>,
<784,1254,784>,
<1255,1255,1255>,
<786,1256,786>,
<788,788,788>,
<1257,1257,1257>,
<790,790,790>,
<791,791,791>,
<792,792,792>,
<793,793,793>,
<1258,1258,1258>,
<1259,1259,1259>,
<795,795,795>,
<796,796,796>,
<797,797,797>,
<798,799,799>,
<800,800,800>,
<801,801,801>,
<802,802,802>,
<804,805,805>,
<806,806,806>,
<1260,1260,1260>,
<1261,1261,1261>,
<808,808,808>,
<809,809,809>,
<810,811,811>,
<812,812,812>,
<813,813,813>,
<815,815,816>,
<817,1262,818>,
<820,820,820>,
<821,821,821>,
<822,822,822>,
<823,823,823>,
<1263,1263,1263>,
<825,825,825>,
<826,826,826>,
<827,827,827>,
<828,828,828>,
<829,829,829>,
<1264,1264,1264>,
<1265,1265,1265>,
<831,831,832>,
<833,834,834>,
<1266,1266,1266>,
<1267,1267,1267>,
<836,836,836>,
<837,837,837>,
<838,1268,839>,
<840,841,841>,
<842,842,843>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>,
<844,844,844>
}
inside_vector <0,0,1> }
 
 
//Model assembly from the meshes
#declare sedan_rim=
object {
object{sedan_rim_Material_001_  material{Material_001_} hollow }
rotate <-90, 0, 0>
scale <1,1,-1>
}
 
//restore the version used outside this file
#version Temp_version;
