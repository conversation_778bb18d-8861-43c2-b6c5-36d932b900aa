// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file RegionOfInterest.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "RegionOfInterest.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define sensor_msgs_msg_RegionOfInterest_max_cdr_typesize 17ULL;
#define sensor_msgs_msg_RegionOfInterest_max_key_cdr_typesize 0ULL;

sensor_msgs::msg::RegionOfInterest::RegionOfInterest()
{
    // unsigned long m_x_offset
    m_x_offset = 0;
    // unsigned long m_y_offset
    m_y_offset = 0;
    // unsigned long m_height
    m_height = 0;
    // unsigned long m_width
    m_width = 0;
    // boolean m_do_rectify
    m_do_rectify = false;
}

sensor_msgs::msg::RegionOfInterest::~RegionOfInterest()
{
}

sensor_msgs::msg::RegionOfInterest::RegionOfInterest(
        const RegionOfInterest& x)
{
    m_x_offset = x.m_x_offset;
    m_y_offset = x.m_y_offset;
    m_height = x.m_height;
    m_width = x.m_width;
    m_do_rectify = x.m_do_rectify;
}

sensor_msgs::msg::RegionOfInterest::RegionOfInterest(
        RegionOfInterest&& x) noexcept
{
    m_x_offset = x.m_x_offset;
    m_y_offset = x.m_y_offset;
    m_height = x.m_height;
    m_width = x.m_width;
    m_do_rectify = x.m_do_rectify;
}

sensor_msgs::msg::RegionOfInterest& sensor_msgs::msg::RegionOfInterest::operator =(
        const RegionOfInterest& x)
{
    m_x_offset = x.m_x_offset;
    m_y_offset = x.m_y_offset;
    m_height = x.m_height;
    m_width = x.m_width;
    m_do_rectify = x.m_do_rectify;

    return *this;
}

sensor_msgs::msg::RegionOfInterest& sensor_msgs::msg::RegionOfInterest::operator =(
        RegionOfInterest&& x) noexcept
{
    m_x_offset = x.m_x_offset;
    m_y_offset = x.m_y_offset;
    m_height = x.m_height;
    m_width = x.m_width;
    m_do_rectify = x.m_do_rectify;

    return *this;
}

bool sensor_msgs::msg::RegionOfInterest::operator ==(
        const RegionOfInterest& x) const
{
    return (m_x_offset == x.m_x_offset && m_y_offset == x.m_y_offset && m_height == x.m_height && m_width == x.m_width && m_do_rectify == x.m_do_rectify);
}

bool sensor_msgs::msg::RegionOfInterest::operator !=(
        const RegionOfInterest& x) const
{
    return !(*this == x);
}

size_t sensor_msgs::msg::RegionOfInterest::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_RegionOfInterest_max_cdr_typesize;
}

size_t sensor_msgs::msg::RegionOfInterest::getCdrSerializedSize(
        const sensor_msgs::msg::RegionOfInterest& data,
        size_t current_alignment)
{
    (void)data;
    size_t initial_alignment = current_alignment;
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 1 + eprosima::fastcdr::Cdr::alignment(current_alignment, 1);

    return current_alignment - initial_alignment;
}

void sensor_msgs::msg::RegionOfInterest::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_x_offset;
    scdr << m_y_offset;
    scdr << m_height;
    scdr << m_width;
    scdr << m_do_rectify;
}

void sensor_msgs::msg::RegionOfInterest::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_x_offset;
    dcdr >> m_y_offset;
    dcdr >> m_height;
    dcdr >> m_width;
    dcdr >> m_do_rectify;
}

/*!
 * @brief This function sets a value in member x_offset
 * @param _x_offset New value for member x_offset
 */
void sensor_msgs::msg::RegionOfInterest::x_offset(
        uint32_t _x_offset)
{
    m_x_offset = _x_offset;
}

/*!
 * @brief This function returns the value of member x_offset
 * @return Value of member x_offset
 */
uint32_t sensor_msgs::msg::RegionOfInterest::x_offset() const
{
    return m_x_offset;
}

/*!
 * @brief This function returns a reference to member x_offset
 * @return Reference to member x_offset
 */
uint32_t& sensor_msgs::msg::RegionOfInterest::x_offset()
{
    return m_x_offset;
}

/*!
 * @brief This function sets a value in member y_offset
 * @param _y_offset New value for member y_offset
 */
void sensor_msgs::msg::RegionOfInterest::y_offset(
        uint32_t _y_offset)
{
    m_y_offset = _y_offset;
}

/*!
 * @brief This function returns the value of member y_offset
 * @return Value of member y_offset
 */
uint32_t sensor_msgs::msg::RegionOfInterest::y_offset() const
{
    return m_y_offset;
}

/*!
 * @brief This function returns a reference to member y_offset
 * @return Reference to member y_offset
 */
uint32_t& sensor_msgs::msg::RegionOfInterest::y_offset()
{
    return m_y_offset;
}

/*!
 * @brief This function sets a value in member height
 * @param _height New value for member height
 */
void sensor_msgs::msg::RegionOfInterest::height(
        uint32_t _height)
{
    m_height = _height;
}

/*!
 * @brief This function returns the value of member height
 * @return Value of member height
 */
uint32_t sensor_msgs::msg::RegionOfInterest::height() const
{
    return m_height;
}

/*!
 * @brief This function returns a reference to member height
 * @return Reference to member height
 */
uint32_t& sensor_msgs::msg::RegionOfInterest::height()
{
    return m_height;
}

/*!
 * @brief This function sets a value in member width
 * @param _width New value for member width
 */
void sensor_msgs::msg::RegionOfInterest::width(
        uint32_t _width)
{
    m_width = _width;
}

/*!
 * @brief This function returns the value of member width
 * @return Value of member width
 */
uint32_t sensor_msgs::msg::RegionOfInterest::width() const
{
    return m_width;
}

/*!
 * @brief This function returns a reference to member width
 * @return Reference to member width
 */
uint32_t& sensor_msgs::msg::RegionOfInterest::width()
{
    return m_width;
}

/*!
 * @brief This function sets a value in member do_rectify
 * @param _do_rectify New value for member do_rectify
 */
void sensor_msgs::msg::RegionOfInterest::do_rectify(
        bool _do_rectify)
{
    m_do_rectify = _do_rectify;
}

/*!
 * @brief This function returns the value of member do_rectify
 * @return Value of member do_rectify
 */
bool sensor_msgs::msg::RegionOfInterest::do_rectify() const
{
    return m_do_rectify;
}

/*!
 * @brief This function returns a reference to member do_rectify
 * @return Reference to member do_rectify
 */
bool& sensor_msgs::msg::RegionOfInterest::do_rectify()
{
    return m_do_rectify;
}

size_t sensor_msgs::msg::RegionOfInterest::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_RegionOfInterest_max_key_cdr_typesize;
}

bool sensor_msgs::msg::RegionOfInterest::isKeyDefined()
{
    return false;
}

void sensor_msgs::msg::RegionOfInterest::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
