// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file NavSatFix.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "NavSatFix.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define std_msgs_msg_Time_max_cdr_typesize 8ULL;
#define sensor_msgs_msg_NavSatStatus_max_cdr_typesize 4ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define sensor_msgs_msg_NavSatFix_max_cdr_typesize 369ULL;

#define std_msgs_msg_Time_max_key_cdr_typesize 0ULL;
#define sensor_msgs_msg_NavSatStatus_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;
#define sensor_msgs_msg_NavSatFix_max_key_cdr_typesize 0ULL;

sensor_msgs::msg::NavSatFix::NavSatFix()
{
    // std_msgs::msg::Header m_header

    // sensor_msgs::msg::NavSatStatus m_status

    // double m_latitude
    m_latitude = 0.0;
    // double m_longitude
    m_longitude = 0.0;
    // double m_altitude
    m_altitude = 0.0;
    // sensor_msgs::msg::sensor_msgs__NavSatFix__double_array_9 m_position_covariance
    memset(&m_position_covariance, 0, (9) * 8);
    // octet m_position_covariance_type
    m_position_covariance_type = 0;
}

sensor_msgs::msg::NavSatFix::~NavSatFix()
{
}

sensor_msgs::msg::NavSatFix::NavSatFix(
        const NavSatFix& x)
{
    m_header = x.m_header;
    m_status = x.m_status;
    m_latitude = x.m_latitude;
    m_longitude = x.m_longitude;
    m_altitude = x.m_altitude;
    m_position_covariance = x.m_position_covariance;
    m_position_covariance_type = x.m_position_covariance_type;
}

sensor_msgs::msg::NavSatFix::NavSatFix(
        NavSatFix&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_status = std::move(x.m_status);
    m_latitude = x.m_latitude;
    m_longitude = x.m_longitude;
    m_altitude = x.m_altitude;
    m_position_covariance = std::move(x.m_position_covariance);
    m_position_covariance_type = x.m_position_covariance_type;
}

sensor_msgs::msg::NavSatFix& sensor_msgs::msg::NavSatFix::operator =(
        const NavSatFix& x)
{
    m_header = x.m_header;
    m_status = x.m_status;
    m_latitude = x.m_latitude;
    m_longitude = x.m_longitude;
    m_altitude = x.m_altitude;
    m_position_covariance = x.m_position_covariance;
    m_position_covariance_type = x.m_position_covariance_type;

    return *this;
}

sensor_msgs::msg::NavSatFix& sensor_msgs::msg::NavSatFix::operator =(
        NavSatFix&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_status = std::move(x.m_status);
    m_latitude = x.m_latitude;
    m_longitude = x.m_longitude;
    m_altitude = x.m_altitude;
    m_position_covariance = std::move(x.m_position_covariance);
    m_position_covariance_type = x.m_position_covariance_type;

    return *this;
}

bool sensor_msgs::msg::NavSatFix::operator ==(
        const NavSatFix& x) const
{
    return (m_header == x.m_header && m_status == x.m_status && m_latitude == x.m_latitude && m_longitude == x.m_longitude && m_altitude == x.m_altitude && m_position_covariance == x.m_position_covariance && m_position_covariance_type == x.m_position_covariance_type);
}

bool sensor_msgs::msg::NavSatFix::operator !=(
        const NavSatFix& x) const
{
    return !(*this == x);
}

size_t sensor_msgs::msg::NavSatFix::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_NavSatFix_max_cdr_typesize;
}

size_t sensor_msgs::msg::NavSatFix::getCdrSerializedSize(
        const sensor_msgs::msg::NavSatFix& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += std_msgs::msg::Header::getCdrSerializedSize(data.header(), current_alignment);
    current_alignment += sensor_msgs::msg::NavSatStatus::getCdrSerializedSize(data.status(), current_alignment);
    current_alignment += 8 + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += 8 + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += 8 + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += ((9) * 8) + eprosima::fastcdr::Cdr::alignment(current_alignment, 8);
    current_alignment += 1 + eprosima::fastcdr::Cdr::alignment(current_alignment, 1);

    return current_alignment - initial_alignment;
}

void sensor_msgs::msg::NavSatFix::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_header;
    scdr << m_status;
    scdr << m_latitude;
    scdr << m_longitude;
    scdr << m_altitude;
    scdr << m_position_covariance;
    scdr << m_position_covariance_type;
}

void sensor_msgs::msg::NavSatFix::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_header;
    dcdr >> m_status;
    dcdr >> m_latitude;
    dcdr >> m_longitude;
    dcdr >> m_altitude;
    dcdr >> m_position_covariance;
    dcdr >> m_position_covariance_type;
}

/*!
 * @brief This function copies the value in member header
 * @param _header New value to be copied in member header
 */
void sensor_msgs::msg::NavSatFix::header(
        const std_msgs::msg::Header& _header)
{
    m_header = _header;
}

/*!
 * @brief This function moves the value in member header
 * @param _header New value to be moved in member header
 */
void sensor_msgs::msg::NavSatFix::header(
        std_msgs::msg::Header&& _header)
{
    m_header = std::move(_header);
}

/*!
 * @brief This function returns a constant reference to member header
 * @return Constant reference to member header
 */
const std_msgs::msg::Header& sensor_msgs::msg::NavSatFix::header() const
{
    return m_header;
}

/*!
 * @brief This function returns a reference to member header
 * @return Reference to member header
 */
std_msgs::msg::Header& sensor_msgs::msg::NavSatFix::header()
{
    return m_header;
}

/*!
 * @brief This function copies the value in member status
 * @param _status New value to be copied in member status
 */
void sensor_msgs::msg::NavSatFix::status(
        const sensor_msgs::msg::NavSatStatus& _status)
{
    m_status = _status;
}

/*!
 * @brief This function moves the value in member status
 * @param _status New value to be moved in member status
 */
void sensor_msgs::msg::NavSatFix::status(
        sensor_msgs::msg::NavSatStatus&& _status)
{
    m_status = std::move(_status);
}

/*!
 * @brief This function returns a constant reference to member status
 * @return Constant reference to member status
 */
const sensor_msgs::msg::NavSatStatus& sensor_msgs::msg::NavSatFix::status() const
{
    return m_status;
}

/*!
 * @brief This function returns a reference to member status
 * @return Reference to member status
 */
sensor_msgs::msg::NavSatStatus& sensor_msgs::msg::NavSatFix::status()
{
    return m_status;
}

/*!
 * @brief This function sets a value in member latitude
 * @param _latitude New value for member latitude
 */
void sensor_msgs::msg::NavSatFix::latitude(
        double _latitude)
{
    m_latitude = _latitude;
}

/*!
 * @brief This function returns the value of member latitude
 * @return Value of member latitude
 */
double sensor_msgs::msg::NavSatFix::latitude() const
{
    return m_latitude;
}

/*!
 * @brief This function returns a reference to member latitude
 * @return Reference to member latitude
 */
double& sensor_msgs::msg::NavSatFix::latitude()
{
    return m_latitude;
}

/*!
 * @brief This function sets a value in member longitude
 * @param _longitude New value for member longitude
 */
void sensor_msgs::msg::NavSatFix::longitude(
        double _longitude)
{
    m_longitude = _longitude;
}

/*!
 * @brief This function returns the value of member longitude
 * @return Value of member longitude
 */
double sensor_msgs::msg::NavSatFix::longitude() const
{
    return m_longitude;
}

/*!
 * @brief This function returns a reference to member longitude
 * @return Reference to member longitude
 */
double& sensor_msgs::msg::NavSatFix::longitude()
{
    return m_longitude;
}

/*!
 * @brief This function sets a value in member altitude
 * @param _altitude New value for member altitude
 */
void sensor_msgs::msg::NavSatFix::altitude(
        double _altitude)
{
    m_altitude = _altitude;
}

/*!
 * @brief This function returns the value of member altitude
 * @return Value of member altitude
 */
double sensor_msgs::msg::NavSatFix::altitude() const
{
    return m_altitude;
}

/*!
 * @brief This function returns a reference to member altitude
 * @return Reference to member altitude
 */
double& sensor_msgs::msg::NavSatFix::altitude()
{
    return m_altitude;
}

/*!
 * @brief This function copies the value in member position_covariance
 * @param _position_covariance New value to be copied in member position_covariance
 */
void sensor_msgs::msg::NavSatFix::position_covariance(
        const sensor_msgs::msg::sensor_msgs__NavSatFix__double_array_9& _position_covariance)
{
    m_position_covariance = _position_covariance;
}

/*!
 * @brief This function moves the value in member position_covariance
 * @param _position_covariance New value to be moved in member position_covariance
 */
void sensor_msgs::msg::NavSatFix::position_covariance(
        sensor_msgs::msg::sensor_msgs__NavSatFix__double_array_9&& _position_covariance)
{
    m_position_covariance = std::move(_position_covariance);
}

/*!
 * @brief This function returns a constant reference to member position_covariance
 * @return Constant reference to member position_covariance
 */
const sensor_msgs::msg::sensor_msgs__NavSatFix__double_array_9& sensor_msgs::msg::NavSatFix::position_covariance() const
{
    return m_position_covariance;
}

/*!
 * @brief This function returns a reference to member position_covariance
 * @return Reference to member position_covariance
 */
sensor_msgs::msg::sensor_msgs__NavSatFix__double_array_9& sensor_msgs::msg::NavSatFix::position_covariance()
{
    return m_position_covariance;
}
/*!
 * @brief This function sets a value in member position_covariance_type
 * @param _position_covariance_type New value for member position_covariance_type
 */
void sensor_msgs::msg::NavSatFix::position_covariance_type(
        uint8_t _position_covariance_type)
{
    m_position_covariance_type = _position_covariance_type;
}

/*!
 * @brief This function returns the value of member position_covariance_type
 * @return Value of member position_covariance_type
 */
uint8_t sensor_msgs::msg::NavSatFix::position_covariance_type() const
{
    return m_position_covariance_type;
}

/*!
 * @brief This function returns a reference to member position_covariance_type
 * @return Reference to member position_covariance_type
 */
uint8_t& sensor_msgs::msg::NavSatFix::position_covariance_type()
{
    return m_position_covariance_type;
}

size_t sensor_msgs::msg::NavSatFix::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return sensor_msgs_msg_NavSatFix_max_key_cdr_typesize;
}

bool sensor_msgs::msg::NavSatFix::isKeyDefined()
{
    return false;
}

void sensor_msgs::msg::NavSatFix::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
