// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file AckermannDriveStamped.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "AckermannDriveStamped.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define builtin_interfaces_msg_Time_max_cdr_typesize 8ULL;
#define ackermann_msgs_msg_AckermannDriveStamped_max_cdr_typesize 288ULL;
#define ackermann_msgs_msg_AckermannDrive_max_cdr_typesize 20ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define builtin_interfaces_msg_Time_max_key_cdr_typesize 0ULL;
#define ackermann_msgs_msg_AckermannDriveStamped_max_key_cdr_typesize 0ULL;
#define ackermann_msgs_msg_AckermannDrive_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;

ackermann_msgs::msg::AckermannDriveStamped::AckermannDriveStamped()
{
    // std_msgs::msg::Header m_header

    // ackermann_msgs::msg::AckermannDrive m_drive


}

ackermann_msgs::msg::AckermannDriveStamped::~AckermannDriveStamped()
{


}

ackermann_msgs::msg::AckermannDriveStamped::AckermannDriveStamped(
        const AckermannDriveStamped& x)
{
    m_header = x.m_header;
    m_drive = x.m_drive;
}

ackermann_msgs::msg::AckermannDriveStamped::AckermannDriveStamped(
        AckermannDriveStamped&& x) noexcept 
{
    m_header = std::move(x.m_header);
    m_drive = std::move(x.m_drive);
}

ackermann_msgs::msg::AckermannDriveStamped& ackermann_msgs::msg::AckermannDriveStamped::operator =(
        const AckermannDriveStamped& x)
{

    m_header = x.m_header;
    m_drive = x.m_drive;

    return *this;
}

ackermann_msgs::msg::AckermannDriveStamped& ackermann_msgs::msg::AckermannDriveStamped::operator =(
        AckermannDriveStamped&& x) noexcept
{

    m_header = std::move(x.m_header);
    m_drive = std::move(x.m_drive);

    return *this;
}

bool ackermann_msgs::msg::AckermannDriveStamped::operator ==(
        const AckermannDriveStamped& x) const
{

    return (m_header == x.m_header && m_drive == x.m_drive);
}

bool ackermann_msgs::msg::AckermannDriveStamped::operator !=(
        const AckermannDriveStamped& x) const
{
    return !(*this == x);
}

size_t ackermann_msgs::msg::AckermannDriveStamped::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return ackermann_msgs_msg_AckermannDriveStamped_max_cdr_typesize;
}

size_t ackermann_msgs::msg::AckermannDriveStamped::getCdrSerializedSize(
        const ackermann_msgs::msg::AckermannDriveStamped& data,
        size_t current_alignment)
{
    (void)data;
    size_t initial_alignment = current_alignment;


    current_alignment += std_msgs::msg::Header::getCdrSerializedSize(data.header(), current_alignment);
    current_alignment += ackermann_msgs::msg::AckermannDrive::getCdrSerializedSize(data.drive(), current_alignment);

    return current_alignment - initial_alignment;
}

void ackermann_msgs::msg::AckermannDriveStamped::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{

    scdr << m_header;
    scdr << m_drive;

}

void ackermann_msgs::msg::AckermannDriveStamped::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{

    dcdr >> m_header;
    dcdr >> m_drive;
}

/*!
 * @brief This function copies the value in member header
 * @param _header New value to be copied in member header
 */
void ackermann_msgs::msg::AckermannDriveStamped::header(
        const std_msgs::msg::Header& _header)
{
    m_header = _header;
}

/*!
 * @brief This function moves the value in member header
 * @param _header New value to be moved in member header
 */
void ackermann_msgs::msg::AckermannDriveStamped::header(
        std_msgs::msg::Header&& _header)
{
    m_header = std::move(_header);
}

/*!
 * @brief This function returns a constant reference to member header
 * @return Constant reference to member header
 */
const std_msgs::msg::Header& ackermann_msgs::msg::AckermannDriveStamped::header() const
{
    return m_header;
}

/*!
 * @brief This function returns a reference to member header
 * @return Reference to member header
 */
std_msgs::msg::Header& ackermann_msgs::msg::AckermannDriveStamped::header()
{
    return m_header;
}
/*!
 * @brief This function copies the value in member drive
 * @param _drive New value to be copied in member drive
 */
void ackermann_msgs::msg::AckermannDriveStamped::drive(
        const ackermann_msgs::msg::AckermannDrive& _drive)
{
    m_drive = _drive;
}

/*!
 * @brief This function moves the value in member drive
 * @param _drive New value to be moved in member drive
 */
void ackermann_msgs::msg::AckermannDriveStamped::drive(
        ackermann_msgs::msg::AckermannDrive&& _drive)
{
    m_drive = std::move(_drive);
}

/*!
 * @brief This function returns a constant reference to member drive
 * @return Constant reference to member drive
 */
const ackermann_msgs::msg::AckermannDrive& ackermann_msgs::msg::AckermannDriveStamped::drive() const
{
    return m_drive;
}

/*!
 * @brief This function returns a reference to member drive
 * @return Reference to member drive
 */
ackermann_msgs::msg::AckermannDrive& ackermann_msgs::msg::AckermannDriveStamped::drive()
{
    return m_drive;
}


size_t ackermann_msgs::msg::AckermannDriveStamped::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return ackermann_msgs_msg_AckermannDriveStamped_max_key_cdr_typesize;
}

bool ackermann_msgs::msg::AckermannDriveStamped::isKeyDefined()
{
    return false;
}

void ackermann_msgs::msg::AckermannDriveStamped::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}



