// Copyright (c) 2020 Computer Vision Center (CVC) at the Universitat Autonoma
// de Barcelona (UAB).
//
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#pragma once

#include "carla/Debug.h"
#include "carla/Memory.h"
#include "carla/sensor/RawData.h"
#include "carla/sensor/data/SemanticLidarData.h"

namespace carla {
namespace sensor {

class SensorData;

namespace s11n {

  // ===========================================================================
  // -- SemanticLidarHeaderView --------------------------------------------------------
  // ===========================================================================

  /// A view over the header of a Lidar measurement.
  class SemanticLidarHeaderView {
    using Index = data::SemanticLidarData::Index;

    public:

    float GetHorizontalAngle() const {
      return reinterpret_cast<const float &>(_begin[Index::HorizontalAngle]);
    }

    uint32_t GetChannelCount() const {
      return _begin[Index::ChannelCount];
    }

    uint32_t GetPointCount(size_t channel) const {
      DEBUG_ASSERT(channel < GetChannelCount());
      return _begin[Index::SIZE + channel];
    }

  protected:
    friend class SemanticLidarSerializer;

    explicit SemanticLidarHeaderView(const uint32_t *begin) : _begin(begin) {
      DEBUG_ASSERT(_begin != nullptr);
    }

    const uint32_t *_begin;
  };

  // ===========================================================================
  // -- LidarSerializer --------------------------------------------------------
  // ===========================================================================

  /// Serializes the data generated by Lidar sensors.
  class SemanticLidarSerializer {
  public:

    static SemanticLidarHeaderView DeserializeHeader(const RawData &data) {
      return SemanticLidarHeaderView{reinterpret_cast<const uint32_t *>(data.begin())};
    }

    static size_t GetHeaderOffset(const RawData &data) {
      auto View = DeserializeHeader(data);
      return sizeof(uint32_t) * (View.GetChannelCount() + data::SemanticLidarData::Index::SIZE);
    }

    template <typename Sensor>
    static Buffer Serialize(
        const Sensor &sensor,
        const data::SemanticLidarData &measurement,
        Buffer &&output);

    static SharedPtr<SensorData> Deserialize(RawData &&data);
  };

  // ===========================================================================
  // -- LidarRawSerializer implementation -----------------------------------------
  // ===========================================================================

  template <typename Sensor>
  inline Buffer SemanticLidarSerializer::Serialize(
      const Sensor &,
      const data::SemanticLidarData &measurement,
      Buffer &&output) {
    std::array<boost::asio::const_buffer, 2u> seq = {
        boost::asio::buffer(measurement._header),
        boost::asio::buffer(measurement._ser_points)};
    output.copy_from(seq);
    return std::move(output);
  }

} // namespace s11n
} // namespace sensor
} // namespace carla
