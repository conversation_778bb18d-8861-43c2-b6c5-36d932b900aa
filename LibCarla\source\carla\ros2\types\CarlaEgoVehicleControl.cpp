// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file CarlaEgoCarlaEgoVehicleControl.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "CarlaEgoVehicleControl.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define builtin_interfaces_msg_Time_max_cdr_typesize 8ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define carla_msgs_msg_CarlaEgoVehicleControl_max_cdr_typesize 289ULL;
#define builtin_interfaces_msg_Time_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;
#define carla_msgs_msg_CarlaEgoVehicleControl_max_key_cdr_typesize 0ULL;

carla_msgs::msg::CarlaEgoVehicleControl::CarlaEgoVehicleControl()
{
    // std_msgs::msg::Header m_header

    // float m_throttle
    m_throttle = 0.0;
    // float m_steer
    m_steer = 0.0;
    // float m_brake
    m_brake = 0.0;
    // boolean m_hand_brake
    m_hand_brake = false;
    // boolean m_reverse
    m_reverse = false;
    // long m_gear
    m_gear = 0;
    // boolean m_manual_gear_shift
    m_manual_gear_shift = false;

}

carla_msgs::msg::CarlaEgoVehicleControl::~CarlaEgoVehicleControl()
{
}

carla_msgs::msg::CarlaEgoVehicleControl::CarlaEgoVehicleControl(
        const CarlaEgoVehicleControl& x)
{
    m_header = x.m_header;
    m_throttle = x.m_throttle;
    m_steer = x.m_steer;
    m_brake = x.m_brake;
    m_hand_brake = x.m_hand_brake;
    m_reverse = x.m_reverse;
    m_gear = x.m_gear;
    m_manual_gear_shift = x.m_manual_gear_shift;
}

carla_msgs::msg::CarlaEgoVehicleControl::CarlaEgoVehicleControl(
        CarlaEgoVehicleControl&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_throttle = x.m_throttle;
    m_steer = x.m_steer;
    m_brake = x.m_brake;
    m_hand_brake = x.m_hand_brake;
    m_reverse = x.m_reverse;
    m_gear = x.m_gear;
    m_manual_gear_shift = x.m_manual_gear_shift;
}

carla_msgs::msg::CarlaEgoVehicleControl& carla_msgs::msg::CarlaEgoVehicleControl::operator =(
        const CarlaEgoVehicleControl& x)
{
    m_header = x.m_header;
    m_throttle = x.m_throttle;
    m_steer = x.m_steer;
    m_brake = x.m_brake;
    m_hand_brake = x.m_hand_brake;
    m_reverse = x.m_reverse;
    m_gear = x.m_gear;
    m_manual_gear_shift = x.m_manual_gear_shift;

    return *this;
}

carla_msgs::msg::CarlaEgoVehicleControl& carla_msgs::msg::CarlaEgoVehicleControl::operator =(
        CarlaEgoVehicleControl&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_throttle = x.m_throttle;
    m_steer = x.m_steer;
    m_brake = x.m_brake;
    m_hand_brake = x.m_hand_brake;
    m_reverse = x.m_reverse;
    m_gear = x.m_gear;
    m_manual_gear_shift = x.m_manual_gear_shift;

    return *this;
}

bool carla_msgs::msg::CarlaEgoVehicleControl::operator ==(
        const CarlaEgoVehicleControl& x) const
{
    return (m_header == x.m_header && m_throttle == x.m_throttle && m_steer == x.m_steer && m_brake == x.m_brake && m_hand_brake == x.m_hand_brake && m_reverse == x.m_reverse && m_gear == x.m_gear && m_manual_gear_shift == x.m_manual_gear_shift);
}

bool carla_msgs::msg::CarlaEgoVehicleControl::operator !=(
        const CarlaEgoVehicleControl& x) const
{
    return !(*this == x);
}

size_t carla_msgs::msg::CarlaEgoVehicleControl::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return carla_msgs_msg_CarlaEgoVehicleControl_max_cdr_typesize;
}

size_t carla_msgs::msg::CarlaEgoVehicleControl::getCdrSerializedSize(
        const carla_msgs::msg::CarlaEgoVehicleControl& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += std_msgs::msg::Header::getCdrSerializedSize(data.header(), current_alignment);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 1 + eprosima::fastcdr::Cdr::alignment(current_alignment, 1);
    current_alignment += 1 + eprosima::fastcdr::Cdr::alignment(current_alignment, 1);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    current_alignment += 1 + eprosima::fastcdr::Cdr::alignment(current_alignment, 1);
    return current_alignment - initial_alignment;
}

void carla_msgs::msg::CarlaEgoVehicleControl::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_header;
    scdr << m_throttle;
    scdr << m_steer;
    scdr << m_brake;
    scdr << m_hand_brake;
    scdr << m_reverse;
    scdr << m_gear;
    scdr << m_manual_gear_shift;
}

void carla_msgs::msg::CarlaEgoVehicleControl::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_header;
    dcdr >> m_throttle;
    dcdr >> m_steer;
    dcdr >> m_brake;
    dcdr >> m_hand_brake;
    dcdr >> m_reverse;
    dcdr >> m_gear;
    dcdr >> m_manual_gear_shift;
}

/*!
 * @brief This function copies the value in member header
 * @param _header New value to be copied in member header
 */
void carla_msgs::msg::CarlaEgoVehicleControl::header(
        const std_msgs::msg::Header& _header)
{
    m_header = _header;
}

/*!
 * @brief This function moves the value in member header
 * @param _header New value to be moved in member header
 */
void carla_msgs::msg::CarlaEgoVehicleControl::header(
        std_msgs::msg::Header&& _header)
{
    m_header = std::move(_header);
}

/*!
 * @brief This function returns a constant reference to member header
 * @return Constant reference to member header
 */
const std_msgs::msg::Header& carla_msgs::msg::CarlaEgoVehicleControl::header() const
{
    return m_header;
}

/*!
 * @brief This function returns a reference to member header
 * @return Reference to member header
 */
std_msgs::msg::Header& carla_msgs::msg::CarlaEgoVehicleControl::header()
{
    return m_header;
}
/*!
 * @brief This function sets a value in member throttle
 * @param _throttle New value for member throttle
 */
void carla_msgs::msg::CarlaEgoVehicleControl::throttle(
        float _throttle)
{
    m_throttle = _throttle;
}

/*!
 * @brief This function returns the value of member throttle
 * @return Value of member throttle
 */
float carla_msgs::msg::CarlaEgoVehicleControl::throttle() const
{
    return m_throttle;
}

/*!
 * @brief This function returns a reference to member throttle
 * @return Reference to member throttle
 */
float& carla_msgs::msg::CarlaEgoVehicleControl::throttle()
{
    return m_throttle;
}

/*!
 * @brief This function sets a value in member steer
 * @param _steer New value for member steer
 */
void carla_msgs::msg::CarlaEgoVehicleControl::steer(
        float _steer)
{
    m_steer = _steer;
}

/*!
 * @brief This function returns the value of member steer
 * @return Value of member steer
 */
float carla_msgs::msg::CarlaEgoVehicleControl::steer() const
{
    return m_steer;
}

/*!
 * @brief This function returns a reference to member steer
 * @return Reference to member steer
 */
float& carla_msgs::msg::CarlaEgoVehicleControl::steer()
{
    return m_steer;
}

/*!
 * @brief This function sets a value in member brake
 * @param _brake New value for member brake
 */
void carla_msgs::msg::CarlaEgoVehicleControl::brake(
        float _brake)
{
    m_brake = _brake;
}

/*!
 * @brief This function returns the value of member brake
 * @return Value of member brake
 */
float carla_msgs::msg::CarlaEgoVehicleControl::brake() const
{
    return m_brake;
}

/*!
 * @brief This function returns a reference to member brake
 * @return Reference to member brake
 */
float& carla_msgs::msg::CarlaEgoVehicleControl::brake()
{
    return m_brake;
}

/*!
 * @brief This function sets a value in member hand_brake
 * @param _hand_brake New value for member hand_brake
 */
void carla_msgs::msg::CarlaEgoVehicleControl::hand_brake(
        bool _hand_brake)
{
    m_hand_brake = _hand_brake;
}

/*!
 * @brief This function returns the value of member hand_brake
 * @return Value of member hand_brake
 */
bool carla_msgs::msg::CarlaEgoVehicleControl::hand_brake() const
{
    return m_hand_brake;
}

/*!
 * @brief This function returns a reference to member hand_brake
 * @return Reference to member hand_brake
 */
bool& carla_msgs::msg::CarlaEgoVehicleControl::hand_brake()
{
    return m_hand_brake;
}

/*!
 * @brief This function sets a value in member reverse
 * @param _reverse New value for member reverse
 */
void carla_msgs::msg::CarlaEgoVehicleControl::reverse(
        bool _reverse)
{
    m_reverse = _reverse;
}

/*!
 * @brief This function returns the value of member reverse
 * @return Value of member reverse
 */
bool carla_msgs::msg::CarlaEgoVehicleControl::reverse() const
{
    return m_reverse;
}

/*!
 * @brief This function returns a reference to member reverse
 * @return Reference to member reverse
 */
bool& carla_msgs::msg::CarlaEgoVehicleControl::reverse()
{
    return m_reverse;
}

/*!
 * @brief This function sets a value in member gear
 * @param _gear New value for member gear
 */
void carla_msgs::msg::CarlaEgoVehicleControl::gear(
        int32_t _gear)
{
    m_gear = _gear;
}

/*!
 * @brief This function returns the value of member gear
 * @return Value of member gear
 */
int32_t carla_msgs::msg::CarlaEgoVehicleControl::gear() const
{
    return m_gear;
}

/*!
 * @brief This function returns a reference to member gear
 * @return Reference to member gear
 */
int32_t& carla_msgs::msg::CarlaEgoVehicleControl::gear()
{
    return m_gear;
}

/*!
 * @brief This function sets a value in member manual_gear_shift
 * @param _manual_gear_shift New value for member manual_gear_shift
 */
void carla_msgs::msg::CarlaEgoVehicleControl::manual_gear_shift(
        bool _manual_gear_shift)
{
    m_manual_gear_shift = _manual_gear_shift;
}

/*!
 * @brief This function returns the value of member manual_gear_shift
 * @return Value of member manual_gear_shift
 */
bool carla_msgs::msg::CarlaEgoVehicleControl::manual_gear_shift() const
{
    return m_manual_gear_shift;
}

/*!
 * @brief This function returns a reference to member manual_gear_shift
 * @return Reference to member manual_gear_shift
 */
bool& carla_msgs::msg::CarlaEgoVehicleControl::manual_gear_shift()
{
    return m_manual_gear_shift;
}

size_t carla_msgs::msg::CarlaEgoVehicleControl::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return carla_msgs_msg_CarlaEgoVehicleControl_max_key_cdr_typesize;
}

bool carla_msgs::msg::CarlaEgoVehicleControl::isKeyDefined()
{
    return false;
}

void carla_msgs::msg::CarlaEgoVehicleControl::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
