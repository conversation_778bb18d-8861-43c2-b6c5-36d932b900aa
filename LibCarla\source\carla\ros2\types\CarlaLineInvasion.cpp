// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file CarlaLineInvasion.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "CarlaLineInvasion.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define carla_msgs_msg_std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define carla_msgs_msg_LaneInvasionEvent_max_cdr_typesize 672ULL;
#define carla_msgs_msg_std_msgs_msg_Time_max_cdr_typesize 8ULL;
#define carla_msgs_msg_std_msgs_msg_Header_max_key_cdr_typesize 0ULL;
#define carla_msgs_msg_LaneInvasionEvent_max_key_cdr_typesize 0ULL;
#define carla_msgs_msg_std_msgs_msg_Time_max_key_cdr_typesize 0ULL;

carla_msgs::msg::LaneInvasionEvent::LaneInvasionEvent()
{
}

carla_msgs::msg::LaneInvasionEvent::~LaneInvasionEvent()
{
}

carla_msgs::msg::LaneInvasionEvent::LaneInvasionEvent(
        const LaneInvasionEvent& x)
{
    m_header = x.m_header;
    m_crossed_lane_markings = x.m_crossed_lane_markings;
}

carla_msgs::msg::LaneInvasionEvent::LaneInvasionEvent(
        LaneInvasionEvent&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_crossed_lane_markings = std::move(x.m_crossed_lane_markings);
}

carla_msgs::msg::LaneInvasionEvent& carla_msgs::msg::LaneInvasionEvent::operator =(
        const LaneInvasionEvent& x)
{
    m_header = x.m_header;
    m_crossed_lane_markings = x.m_crossed_lane_markings;

    return *this;
}

carla_msgs::msg::LaneInvasionEvent& carla_msgs::msg::LaneInvasionEvent::operator =(
        LaneInvasionEvent&& x) noexcept
{
    m_header = std::move(x.m_header);
    m_crossed_lane_markings = std::move(x.m_crossed_lane_markings);

    return *this;
}

bool carla_msgs::msg::LaneInvasionEvent::operator ==(
        const LaneInvasionEvent& x) const
{
    return (m_header == x.m_header && m_crossed_lane_markings == x.m_crossed_lane_markings);
}

bool carla_msgs::msg::LaneInvasionEvent::operator !=(
        const LaneInvasionEvent& x) const
{
    return !(*this == x);
}

size_t carla_msgs::msg::LaneInvasionEvent::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return carla_msgs_msg_LaneInvasionEvent_max_cdr_typesize;
}

size_t carla_msgs::msg::LaneInvasionEvent::getCdrSerializedSize(
        const carla_msgs::msg::LaneInvasionEvent& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += std_msgs::msg::Header::getCdrSerializedSize(data.header(), current_alignment);
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);

    if (data.crossed_lane_markings().size() > 0)
    {
        current_alignment += (data.crossed_lane_markings().size() * 4) + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);
    }

    return current_alignment - initial_alignment;
}

void carla_msgs::msg::LaneInvasionEvent::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_header;
    scdr << m_crossed_lane_markings;
}

void carla_msgs::msg::LaneInvasionEvent::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_header;
    dcdr >> m_crossed_lane_markings;
}

/*!
 * @brief This function copies the value in member header
 * @param _header New value to be copied in member header
 */
void carla_msgs::msg::LaneInvasionEvent::header(
        const std_msgs::msg::Header& _header)
{
    m_header = _header;
}

/*!
 * @brief This function moves the value in member header
 * @param _header New value to be moved in member header
 */
void carla_msgs::msg::LaneInvasionEvent::header(
        std_msgs::msg::Header&& _header)
{
    m_header = std::move(_header);
}

/*!
 * @brief This function returns a constant reference to member header
 * @return Constant reference to member header
 */
const std_msgs::msg::Header& carla_msgs::msg::LaneInvasionEvent::header() const
{
    return m_header;
}

/*!
 * @brief This function returns a reference to member header
 * @return Reference to member header
 */
std_msgs::msg::Header& carla_msgs::msg::LaneInvasionEvent::header()
{
    return m_header;
}
/*!
 * @brief This function copies the value in member crossed_lane_markings
 * @param _crossed_lane_markings New value to be copied in member crossed_lane_markings
 */
void carla_msgs::msg::LaneInvasionEvent::crossed_lane_markings(
        const std::vector<int32_t>& _crossed_lane_markings)
{
    m_crossed_lane_markings = _crossed_lane_markings;
}

/*!
 * @brief This function moves the value in member crossed_lane_markings
 * @param _crossed_lane_markings New value to be moved in member crossed_lane_markings
 */
void carla_msgs::msg::LaneInvasionEvent::crossed_lane_markings(
        std::vector<int32_t>&& _crossed_lane_markings)
{
    m_crossed_lane_markings = std::move(_crossed_lane_markings);
}

/*!
 * @brief This function returns a constant reference to member crossed_lane_markings
 * @return Constant reference to member crossed_lane_markings
 */
const std::vector<int32_t>& carla_msgs::msg::LaneInvasionEvent::crossed_lane_markings() const
{
    return m_crossed_lane_markings;
}

/*!
 * @brief This function returns a reference to member crossed_lane_markings
 * @return Reference to member crossed_lane_markings
 */
std::vector<int32_t>& carla_msgs::msg::LaneInvasionEvent::crossed_lane_markings()
{
    return m_crossed_lane_markings;
}


size_t carla_msgs::msg::LaneInvasionEvent::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return carla_msgs_msg_LaneInvasionEvent_max_key_cdr_typesize;
}

bool carla_msgs::msg::LaneInvasionEvent::isKeyDefined()
{
    return false;
}

void carla_msgs::msg::LaneInvasionEvent::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
