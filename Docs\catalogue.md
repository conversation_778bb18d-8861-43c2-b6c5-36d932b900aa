# CARLA catalogue

The CARLA simulator provides a vast library of 3D assets to populate your autonomous agent's virtual environment. The 3D asset library provides numerous pre-built maps to choose from, a diverse array of vehicle models for traffic simulation along with models of pedestrians and other structures or obstacles that can be dynamically added to your simulation during runtime. This catalogue documents all of the 3D assets available to use in your simulations. 

* [__Vehicles catalogue__](catalogue_vehicles.md)
* [__Pedestrians catalogue__](catalogue_pedestrians.md)
* [__Props catalogue__](catalogue_props.md)

## Maps

The CARLA simulator provides 10 pre-built maps to choose from, providing a diverse array of environments for training and testing your autonomous agents. 

![maps_montage](../img/catalogue/maps/maps_montage.webp)

| Town       | Summary |
| -----------| ------  |
| [__Town01__](map_town01.md)  | A small, simple town with a river and several bridges.|
| [__Town02__](map_town02.md) | A small simple town with a mixture of residential and commercial buildings.|
| [__Town03__](map_town03.md) | A larger, urban map with a roundabout and large junctions.|
| [__Town04__](map_town04.md) | A small town embedded in the mountains with a special "figure of 8" *infinite* highway.|
| [__Town05__](map_town05.md) | Squared-grid town with cross junctions and a bridge. It has multiple lanes per direction. Useful to perform lane changes.  |
| [__Town06__](map_town06.md) | Long many lane highways with many highway entrances and exits. It also has a [**Michigan left**](<https://en.wikipedia.org/wiki/Michigan_left>). |
| [__Town07__](map_town07.md) | A rural environment with narrow roads, corn, barns and hardly any traffic lights. |
| **Town08** | Secret "unseen" town used for the [Leaderboard](https://leaderboard.carla.org/) challenge |
| **Town09** | Secret "unseen" town used for the [Leaderboard](https://leaderboard.carla.org/) challenge |
| [__Town10__](map_town10.md) | A downtown urban environment with skyscrapers, residential buildings and an ocean promenade.|
| [__Town11__](map_town11.md) | A Large Map that is undecorated. Serves as a proof of concept for the Large Maps feature. |
| [__Town12__](map_town12.md) | A Large Map with numerous different regions, including high-rise, residential and rural environments.|
| [__Town13__](map_town13.md) | A Large Map similar in scale to Town 12, but with distinct features.|
| [__Town15__](map_town15.md) | A map based on the road layout of the Autonomous University of Barcelona. |

## Vehicles

CARLA provides a diverse array of vehicles, with high fidelity models of real world cars, trucks and bikes, for replicating traffic in your simulations. Browse and choose the vehicles you like in the [__vehicle catalogue__](catalogue_vehicles.md).

![vehicles_overview](../img/catalogue/vehicles/vehicle_montage.webp)

## Pedestrians

CARLA's asset library includes a variety of pedestrians to simulate foot traffic in the 3D environments surrounding your agent. Browse and choose the pedestrians you want in the [__pedestrian catalogue__](catalogue_pedestrians.md).

![pedestrian_overview](../img/catalogue/pedestrians/pedestrians_overview.webp)

## Props

CARLA's props model the various structures and items you might find on or near roads, such as kiosks, statues, benches, boxes, bins, debris or trash. The props can be placed anywhere in your simulation dynamically during runtime. Browse and choose your props in the [__props catalogue__](catalogue_props.md).

![props_overview](../img/catalogue/props/props_overview.webp)

