// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file CameraInfo.h
 * This header file contains the declaration of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifndef _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_CAMERAINFO_H_
#define _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_CAMERAINFO_H_

#include "RegionOfInterest.h"
#include "Header.h"

#include <fastrtps/utils/fixed_size_string.hpp>

#include <stdint.h>
#include <array>
#include <string>
#include <vector>
#include <map>
#include <bitset>

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#define eProsima_user_DllExport __declspec( dllexport )
#else
#define eProsima_user_DllExport
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define eProsima_user_DllExport
#endif  // _WIN32

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#if defined(CAMERAINFO_SOURCE)
#define CAMERAINFO_DllAPI __declspec( dllexport )
#else
#define CAMERAINFO_DllAPI __declspec( dllimport )
#endif // CAMERAINFO_SOURCE
#else
#define CAMERAINFO_DllAPI
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define CAMERAINFO_DllAPI
#endif // _WIN32

namespace eprosima {
namespace fastcdr {
class Cdr;
} // namespace fastcdr
} // namespace eprosima

namespace sensor_msgs {
    namespace msg {
        /*!
         * @brief This class represents the structure CameraInfo defined by the user in the IDL file.
         * @ingroup CameraInfo
         */
        class CameraInfo
        {
        public:

            /*!
             * @brief Default constructor.
             */
            eProsima_user_DllExport CameraInfo(uint32_t height = 0, uint32_t width = 0, double fov = 0.0);

            /*!
             * @brief Default destructor.
             */
            eProsima_user_DllExport ~CameraInfo();

            /*!
             * @brief Copy constructor.
             * @param x Reference to the object sensor_msgs::msg::CameraInfo that will be copied.
             */
            eProsima_user_DllExport CameraInfo(
                    const CameraInfo& x);

            /*!
             * @brief Move constructor.
             * @param x Reference to the object sensor_msgs::msg::CameraInfo that will be copied.
             */
            eProsima_user_DllExport CameraInfo(
                    CameraInfo&& x) noexcept;

            /*!
             * @brief Copy assignment.
             * @param x Reference to the object sensor_msgs::msg::CameraInfo that will be copied.
             */
            eProsima_user_DllExport CameraInfo& operator =(
                    const CameraInfo& x);

            /*!
             * @brief Move assignment.
             * @param x Reference to the object sensor_msgs::msg::CameraInfo that will be copied.
             */
            eProsima_user_DllExport CameraInfo& operator =(
                    CameraInfo&& x) noexcept;

            /*!
             * @brief Comparison operator.
             * @param x sensor_msgs::msg::CameraInfo object to compare.
             */
            eProsima_user_DllExport bool operator ==(
                    const CameraInfo& x) const;

            /*!
             * @brief Comparison operator.
             * @param x sensor_msgs::msg::CameraInfo object to compare.
             */
            eProsima_user_DllExport bool operator !=(
                    const CameraInfo& x) const;

            /*!
             * @brief This function copies the value in member header
             * @param _header New value to be copied in member header
             */
            eProsima_user_DllExport void header(
                    const std_msgs::msg::Header& _header);

            /*!
             * @brief This function moves the value in member header
             * @param _header New value to be moved in member header
             */
            eProsima_user_DllExport void header(
                    std_msgs::msg::Header&& _header);

            /*!
             * @brief This function returns a constant reference to member header
             * @return Constant reference to member header
             */
            eProsima_user_DllExport const std_msgs::msg::Header& header() const;

            /*!
             * @brief This function returns a reference to member header
             * @return Reference to member header
             */
            eProsima_user_DllExport std_msgs::msg::Header& header();
            /*!
             * @brief This function sets a value in member height
             * @param _height New value for member height
             */
            eProsima_user_DllExport void height(
                    uint32_t _height);

            /*!
             * @brief This function returns the value of member height
             * @return Value of member height
             */
            eProsima_user_DllExport uint32_t height() const;

            /*!
             * @brief This function returns a reference to member height
             * @return Reference to member height
             */
            eProsima_user_DllExport uint32_t& height();

            /*!
             * @brief This function sets a value in member width
             * @param _width New value for member width
             */
            eProsima_user_DllExport void width(
                    uint32_t _width);

            /*!
             * @brief This function returns the value of member width
             * @return Value of member width
             */
            eProsima_user_DllExport uint32_t width() const;

            /*!
             * @brief This function returns a reference to member width
             * @return Reference to member width
             */
            eProsima_user_DllExport uint32_t& width();

            /*!
             * @brief This function copies the value in member distortion_model
             * @param _distortion_model New value to be copied in member distortion_model
             */
            eProsima_user_DllExport void distortion_model(
                    const std::string& _distortion_model);

            /*!
             * @brief This function moves the value in member distortion_model
             * @param _distortion_model New value to be moved in member distortion_model
             */
            eProsima_user_DllExport void distortion_model(
                    std::string&& _distortion_model);

            /*!
             * @brief This function returns a constant reference to member distortion_model
             * @return Constant reference to member distortion_model
             */
            eProsima_user_DllExport const std::string& distortion_model() const;

            /*!
             * @brief This function returns a reference to member distortion_model
             * @return Reference to member distortion_model
             */
            eProsima_user_DllExport std::string& distortion_model();
            /*!
             * @brief This function copies the value in member D
             * @param _D New value to be copied in member D
             */
            eProsima_user_DllExport void D(
                    const std::vector<double>& _D);

            /*!
             * @brief This function moves the value in member D
             * @param _D New value to be moved in member D
             */
            eProsima_user_DllExport void D(
                    std::vector<double>&& _D);

            /*!
             * @brief This function returns a constant reference to member D
             * @return Constant reference to member D
             */
            eProsima_user_DllExport const std::vector<double>& D() const;

            /*!
             * @brief This function returns a reference to member D
             * @return Reference to member D
             */
            eProsima_user_DllExport std::vector<double>& D();
            /*!
             * @brief This function copies the value in member K
             * @param _K New value to be copied in member K
             */
            eProsima_user_DllExport void k(
                    const std::array<double, 9>& _k);

        /*!
        * @brief This function moves the value in member k
        * @param _k New value to be moved in member k
        */
        eProsima_user_DllExport void k(
                std::array<double, 9>&& _k);

        /*!
        * @brief This function returns a constant reference to member k
        * @return Constant reference to member k
        */
        eProsima_user_DllExport const std::array<double, 9>& k() const;

        /*!
        * @brief This function returns a reference to member k
        * @return Reference to member k
        */
        eProsima_user_DllExport std::array<double, 9>& k();
        /*!
        * @brief This function copies the value in member r
        * @param _r New value to be copied in member r
        */
        eProsima_user_DllExport void r(
                const std::array<double, 9>& _r);

        /*!
        * @brief This function moves the value in member r
        * @param _r New value to be moved in member r
        */
        eProsima_user_DllExport void r(
                std::array<double, 9>&& _r);

        /*!
        * @brief This function returns a constant reference to member r
        * @return Constant reference to member r
        */
        eProsima_user_DllExport const std::array<double, 9>& r() const;

        /*!
        * @brief This function returns a reference to member r
        * @return Reference to member r
        */
        eProsima_user_DllExport std::array<double, 9>& r();
        /*!
        * @brief This function copies the value in member p
        * @param _p New value to be copied in member p
        */
        eProsima_user_DllExport void p(
                const std::array<double, 12>& _p);

        /*!
        * @brief This function moves the value in member p
        * @param _p New value to be moved in member p
        */
        eProsima_user_DllExport void p(
                std::array<double, 12>&& _p);

        /*!
        * @brief This function returns a constant reference to member p
        * @return Constant reference to member p
        */
        eProsima_user_DllExport const std::array<double, 12>& p() const;

        /*!
        * @brief This function returns a reference to member p
        * @return Reference to member p
        */
        eProsima_user_DllExport std::array<double, 12>& p();
        /*!
        * @brief This function sets a value in member binning_x
        * @param _binning_x New value for member binning_x
        */
        eProsima_user_DllExport void binning_x(
                uint32_t _binning_x);

            /*!
             * @brief This function returns the value of member binning_x
             * @return Value of member binning_x
             */
            eProsima_user_DllExport uint32_t binning_x() const;

            /*!
             * @brief This function returns a reference to member binning_x
             * @return Reference to member binning_x
             */
            eProsima_user_DllExport uint32_t& binning_x();

            /*!
             * @brief This function sets a value in member binning_y
             * @param _binning_y New value for member binning_y
             */
            eProsima_user_DllExport void binning_y(
                    uint32_t _binning_y);

            /*!
             * @brief This function returns the value of member binning_y
             * @return Value of member binning_y
             */
            eProsima_user_DllExport uint32_t binning_y() const;

            /*!
             * @brief This function returns a reference to member binning_y
             * @return Reference to member binning_y
             */
            eProsima_user_DllExport uint32_t& binning_y();

            /*!
             * @brief This function copies the value in member roi
             * @param _roi New value to be copied in member roi
             */
            eProsima_user_DllExport void roi(
                    const sensor_msgs::msg::RegionOfInterest& _roi);

            /*!
             * @brief This function moves the value in member roi
             * @param _roi New value to be moved in member roi
             */
            eProsima_user_DllExport void roi(
                    sensor_msgs::msg::RegionOfInterest&& _roi);

            /*!
             * @brief This function returns a constant reference to member roi
             * @return Constant reference to member roi
             */
            eProsima_user_DllExport const sensor_msgs::msg::RegionOfInterest& roi() const;

            /*!
             * @brief This function returns a reference to member roi
             * @return Reference to member roi
             */
            eProsima_user_DllExport sensor_msgs::msg::RegionOfInterest& roi();

            /*!
            * @brief This function returns the maximum serialized size of an object
            * depending on the buffer alignment.
            * @param current_alignment Buffer alignment.
            * @return Maximum serialized size.
            */
            eProsima_user_DllExport static size_t getMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function returns the serialized size of a data depending on the buffer alignment.
             * @param data Data which is calculated its serialized size.
             * @param current_alignment Buffer alignment.
             * @return Serialized size.
             */
            eProsima_user_DllExport static size_t getCdrSerializedSize(
                    const sensor_msgs::msg::CameraInfo& data,
                    size_t current_alignment = 0);

            /*!
             * @brief This function serializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serialize(
                    eprosima::fastcdr::Cdr& cdr) const;

            /*!
             * @brief This function deserializes an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void deserialize(
                    eprosima::fastcdr::Cdr& cdr);

            /*!
             * @brief This function returns the maximum serialized size of the Key of an object
             * depending on the buffer alignment.
             * @param current_alignment Buffer alignment.
             * @return Maximum serialized size.
             */
            eProsima_user_DllExport static size_t getKeyMaxCdrSerializedSize(
                    size_t current_alignment = 0);

            /*!
             * @brief This function tells you if the Key has been defined for this type
             */
            eProsima_user_DllExport static bool isKeyDefined();

            /*!
             * @brief This function serializes the key members of an object using CDR serialization.
             * @param cdr CDR serialization object.
             */
            eProsima_user_DllExport void serializeKey(
                    eprosima::fastcdr::Cdr& cdr) const;

        private:
        std_msgs::msg::Header m_header;
        uint32_t m_height;
        uint32_t m_width;
        std::string m_distortion_model;
        std::vector<double> m_d;
        std::array<double, 9> m_k;
        std::array<double, 9> m_r;
        std::array<double, 12> m_p;
        uint32_t m_binning_x;
        uint32_t m_binning_y;
        sensor_msgs::msg::RegionOfInterest m_roi;
        };
    } // namespace msg
} // namespace sensor_msgs

#endif // _FAST_DDS_GENERATED_SENSOR_MSGS_MSG_CAMERAINFO_H_
