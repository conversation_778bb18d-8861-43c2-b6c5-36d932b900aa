// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file String.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "String.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define std_msgs_msg_String_max_cdr_typesize 260ULL;
#define std_msgs_msg_String_max_key_cdr_typesize 0ULL;

std_msgs::msg::String::String()
{
    // string m_data
    m_data ="";
}

std_msgs::msg::String::~String()
{
}

std_msgs::msg::String::String(
        const String& x)
{
    m_data = x.m_data;
}

std_msgs::msg::String::String(
        String&& x) noexcept
{
    m_data = std::move(x.m_data);
}

std_msgs::msg::String& std_msgs::msg::String::operator =(
        const String& x)
{
    m_data = x.m_data;

    return *this;
}

std_msgs::msg::String& std_msgs::msg::String::operator =(
        String&& x) noexcept
{
    m_data = std::move(x.m_data);

    return *this;
}

bool std_msgs::msg::String::operator ==(
        const String& x) const
{
    return (m_data == x.m_data);
}

bool std_msgs::msg::String::operator !=(
        const String& x) const
{
    return !(*this == x);
}

size_t std_msgs::msg::String::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return std_msgs_msg_String_max_cdr_typesize;
}

size_t std_msgs::msg::String::getCdrSerializedSize(
        const std_msgs::msg::String& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4) + data.data().size() + 1;

    return current_alignment - initial_alignment;
}

void std_msgs::msg::String::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_data.c_str();
}

void std_msgs::msg::String::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_data;
}

/*!
 * @brief This function copies the value in member data
 * @param _data New value to be copied in member data
 */
void std_msgs::msg::String::data(
        const std::string& _data)
{
    m_data = _data;
}

/*!
 * @brief This function moves the value in member data
 * @param _data New value to be moved in member data
 */
void std_msgs::msg::String::data(
        std::string&& _data)
{
    m_data = std::move(_data);
}

/*!
 * @brief This function returns a constant reference to member data
 * @return Constant reference to member data
 */
const std::string& std_msgs::msg::String::data() const
{
    return m_data;
}

/*!
 * @brief This function returns a reference to member data
 * @return Reference to member data
 */
std::string& std_msgs::msg::String::data()
{
    return m_data;
}

size_t std_msgs::msg::String::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return std_msgs_msg_String_max_key_cdr_typesize;
}

bool std_msgs::msg::String::isKeyDefined()
{
    return false;
}

void std_msgs::msg::String::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
