// Copyright (c) 2017 Computer Vision Center (CVC) at the Universitat Autonoma
// de Barcelona (UAB).
//
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#pragma once

#include <cstdint>
#include <string>

namespace carla {
namespace road {

  using RoadId = uint32_t;

  using JuncId = int32_t;

  using LaneId = int32_t;

  using SectionId = uint32_t;

  using ObjId = uint32_t;

  using SignId = std::string;

  using ConId = uint32_t;

  using ContId = std::string;

} // road
} // carla
