#!/usr/bin/env python3

"""
Cosmos Transfer Data Viewer

This script allows you to view the captured Cosmos Transfer batches in a pygame window.
You can view RGB, depth, and semantic segmentation data as image sequences.

Usage:
    python3 cosmos_data_viewer.py --batch-dir cosmos_transfer_data/batch_0001_20250818_123045
    python3 cosmos_data_viewer.py --batch-dir cosmos_transfer_data/batch_0001_20250818_123045 --data-type rgb
    python3 cosmos_data_viewer.py --list-batches cosmos_transfer_data
"""

import pygame
import numpy as np
import json
import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("CosmosDataViewer")

class CosmosDataViewer:
    """
    Pygame-based viewer for Cosmos Transfer batch data
    """
    
    def __init__(self, window_size: Tuple[int, int] = (1200, 800)):
        pygame.init()
        self.window_size = window_size
        self.screen = pygame.display.set_mode(window_size)
        pygame.display.set_caption("Cosmos Transfer Data Viewer")
        
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # Viewer state
        self.current_frame = 0
        self.playing = False
        self.fps = 10
        self.data_type = 'rgb'
        
        # Data storage
        self.batch_data = None
        self.metadata = None
        self.frames = []
        
        logger.info(f"Cosmos Data Viewer initialized with window size {window_size}")
    
    def load_batch(self, batch_dir: Path, data_type: str = 'rgb') -> bool:
        """
        Load a Cosmos Transfer batch for viewing
        
        Args:
            batch_dir: Path to batch directory
            data_type: Type of data to load ('rgb', 'depth', 'semantic')
        
        Returns:
            True if successful, False otherwise
        """
        
        if not batch_dir.exists():
            logger.error(f"Batch directory does not exist: {batch_dir}")
            return False
        
        # Load metadata
        metadata_file = batch_dir / "metadata.json"
        if not metadata_file.exists():
            logger.error(f"Metadata file not found: {metadata_file}")
            return False
        
        with open(metadata_file, 'r') as f:
            self.metadata = json.load(f)
        
        # Load prompt
        prompt_file = batch_dir / "prompt.txt"
        if prompt_file.exists():
            with open(prompt_file, 'r') as f:
                self.prompt = f.read().strip()
        else:
            self.prompt = "No prompt available"
        
        # Load frame data
        self.data_type = data_type
        frames_dir = batch_dir / f"{data_type}_frames"
        
        if not frames_dir.exists():
            logger.error(f"Frames directory not found: {frames_dir}")
            return False
        
        # Load all frame files
        frame_files = sorted(frames_dir.glob("frame_*.png")) + sorted(frames_dir.glob("frame_*.npy"))
        
        if not frame_files:
            logger.error(f"No frame files found in {frames_dir}")
            return False
        
        self.frames = []
        for frame_file in frame_files:
            try:
                if frame_file.suffix == '.npy':
                    # Load numpy array
                    frame_data = np.load(frame_file)
                else:
                    # Load image file
                    try:
                        from PIL import Image
                        img = Image.open(frame_file)
                        frame_data = np.array(img)
                    except ImportError:
                        logger.warning(f"PIL not available, skipping {frame_file}")
                        continue
                
                self.frames.append(frame_data)
                
            except Exception as e:
                logger.warning(f"Failed to load frame {frame_file}: {e}")
                continue
        
        if not self.frames:
            logger.error("No frames could be loaded")
            return False
        
        logger.info(f"Loaded {len(self.frames)} frames of type '{data_type}' from {batch_dir}")
        logger.info(f"Batch: {self.metadata.get('batch_id', 'Unknown')}")
        logger.info(f"Condition: {self.metadata.get('environmental_condition', 'Unknown')}")
        
        self.current_frame = 0
        return True
    
    def process_frame_for_display(self, frame_data: np.ndarray) -> np.ndarray:
        """
        Process frame data for pygame display
        
        Args:
            frame_data: Raw frame data
            
        Returns:
            Processed frame ready for display
        """
        
        if self.data_type == 'rgb':
            # RGB data - ensure it's in the right format
            if frame_data.dtype != np.uint8:
                frame_data = (frame_data * 255).astype(np.uint8)
            
            if len(frame_data.shape) == 3 and frame_data.shape[2] == 3:
                return frame_data
            else:
                logger.warning(f"Unexpected RGB frame shape: {frame_data.shape}")
                return np.zeros((480, 640, 3), dtype=np.uint8)
        
        elif self.data_type == 'depth':
            # Depth data - convert to grayscale visualization
            if len(frame_data.shape) == 2:
                # Normalize depth data for visualization
                if frame_data.max() > frame_data.min():
                    normalized = ((frame_data - frame_data.min()) / (frame_data.max() - frame_data.min()) * 255).astype(np.uint8)
                else:
                    normalized = np.zeros_like(frame_data, dtype=np.uint8)
                
                # Convert to RGB for display
                return np.stack([normalized, normalized, normalized], axis=2)
            else:
                logger.warning(f"Unexpected depth frame shape: {frame_data.shape}")
                return np.zeros((480, 640, 3), dtype=np.uint8)
        
        elif self.data_type == 'semantic':
            # Semantic data - convert labels to colors
            if len(frame_data.shape) == 2:
                # Create a simple colormap for semantic labels
                colormap = self.create_semantic_colormap()
                colored = np.zeros((*frame_data.shape, 3), dtype=np.uint8)
                
                for label_id, color in colormap.items():
                    mask = frame_data == label_id
                    colored[mask] = color
                
                return colored
            else:
                logger.warning(f"Unexpected semantic frame shape: {frame_data.shape}")
                return np.zeros((480, 640, 3), dtype=np.uint8)
        
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def create_semantic_colormap(self) -> Dict[int, Tuple[int, int, int]]:
        """Create a colormap for semantic segmentation labels"""
        # CARLA semantic segmentation color palette
        colormap = {
            0: (0, 0, 0),        # Unlabeled
            1: (70, 70, 70),     # Building
            2: (100, 40, 40),    # Fence
            3: (55, 90, 80),     # Other
            4: (220, 20, 60),    # Pedestrian
            5: (153, 153, 153),  # Pole
            6: (157, 234, 50),   # RoadLine
            7: (128, 64, 128),   # Road
            8: (244, 35, 232),   # SideWalk
            9: (107, 142, 35),   # Vegetation
            10: (0, 0, 142),     # Vehicles
            11: (102, 102, 156), # Wall
            12: (220, 220, 0),   # TrafficSign
            13: (70, 130, 180),  # Sky
            14: (81, 0, 81),     # Ground
            15: (150, 100, 100), # Bridge
            16: (230, 150, 140), # RailTrack
            17: (180, 165, 180), # GuardRail
            18: (250, 170, 30),  # TrafficLight
            19: (110, 190, 160), # Static
            20: (170, 120, 50),  # Dynamic
            21: (45, 60, 150),   # Water
            22: (145, 170, 100), # Terrain
        }
        return colormap
    
    def draw_frame(self):
        """Draw the current frame and UI"""
        self.screen.fill((0, 0, 0))
        
        if not self.frames:
            # No data loaded
            text = self.font.render("No data loaded", True, (255, 255, 255))
            text_rect = text.get_rect(center=(self.window_size[0]//2, self.window_size[1]//2))
            self.screen.blit(text, text_rect)
            return
        
        # Get current frame
        frame_data = self.frames[self.current_frame]
        processed_frame = self.process_frame_for_display(frame_data)
        
        # Scale frame to fit window while maintaining aspect ratio
        frame_height, frame_width = processed_frame.shape[:2]
        
        # Calculate scaling to fit in the main display area (leave space for UI)
        ui_height = 100
        available_width = self.window_size[0] - 40
        available_height = self.window_size[1] - ui_height - 40
        
        scale_x = available_width / frame_width
        scale_y = available_height / frame_height
        scale = min(scale_x, scale_y)
        
        new_width = int(frame_width * scale)
        new_height = int(frame_height * scale)
        
        # Convert to pygame surface and scale
        frame_surface = pygame.surfarray.make_surface(processed_frame.swapaxes(0, 1))
        scaled_surface = pygame.transform.scale(frame_surface, (new_width, new_height))
        
        # Center the frame
        frame_x = (self.window_size[0] - new_width) // 2
        frame_y = (self.window_size[1] - ui_height - new_height) // 2
        
        self.screen.blit(scaled_surface, (frame_x, frame_y))
        
        # Draw UI
        self.draw_ui()
    
    def draw_ui(self):
        """Draw the user interface"""
        ui_y = self.window_size[1] - 90
        
        # Frame info
        frame_text = f"Frame: {self.current_frame + 1}/{len(self.frames)}"
        frame_surface = self.font.render(frame_text, True, (255, 255, 255))
        self.screen.blit(frame_surface, (10, ui_y))
        
        # Data type
        type_text = f"Type: {self.data_type.upper()}"
        type_surface = self.font.render(type_text, True, (255, 255, 255))
        self.screen.blit(type_surface, (200, ui_y))
        
        # Play/Pause status
        status_text = "Playing" if self.playing else "Paused"
        status_color = (0, 255, 0) if self.playing else (255, 255, 0)
        status_surface = self.font.render(status_text, True, status_color)
        self.screen.blit(status_surface, (350, ui_y))
        
        # FPS
        fps_text = f"FPS: {self.fps}"
        fps_surface = self.font.render(fps_text, True, (255, 255, 255))
        self.screen.blit(fps_surface, (500, ui_y))
        
        # Controls
        controls_y = ui_y + 30
        controls_text = "Controls: SPACE=Play/Pause, LEFT/RIGHT=Navigate, +/-=Speed, Q=Quit"
        controls_surface = self.small_font.render(controls_text, True, (200, 200, 200))
        self.screen.blit(controls_surface, (10, controls_y))
        
        # Batch info
        if self.metadata:
            batch_info = f"Batch: {self.metadata.get('batch_id', 'Unknown')} | Condition: {self.metadata.get('environmental_condition', 'Unknown')}"
            info_surface = self.small_font.render(batch_info, True, (150, 150, 255))
            self.screen.blit(info_surface, (10, 10))
    
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_q:
                    return False
                elif event.key == pygame.K_SPACE:
                    self.playing = not self.playing
                elif event.key == pygame.K_LEFT:
                    self.current_frame = max(0, self.current_frame - 1)
                elif event.key == pygame.K_RIGHT:
                    self.current_frame = min(len(self.frames) - 1, self.current_frame + 1)
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.fps = min(60, self.fps + 1)
                elif event.key == pygame.K_MINUS:
                    self.fps = max(1, self.fps - 1)
                elif event.key == pygame.K_HOME:
                    self.current_frame = 0
                elif event.key == pygame.K_END:
                    self.current_frame = len(self.frames) - 1
        
        return True
    
    def run(self):
        """Main viewer loop"""
        running = True
        last_frame_time = time.time()
        
        while running:
            running = self.handle_events()
            
            # Auto-advance frames if playing
            if self.playing and self.frames:
                current_time = time.time()
                if current_time - last_frame_time >= 1.0 / self.fps:
                    self.current_frame = (self.current_frame + 1) % len(self.frames)
                    last_frame_time = current_time
            
            self.draw_frame()
            pygame.display.flip()
            self.clock.tick(60)  # 60 FPS for smooth UI
        
        pygame.quit()

def list_batches(data_dir: Path) -> List[Path]:
    """List all available batches in the data directory"""
    if not data_dir.exists():
        logger.error(f"Data directory does not exist: {data_dir}")
        return []
    
    batch_dirs = [d for d in data_dir.iterdir() if d.is_dir() and d.name.startswith('batch_')]
    batch_dirs.sort()
    
    return batch_dirs

def main():
    parser = argparse.ArgumentParser(description="View Cosmos Transfer batch data")
    parser.add_argument('--batch-dir', type=str, help='Path to specific batch directory')
    parser.add_argument('--data-type', choices=['rgb', 'depth', 'semantic'], default='rgb',
                       help='Type of data to view')
    parser.add_argument('--list-batches', type=str, help='List all batches in directory')
    parser.add_argument('--window-size', nargs=2, type=int, default=[1200, 800],
                       help='Window size (width height)')
    
    args = parser.parse_args()
    
    if args.list_batches:
        # List available batches
        data_dir = Path(args.list_batches)
        batches = list_batches(data_dir)
        
        if not batches:
            print("No batches found.")
            return
        
        print(f"Found {len(batches)} batches in {data_dir}:")
        for i, batch_dir in enumerate(batches, 1):
            metadata_file = batch_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                condition = metadata.get('environmental_condition', 'Unknown')
                frames = metadata.get('sequence_length', 'Unknown')
                print(f"  {i}. {batch_dir.name} - {condition} ({frames} frames)")
            else:
                print(f"  {i}. {batch_dir.name} - No metadata")
        
        print(f"\nTo view a batch, use:")
        print(f"python3 {sys.argv[0]} --batch-dir {batches[0]} --data-type rgb")
        return
    
    if not args.batch_dir:
        print("Please specify --batch-dir or use --list-batches to see available batches")
        return
    
    # Create viewer and load data
    viewer = CosmosDataViewer(tuple(args.window_size))
    
    batch_dir = Path(args.batch_dir)
    if not viewer.load_batch(batch_dir, args.data_type):
        logger.error("Failed to load batch data")
        return
    
    print(f"Viewing {args.data_type} data from {batch_dir}")
    print("Controls:")
    print("  SPACE - Play/Pause")
    print("  LEFT/RIGHT - Navigate frames")
    print("  +/- - Adjust playback speed")
    print("  HOME/END - Go to first/last frame")
    print("  Q - Quit")
    
    # Run viewer
    viewer.run()

if __name__ == '__main__':
    main()
