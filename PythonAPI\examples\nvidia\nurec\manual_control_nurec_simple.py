#!/usr/bin/env python3

"""
Simple Manual Control in NUREC Reconstructed Environment

This script allows you to manually drive through a NUREC reconstructed environment.
It combines NUREC's photorealistic reconstruction with manual vehicle control.

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    Esc         : Quit

Usage:
    python3 manual_control_nurec_simple.py --usdz-filename /path/to/scenario.usdz
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time
from typing import Optional

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("ManualControlNurec")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class ManualControlNurec:
    """
    Main class for manual control in NUREC environment
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.scenario = None
        self.display = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Pygame setup
        pygame.init()
        self.clock = pygame.time.Clock()
        
        # Initialize display for controls info
        self.info_display = pygame.display.set_mode((400, 200))
        pygame.display.set_caption("Manual Control Info")
        self.font = pygame.font.Font(None, 24)
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            self.world = self.client.get_world()
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def load_nurec_scenario(self):
        """Load NUREC scenario"""
        try:
            logger.info(f"Loading NUREC scenario: {self.args.usdz_filename}")
            self.scenario = NurecScenario(
                self.client, 
                self.args.usdz_filename, 
                port=self.args.nurec_port
            )
            self.scenario.__enter__()  # Enter the context
            logger.info("NUREC scenario loaded successfully")
            
            # Setup NUREC display
            self.display = PygameDisplay()
            self.scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: self.display.setImage(image, (1, 1), (0, 0)),
                framerate=30,
                resolution_ratio=0.5
            )
            
            return True
        except Exception as e:
            logger.error(f"Failed to load NUREC scenario: {e}")
            return False
    
    def setup_vehicle(self):
        """Setup the ego vehicle for manual control"""
        try:
            # Get the ego vehicle from NUREC scenario
            if EGO_TRACK_ID in self.scenario.actor_mapping:
                self.vehicle = self.scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {self.vehicle.id}")
                
                # Enable physics for manual control
                self.scenario.actor_mapping[EGO_TRACK_ID].set_physics(True, self.scenario.get_sim_time())
                
                # Set initial control
                self.vehicle.apply_control(self.control)
                
                return True
            else:
                logger.error("Ego vehicle not found in scenario")
                return False
        except Exception as e:
            logger.error(f"Failed to setup vehicle: {e}")
            return False
    
    def handle_input(self):
        """Handle keyboard input for vehicle control"""
        keys = pygame.key.get_pressed()
        
        # Handle pygame events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_r:
                    self.restart_vehicle()
                elif event.key == pygame.K_p:
                    self.toggle_autopilot()
                elif event.key == pygame.K_q:
                    # Toggle reverse
                    self.control.gear = 1 if self.control.reverse else -1
        
        # Continuous key handling
        if not self.autopilot_enabled:
            # Throttle/Brake
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                self.control.throttle = min(self.control.throttle + 0.01, 1.0)
            else:
                self.control.throttle = 0.0
            
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                self.control.brake = min(self.control.brake + 0.2, 1.0)
            else:
                self.control.brake = 0.0
            
            # Steering
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                self.control.steer = max(self.control.steer - 0.01, -1.0)
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                self.control.steer = min(self.control.steer + 0.01, 1.0)
            else:
                self.control.steer *= 0.9  # Gradual return to center
            
            # Hand brake
            self.control.hand_brake = keys[pygame.K_SPACE]
            
            # Set reverse flag
            self.control.reverse = self.control.gear < 0
            
            # Apply control to vehicle
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        return True
    
    def toggle_autopilot(self):
        """Toggle autopilot mode"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        logger.info(f"Autopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def restart_vehicle(self):
        """Restart vehicle at a spawn point"""
        if self.vehicle and self.world:
            try:
                spawn_points = self.world.get_map().get_spawn_points()
                if spawn_points:
                    import random
                    spawn_point = random.choice(spawn_points)
                    self.vehicle.set_transform(spawn_point)
                    logger.info("Vehicle restarted at new location")
            except Exception as e:
                logger.warning(f"Failed to restart vehicle: {e}")
    
    def update_info_display(self):
        """Update the info display with current status"""
        self.info_display.fill((0, 0, 0))
        
        # Get vehicle info
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * np.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
            transform = self.vehicle.get_transform()
        else:
            speed_kmh = 0
            transform = None
        
        # Create info text
        info_lines = [
            "Manual Control in NUREC Environment",
            f"Speed: {speed_kmh:.1f} km/h",
            f"Throttle: {self.control.throttle:.2f}",
            f"Brake: {self.control.brake:.2f}",
            f"Steer: {self.control.steer:.2f}",
            f"Autopilot: {'ON' if self.autopilot_enabled else 'OFF'}",
            f"Reverse: {'ON' if self.control.reverse else 'OFF'}",
            "",
            "Controls: W/S=Throttle/Brake, A/D=Steer",
            "P=Autopilot, R=Restart, Q=Reverse, ESC=Quit"
        ]
        
        # Render text
        y_offset = 10
        for line in info_lines:
            if line:  # Skip empty lines
                text_surface = self.font.render(line, True, (255, 255, 255))
                self.info_display.blit(text_surface, (10, y_offset))
            y_offset += 20
        
        pygame.display.flip()
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False
        
        if not self.load_nurec_scenario():
            return False
        
        if not self.setup_vehicle():
            return False
        
        logger.info("Starting manual control loop...")
        logger.info("Use WASD or arrow keys to control the vehicle")
        logger.info("Press P to toggle autopilot, R to restart, ESC to quit")
        
        running = True
        try:
            while running:
                # Handle input
                running = self.handle_input()
                
                # Tick the scenario and world
                if self.scenario:
                    self.scenario.tick()
                if self.world:
                    self.world.tick()
                
                # Update displays
                self.update_info_display()
                
                # Control frame rate
                self.clock.tick(60)  # 60 FPS
                
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            handle_exception(e)
        finally:
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        if self.display:
            self.display.destroy()
        
        if self.scenario:
            try:
                self.scenario.__exit__(None, None, None)
            except:
                pass
        
        pygame.quit()
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Manual Control in NUREC Environment")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    # Create and run the manual control
    manual_control = ManualControlNurec(args)
    success = manual_control.run()
    
    if success:
        logger.info("Manual control session completed successfully")
    else:
        logger.error("Manual control session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
