// Copyright (c) 2017 Computer Vision Center (CVC) at the Universitat Autonoma
// de Barcelona (UAB).
//
// This work is licensed under the terms of the MIT license.
// For a copy, see <https://opensource.org/licenses/MIT>.

#pragma once

#include "carla/streaming/detail/tcp/ServerSession.h"

namespace carla {
namespace streaming {
namespace detail {

  using Session = tcp::ServerSession;

} // namespace detail
} // namespace streaming
} // namespace carla
