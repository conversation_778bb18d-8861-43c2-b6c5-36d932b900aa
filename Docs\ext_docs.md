# Extended documentation

Below, you will find in depth documentation on the many extensive features of CARLA.

## Advanced concepts
 
[__Recorder__](adv_recorder.md) — Register the events in a simulation and play it again.  
[__Rendering options__](adv_rendering_options.md) — From quality settings to no-render or off-screen modes.  
[__Synchrony and time-step__](adv_synchrony_timestep.md) — Client-server communication and simulation time.  
[__Benchmarking Performance__](adv_benchmarking.md) — Perform benchmarking using our prepared script.  
[__CARLA Agents__](adv_agents.md) — Agents scripts allow single vehicles to roam the map or drive to a set destination.  
[__Multi GPU__](adv_multigpu.md) — Set up the CARLA simulator to use multiple GPUs for processing.

## Traffic Simulation

[__Traffic Simulation Overview__](ts_traffic_simulation_overview.md) — An overview of the different options available to populate your scenes with traffic.  
[__Traffic Manager__](adv_traffic_manager.md) — Simulate urban traffic by setting vehicles to autopilot mode.  

## References

[__Recorder binary file format__](ref_recorder_binary_file_format.md) — Detailed explanation of the recorder file format.  
[__Sensors reference__](ref_sensors.md) — Everything about sensors and the data they retrieve.  

## Custom Maps

[__Digital Twin Tool__](adv_digital_twin.md) — A procedural map generation tool that uses OpenStreetMap data  
[__Overview of custom maps in CARLA__](tuto_M_custom_map_overview.md) — An overview of the process and options involved in adding a custom, standard sized map.   
[__Create a map in RoadRunner__](tuto_M_generate_map.md) — How to generate a customs, standard sized map in RoadRunner.  
[__Import map in CARLA package__](tuto_M_add_map_package.md) How to import a map in a CARLA package.  
[__Import map in CARLA source build__](tuto_M_add_map_source.md) — How to import a map in CARLA built from source.  
[__Alternative ways to import maps__](tuto_M_add_map_alternative.md) — Alternative methods to import maps.  
[__Manually prepare map package__](tuto_M_manual_map_package.md) — How to prepare a map for manual import.  
[__Customizing maps: Layered maps__](tuto_M_custom_layers.md) — How to create sub-layers in your custom map.  
[__Customizing maps: Traffic lights and signs__](tuto_M_custom_add_tl.md) — How to add traffic lights and signs to your custom map.  
[__Customizing maps: Road painter__](tuto_M_custom_road_painter.md) — How to use the road painter tool to change the appearance of the road.
[__Customizing Maps: Procedural Buildings__](tuto_M_custom_buildings.md) — Populate your custom map with buildings.  
[__Customizing maps: Weather and landscape__](tuto_M_custom_weather_landscape.md) — Create the weather profile for your custom map and populate the landscape.  
[__Generate pedestrian navigation__](tuto_M_generate_pedestrian_navigation.md) — Obtain the information needed for walkers to move around.  

## Large Maps

[__Large maps overview__](large_map_overview.md) — An explanation of how large maps work in CARLA.  
[__Create a Large Map in RoadRunner__](large_map_roadrunner.md) — How to create a large map in RoadRunner.  
[__Import/Package a Large Map__](large_map_import.md) — How to import a large map.  

