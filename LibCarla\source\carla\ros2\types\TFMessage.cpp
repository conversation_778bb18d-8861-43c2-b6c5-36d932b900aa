// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file TFMessage.cpp
 * This source file contains the definition of the described types in the IDL file.
 *
 * This file was generated by the tool gen.
 */

#ifdef _WIN32
// Remove linker warning LNK4221 on Visual Studio
namespace {
char dummy;
}  // namespace
#endif  // _WIN32

#include "TFMessage.h"
#include <fastcdr/Cdr.h>

#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

#include <utility>

#define geometry_msgs_msg_Vector3_max_cdr_typesize 24ULL;
#define geometry_msgs_msg_Transform_max_cdr_typesize 56ULL;
#define tf2_msgs_msg_TFMessage_max_cdr_typesize 58408ULL;
#define std_msgs_msg_Time_max_cdr_typesize 8ULL;
#define geometry_msgs_msg_TransformStamped_max_cdr_typesize 584ULL;
#define geometry_msgs_msg_Quaternion_max_cdr_typesize 32ULL;
#define std_msgs_msg_Header_max_cdr_typesize 268ULL;
#define geometry_msgs_msg_Vector3_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Transform_max_key_cdr_typesize 0ULL;
#define tf2_msgs_msg_TFMessage_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Time_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_TransformStamped_max_key_cdr_typesize 0ULL;
#define geometry_msgs_msg_Quaternion_max_key_cdr_typesize 0ULL;
#define std_msgs_msg_Header_max_key_cdr_typesize 0ULL;

tf2_msgs::msg::TFMessage::TFMessage()
{
}

tf2_msgs::msg::TFMessage::~TFMessage()
{
}

tf2_msgs::msg::TFMessage::TFMessage(
        const TFMessage& x)
{
    m_transforms = x.m_transforms;
}

tf2_msgs::msg::TFMessage::TFMessage(
        TFMessage&& x) noexcept
{
    m_transforms = std::move(x.m_transforms);
}

tf2_msgs::msg::TFMessage& tf2_msgs::msg::TFMessage::operator =(
        const TFMessage& x)
{
    m_transforms = x.m_transforms;

    return *this;
}

tf2_msgs::msg::TFMessage& tf2_msgs::msg::TFMessage::operator =(
        TFMessage&& x) noexcept
{
    m_transforms = std::move(x.m_transforms);

    return *this;
}

bool tf2_msgs::msg::TFMessage::operator ==(
        const TFMessage& x) const
{
    return (m_transforms == x.m_transforms);
}

bool tf2_msgs::msg::TFMessage::operator !=(
        const TFMessage& x) const
{
    return !(*this == x);
}

size_t tf2_msgs::msg::TFMessage::getMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return tf2_msgs_msg_TFMessage_max_cdr_typesize;
}

size_t tf2_msgs::msg::TFMessage::getCdrSerializedSize(
        const tf2_msgs::msg::TFMessage& data,
        size_t current_alignment)
{
    size_t initial_alignment = current_alignment;
    current_alignment += 4 + eprosima::fastcdr::Cdr::alignment(current_alignment, 4);

    for(size_t a = 0; a < data.transforms().size(); ++a)
    {
        current_alignment += geometry_msgs::msg::TransformStamped::getCdrSerializedSize(data.transforms().at(a), current_alignment);
    }

    return current_alignment - initial_alignment;
}

void tf2_msgs::msg::TFMessage::serialize(
        eprosima::fastcdr::Cdr& scdr) const
{
    scdr << m_transforms;
}

void tf2_msgs::msg::TFMessage::deserialize(
        eprosima::fastcdr::Cdr& dcdr)
{
    dcdr >> m_transforms;
}

/*!
 * @brief This function copies the value in member transforms
 * @param _transforms New value to be copied in member transforms
 */
void tf2_msgs::msg::TFMessage::transforms(
        const std::vector<geometry_msgs::msg::TransformStamped>& _transforms)
{
    m_transforms = _transforms;
}

/*!
 * @brief This function moves the value in member transforms
 * @param _transforms New value to be moved in member transforms
 */
void tf2_msgs::msg::TFMessage::transforms(
        std::vector<geometry_msgs::msg::TransformStamped>&& _transforms)
{
    m_transforms = std::move(_transforms);
}

/*!
 * @brief This function returns a constant reference to member transforms
 * @return Constant reference to member transforms
 */
const std::vector<geometry_msgs::msg::TransformStamped>& tf2_msgs::msg::TFMessage::transforms() const
{
    return m_transforms;
}

/*!
 * @brief This function returns a reference to member transforms
 * @return Reference to member transforms
 */
std::vector<geometry_msgs::msg::TransformStamped>& tf2_msgs::msg::TFMessage::transforms()
{
    return m_transforms;
}

size_t tf2_msgs::msg::TFMessage::getKeyMaxCdrSerializedSize(
        size_t current_alignment)
{
    static_cast<void>(current_alignment);
    return tf2_msgs_msg_TFMessage_max_key_cdr_typesize;
}

bool tf2_msgs::msg::TFMessage::isKeyDefined()
{
    return false;
}

void tf2_msgs::msg::TFMessage::serializeKey(
        eprosima::fastcdr::Cdr& scdr) const
{
    (void) scdr;
}
