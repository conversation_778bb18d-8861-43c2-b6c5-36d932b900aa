#!/usr/bin/env python3

"""
Simple sensor test to debug the multi-sensor capture issue.
This script tests if CARLA sensors work with NUREC scenarios.
"""

import carla
import argparse
import logging
import sys
import numpy as np
import time
from pathlib import Path
from typing import Optional, List

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("SensorTest")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class SimpleSensorTest:
    def __init__(self, world: carla.World):
        self.world = world
        self.rgb_sensor = None
        self.depth_sensor = None
        self.semantic_sensor = None
        self.rgb_count = 0
        self.depth_count = 0
        self.semantic_count = 0
        
    def setup_sensors(self, vehicle: carla.Actor):
        """Setup simple test sensors"""
        blueprint_library = self.world.get_blueprint_library()
        
        # Simple camera transform
        camera_transform = carla.Transform(
            carla.Location(x=1.5, z=2.4),
            carla.Rotation(pitch=0, yaw=0, roll=0)
        )
        
        # RGB Camera
        rgb_bp = blueprint_library.find('sensor.camera.rgb')
        rgb_bp.set_attribute('image_size_x', '800')
        rgb_bp.set_attribute('image_size_y', '600')
        rgb_bp.set_attribute('fov', '90')
        rgb_bp.set_attribute('sensor_tick', '0.0')  # Every frame
        
        self.rgb_sensor = self.world.spawn_actor(rgb_bp, camera_transform, attach_to=vehicle)
        self.rgb_sensor.listen(self.on_rgb_received)
        logger.info(f"RGB sensor created: {self.rgb_sensor.id}")
        
        # Depth Camera
        depth_bp = blueprint_library.find('sensor.camera.depth')
        depth_bp.set_attribute('image_size_x', '800')
        depth_bp.set_attribute('image_size_y', '600')
        depth_bp.set_attribute('fov', '90')
        depth_bp.set_attribute('sensor_tick', '0.0')
        
        self.depth_sensor = self.world.spawn_actor(depth_bp, camera_transform, attach_to=vehicle)
        self.depth_sensor.listen(self.on_depth_received)
        logger.info(f"Depth sensor created: {self.depth_sensor.id}")
        
        # Semantic Camera
        semantic_bp = blueprint_library.find('sensor.camera.semantic_segmentation')
        semantic_bp.set_attribute('image_size_x', '800')
        semantic_bp.set_attribute('image_size_y', '600')
        semantic_bp.set_attribute('fov', '90')
        semantic_bp.set_attribute('sensor_tick', '0.0')
        
        self.semantic_sensor = self.world.spawn_actor(semantic_bp, camera_transform, attach_to=vehicle)
        self.semantic_sensor.listen(self.on_semantic_received)
        logger.info(f"Semantic sensor created: {self.semantic_sensor.id}")
        
    def on_rgb_received(self, image):
        self.rgb_count += 1
        logger.info(f"RGB image received #{self.rgb_count}: frame {image.frame}")
        
    def on_depth_received(self, image):
        self.depth_count += 1
        logger.info(f"Depth image received #{self.depth_count}: frame {image.frame}")
        
    def on_semantic_received(self, image):
        self.semantic_count += 1
        logger.info(f"Semantic image received #{self.semantic_count}: frame {image.frame}")
        
    def get_counts(self):
        return self.rgb_count, self.depth_count, self.semantic_count
        
    def destroy(self):
        if self.rgb_sensor:
            self.rgb_sensor.destroy()
        if self.depth_sensor:
            self.depth_sensor.destroy()
        if self.semantic_sensor:
            self.semantic_sensor.destroy()

def main():
    parser = argparse.ArgumentParser(description="Test CARLA sensors with NUREC")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA host')
    parser.add_argument('--port', default=2000, type=int, help='CARLA port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC port')
    args = parser.parse_args()
    
    client = None
    sensor_test = None
    display = None
    
    try:
        # Connect to CARLA
        logger.info(f"Connecting to CARLA at {args.host}:{args.port}")
        client = carla.Client(args.host, args.port)
        client.set_timeout(60.0)
        world = client.get_world()
        logger.info(f"Connected to CARLA: {client.get_server_version()}")
        
        # Initialize sensor test
        sensor_test = SimpleSensorTest(world)
        
        # Load NUREC scenario
        logger.info(f"Loading NUREC scenario: {args.usdz_filename}")
        with NurecScenario(client, args.usdz_filename, port=args.nurec_port) as scenario:
            logger.info("NUREC scenario loaded successfully")
            
            # Add display
            display = PygameDisplay()
            scenario.add_camera(
                "camera_front_wide_120fov",
                lambda image: display.setImage(image, (1, 1), (0, 0)),
                framerate=30,
                resolution_ratio=0.25
            )
            
            # Get ego vehicle and setup sensors
            if EGO_TRACK_ID in scenario.actor_mapping:
                ego_vehicle = scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {ego_vehicle.id}")
                
                sensor_test.setup_sensors(ego_vehicle)
                logger.info("Sensors setup complete")
                
                # Start scenario
                scenario.start_replay()
                logger.info("Scenario replay started")
                
                # Test loop
                logger.info("Starting test loop...")
                for i in range(100):  # Test for 100 ticks
                    scenario.tick()
                    world.tick()  # Important: tick the world for sensors
                    
                    if i % 10 == 0:
                        rgb_count, depth_count, semantic_count = sensor_test.get_counts()
                        logger.info(f"Tick {i}: RGB={rgb_count}, Depth={depth_count}, Semantic={semantic_count}")
                    
                    time.sleep(0.05)  # 20 FPS
                
                # Final counts
                rgb_count, depth_count, semantic_count = sensor_test.get_counts()
                logger.info(f"Final counts: RGB={rgb_count}, Depth={depth_count}, Semantic={semantic_count}")
                
                if rgb_count > 0 and depth_count > 0 and semantic_count > 0:
                    logger.info("SUCCESS: All sensors are working!")
                else:
                    logger.warning("ISSUE: Some sensors are not receiving data")
                    
            else:
                logger.error("Ego vehicle not found in scenario")
                
    except Exception as e:
        logger.error(f"Error occurred: {e}", exc_info=True)
        handle_exception(e)
        
    finally:
        if sensor_test:
            sensor_test.destroy()
        if display:
            display.destroy()
        logger.info("Test completed")

if __name__ == '__main__':
    main()
