#!/usr/bin/env python3

"""
Cosmos Transfer Integration Example

This script demonstrates how to use the enhanced CARLA-NUREC-Cosmos Transfer pipeline
to generate diverse training datasets from NUREC scenarios.

Example Usage:
    # Basic usage with default settings
    python cosmos_transfer_example.py --usdz-filename path/to/scenario.usdz

    # Advanced usage with custom conditions and multi-sensor capture
    python cosmos_transfer_example.py \
        --usdz-filename path/to/scenario.usdz \
        --enable-multi-sensor \
        --export-cosmos-data \
        --environmental-conditions CLEAR_DAY HEAVY_RAIN SUNSET NIGHT FOG \
        --sequence-length 60 \
        --output-dir ./my_cosmos_data

    # Batch processing multiple scenarios
    python cosmos_transfer_example.py \
        --batch-process \
        --scenario-dir ./scenarios/ \
        --output-dir ./batch_cosmos_data

Requirements:
    - CARLA server running
    - NUREC integration components
    - PIL/Pillow for image processing
    - numpy for data processing
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add the parent directory to the path to import the enhanced script
sys.path.append(str(Path(__file__).parent))

# Import the enhanced pipeline components
from improved_load_usdz_scene_3 import (
    EnvironmentalCondition,
    MultiSensorManager,
    CosmosTransferManager,
    EnvironmentalController,
    generate_cosmos_transfer_summary,
    batch_process_multiple_scenarios
)

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("CosmosTransferExample")

def run_single_scenario_example():
    """
    Example: Process a single NUREC scenario with multiple environmental conditions
    """
    logger.info("=== Single Scenario Processing Example ===")
    
    # Example configuration
    scenario_path = "path/to/your/scenario.usdz"
    output_dir = "./cosmos_example_output"
    
    # Environmental conditions to generate
    conditions = [
        EnvironmentalCondition.CLEAR_DAY,
        EnvironmentalCondition.HEAVY_RAIN,
        EnvironmentalCondition.SUNSET,
        EnvironmentalCondition.NIGHT
    ]
    
    logger.info(f"Processing scenario: {scenario_path}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Environmental conditions: {[c.value for c in conditions]}")
    
    # This would call the main pipeline
    # For demonstration, we'll show the command that would be executed
    command = f"""
    python improved_load_usdz_scene_3.py \\
        --usdz-filename {scenario_path} \\
        --output-dir {output_dir} \\
        --enable-multi-sensor \\
        --export-cosmos-data \\
        --environmental-conditions {' '.join([c.name for c in conditions])} \\
        --sequence-length 30
    """
    
    logger.info("Command to execute:")
    logger.info(command)
    
    return output_dir

def run_batch_processing_example():
    """
    Example: Batch process multiple NUREC scenarios
    """
    logger.info("=== Batch Processing Example ===")
    
    # Example scenario directory structure
    scenario_dir = Path("./scenarios")
    output_dir = "./batch_cosmos_output"
    
    # Find all USDZ files in the scenario directory
    scenario_files = list(scenario_dir.glob("*.usdz"))
    
    logger.info(f"Found {len(scenario_files)} scenario files")
    
    # Environmental conditions for batch processing
    conditions = [
        EnvironmentalCondition.CLEAR_DAY,
        EnvironmentalCondition.OVERCAST,
        EnvironmentalCondition.HEAVY_RAIN,
        EnvironmentalCondition.SUNSET,
        EnvironmentalCondition.NIGHT
    ]
    
    # Process each scenario
    for scenario_file in scenario_files:
        logger.info(f"Processing: {scenario_file}")
        
        # Create scenario-specific output directory
        scenario_output = Path(output_dir) / scenario_file.stem
        
        command = f"""
        python improved_load_usdz_scene_3.py \\
            --usdz-filename {scenario_file} \\
            --output-dir {scenario_output} \\
            --enable-multi-sensor \\
            --export-cosmos-data \\
            --environmental-conditions {' '.join([c.name for c in conditions])} \\
            --sequence-length 45
        """
        
        logger.info(f"Command for {scenario_file.name}:")
        logger.info(command)
    
    return output_dir

def analyze_generated_data(output_dir: str):
    """
    Example: Analyze the generated Cosmos Transfer data
    """
    logger.info("=== Data Analysis Example ===")
    
    # Generate summary report
    summary = generate_cosmos_transfer_summary(output_dir)
    
    logger.info("Generated Data Summary:")
    logger.info(f"  Total batches: {summary['total_batches']}")
    logger.info(f"  Total frames: {summary['total_frames']}")
    logger.info(f"  Environmental conditions: {summary['environmental_conditions']}")
    
    # Analyze data distribution
    if summary['environmental_conditions']:
        logger.info("\nData Distribution by Environmental Condition:")
        for condition, count in summary['environmental_conditions'].items():
            percentage = (count / summary['total_batches']) * 100
            logger.info(f"  {condition}: {count} batches ({percentage:.1f}%)")
    
    # Show recent batches
    if summary['batch_details']:
        logger.info(f"\nRecent Batches (showing last 5):")
        for batch in summary['batch_details'][-5:]:
            logger.info(f"  {batch['batch_id']}: {batch['condition']} ({batch['frames']} frames)")
    
    return summary

def create_custom_prompt_example():
    """
    Example: Create custom prompts for specific use cases
    """
    logger.info("=== Custom Prompt Example ===")
    
    # Example custom prompts for different scenarios
    custom_prompts = {
        "autonomous_driving": "Transform this driving scene for autonomous vehicle training with enhanced road markings and clear signage",
        "delivery_robot": "Transform this scene to a pedestrian-friendly environment suitable for delivery robot navigation",
        "construction_zone": "Transform this driving scene to include construction zones with orange cones and work vehicles",
        "school_zone": "Transform this scene to a school zone with children crossing signs and reduced speed indicators",
        "parking_lot": "Transform this scene to a busy parking lot environment with parked cars and pedestrians"
    }
    
    logger.info("Custom prompt examples:")
    for scenario, prompt in custom_prompts.items():
        logger.info(f"  {scenario}: {prompt}")
    
    # Show how to use custom prompts
    logger.info("\nTo use custom prompts, modify the CosmosTransferManager.create_batch() call:")
    logger.info("batch = cosmos_manager.create_batch(")
    logger.info("    sensor_data=sensor_data,")
    logger.info("    environmental_condition=condition,")
    logger.info("    sequence_length=30,")
    logger.info("    custom_prompt='Your custom prompt here'")
    logger.info(")")

def main():
    """
    Main example runner
    """
    parser = argparse.ArgumentParser(description="Cosmos Transfer Integration Examples")
    parser.add_argument('--example', choices=['single', 'batch', 'analysis', 'custom-prompts', 'all'],
                       default='all', help='Which example to run')
    parser.add_argument('--output-dir', default='./cosmos_example_output',
                       help='Output directory for examples')
    
    args = parser.parse_args()
    
    logger.info("Starting Cosmos Transfer Integration Examples")
    
    if args.example in ['single', 'all']:
        output_dir = run_single_scenario_example()
        
    if args.example in ['batch', 'all']:
        batch_output_dir = run_batch_processing_example()
        
    if args.example in ['analysis', 'all']:
        # Use the output directory from previous examples or the specified one
        analyze_generated_data(args.output_dir)
        
    if args.example in ['custom-prompts', 'all']:
        create_custom_prompt_example()
    
    logger.info("Examples completed successfully!")
    logger.info("\nNext Steps:")
    logger.info("1. Ensure CARLA server is running")
    logger.info("2. Have your NUREC scenario files ready")
    logger.info("3. Run the actual pipeline with your data")
    logger.info("4. Submit the generated data to Cosmos Transfer for processing")

if __name__ == '__main__':
    main()
