# loads roads and geometry data from usdz , in pygame window shows the render output
# SPDX-FileCopyrightText: © 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# SPDX-License-Identifier: MIT

"""
NUREC USDZ Scene Loader for CARLA using NurecScenario (Enhanced Logging and Visualization)

This script now includes a Pygame window to display the rendered output from a NUREC camera.
"""
import carla
import argparse
import logging
import sys
from typing import Optional

# --- Step 1: Configure Detailed Logging ---
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("Nurec_Verbose_Loader")
logger.info("--- Starting NUREC Scenario Loader Script ---")


# --- Step 2: Import NUREC components ---
try:
    logger.info("Attempting to import NurecScenario and handle_exception...")
    from nurec_integration import NurecScenario
    from utils import handle_exception
    # Import PygameDisplay for visualization
    from pygame_display import PygameDisplay
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    logger.error("Please ensure you have run 'pip install -r requirements.txt' and compiled gRPC protos.")
    sys.exit(1)

# --- Function to add camera and display ---
def add_camera_and_display(scenario: NurecScenario) -> PygameDisplay:
    """
    Creates a Pygame display and adds a NUREC camera to render to it.
    """
    pygame_display = PygameDisplay()
    
    # Add a NUREC camera. We'll use the 'camera_front_wide_120fov' as an example.
    # The lambda function tells the scenario to send the rendered image to our display.
    scenario.add_camera(
        "camera_front_wide_120fov", 
        lambda image: pygame_display.setImage(image, (1, 1), (0, 0)), 
        framerate=30, 
        resolution_ratio=0.25 # Lower resolution for better performance
    )
    
    logger.info("Pygame display and NUREC camera have been added to the scenario.")
    return pygame_display

def main() -> None:
    """
    Main function with detailed logging for each step.
    """
    argparser = argparse.ArgumentParser(description=__doc__)
    argparser.add_argument('--host', default='127.0.0.1', help='IP of the host server')
    argparser.add_argument('-p', '--port', default=2000, type=int, help='TCP port to listen to')
    argparser.add_argument('-np', '--nurec-port', default=46435, type=int, help='NUREC port')
    argparser.add_argument('-u', '--usdz-filename', required=True, help='Path to the USDZ file')
    args = argparser.parse_args()

    logger.info(f"Script arguments: {vars(args)}")

    client = None
    display: Optional[PygameDisplay] = None
    try:
        # --- Step 3: Connect to CARLA Client ---
        logger.info(f"Attempting to connect to CARLA at {args.host}:{args.port}...")
        client = carla.Client(args.host, args.port)
        client.set_timeout(60.0)
        server_version = client.get_server_version()
        logger.info(f"Successfully connected to CARLA. Server version: {server_version}")

        # --- Step 4: Initialize NurecScenario ---
        logger.info(f"Initializing NurecScenario with file: {args.usdz_filename} on port {args.nurec_port}")
        logger.info("This step involves starting the NuRec service and loading data. It may take a moment...")

        with NurecScenario(client, args.usdz_filename, port=args.nurec_port) as scenario:
            logger.info("--- NurecScenario context entered SUCCESSFULLY! ---")
            
            # --- ADD VISUALIZATION ---
            display = add_camera_and_display(scenario)
            
            logger.info("Scenario loaded and ready.")
            
            # --- Step 5: Start Replay ---
            logger.info("Attempting to start scenario replay...")
            scenario.start_replay()
            logger.info("Replay started successfully.")

            # --- Step 6: Simulation Loop ---
            logger.info("Entering main simulation loop. Ticking scenario...")
            tick_count = 0
            while not scenario.is_done():
                scenario.tick()
                if tick_count % 100 == 0:
                     logger.info(f"Scenario tick {tick_count} successful.")
                tick_count += 1

            logger.info(f"Replay finished after {tick_count} ticks.")

    except RuntimeError as e:
        logger.error(f"A CARLA Runtime Error occurred: {e}", exc_info=True)
        logger.error("This often means the CARLA server is not running or is unresponsive.")
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt detected, exiting gracefully.")
    except Exception as e:
        logger.error(f"An unhandled exception occurred: {e}", exc_info=True)
        handle_exception(e)
    finally:
        # --- Clean up the display window ---
        if display is not None:
            logger.info("Destroying Pygame display.")
            display.destroy()
        logger.info("--- Script execution finished. ---")


if __name__ == '__main__':
    main()