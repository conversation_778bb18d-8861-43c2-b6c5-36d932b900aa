"""
NUREC Manual Driving - Fixed Display Version

This version fixes the OpenGL context issues and provides a better driving experience
similar to CARLA's manual control example.
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time
from typing import Optional
import threading
import queue
import cv2

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("NUREC_Drive")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
except ImportError as e:
    logger.error(f"Import failed: {e}")
    sys.exit(1)

class ImprovedDisplay:
    """Improved display class that handles OpenGL context issues"""
    
    def __init__(self, width=1024, height=768):
        # Initialize pygame with better error handling
        import os
        
        # Try different SDL video drivers
        video_drivers = ['x11', 'wayland', 'directfb', 'fbcon']
        pygame_initialized = False
        
        for driver in video_drivers:
            try:
                os.environ['SDL_VIDEODRIVER'] = driver
                pygame.init()
                
                # Try to create the display surface
                self.screen = pygame.display.set_mode(
                    (width, height), 
                    pygame.SWSURFACE | pygame.DOUBLEBUF
                )
                pygame.display.set_caption("NUREC Manual Driving")
                pygame_initialized = True
                logger.info(f"✅ Pygame initialized with driver: {driver}")
                break
                
            except Exception as e:
                logger.debug(f"Failed to initialize with {driver}: {e}")
                continue
        
        if not pygame_initialized:
            logger.error("❌ Failed to initialize pygame with any video driver")
            raise RuntimeError("Could not initialize pygame display")
        
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.frame_count = 0
        self.width = width
        self.height = height
        
        # Image queue for thread-safe updates
        self.image_queue = queue.Queue(maxsize=2)
        self.current_image = None
        
        # HUD info
        self.speed = 0.0
        self.gear = 0
        self.controls = {'throttle': 0, 'brake': 0, 'steer': 0, 'handbrake': False}
        
    def update_vehicle_info(self, vehicle):
        """Update vehicle information for HUD"""
        if vehicle is not None:
            try:
                velocity = vehicle.get_velocity()
                self.speed = 3.6 * (velocity.x**2 + velocity.y**2 + velocity.z**2)**0.5  # km/h
                
                control = vehicle.get_control()
                self.controls = {
                    'throttle': control.throttle,
                    'brake': control.brake,
                    'steer': control.steer,
                    'handbrake': control.hand_brake
                }
            except:
                pass
        
    def show_image(self, image):
        """Add image to queue for display"""
        if image is not None:
            try:
                # Don't block if queue is full, just replace
                try:
                    self.image_queue.get_nowait()
                except queue.Empty:
                    pass
                self.image_queue.put(image, block=False)
            except queue.Full:
                pass
                
    def render_hud(self, surface):
        """Render HUD information"""
        y_offset = 10
        
        # Speed
        speed_text = f"Speed: {self.speed:.1f} km/h"
        speed_surface = self.font.render(speed_text, True, (255, 255, 255))
        surface.blit(speed_surface, (10, y_offset))
        y_offset += 25
        
        # Controls
        throttle_text = f"Throttle: {self.controls['throttle']:.2f}"
        throttle_surface = self.small_font.render(throttle_text, True, (0, 255, 0))
        surface.blit(throttle_surface, (10, y_offset))
        y_offset += 20
        
        brake_text = f"Brake: {self.controls['brake']:.2f}"
        brake_surface = self.small_font.render(brake_text, True, (255, 100, 100))
        surface.blit(brake_surface, (10, y_offset))
        y_offset += 20
        
        steer_text = f"Steering: {self.controls['steer']:.2f}"
        steer_surface = self.small_font.render(steer_text, True, (100, 100, 255))
        surface.blit(steer_surface, (10, y_offset))
        y_offset += 20
        
        if self.controls['handbrake']:
            handbrake_surface = self.small_font.render("HANDBRAKE", True, (255, 0, 0))
            surface.blit(handbrake_surface, (10, y_offset))
        
        # Frame counter
        frame_text = f"Frame: {self.frame_count}"
        frame_surface = self.small_font.render(frame_text, True, (200, 200, 200))
        surface.blit(frame_surface, (self.width - 100, 10))
        
        # Instructions
        instructions = [
            "WASD/Arrows: Drive",
            "SPACE: Handbrake", 
            "ESC: Quit",
            "R: Reset to original path"
        ]
        
        for i, instruction in enumerate(instructions):
            inst_surface = self.small_font.render(instruction, True, (200, 200, 200))
            surface.blit(inst_surface, (self.width - 200, 40 + i * 18))
        
    def update(self):
        """Update display with latest image"""
        try:
            # Get latest image from queue
            try:
                self.current_image = self.image_queue.get_nowait()
                self.frame_count += 1
            except queue.Empty:
                pass
            
            # Clear screen
            self.screen.fill((0, 0, 0))
            
            if self.current_image is not None:
                try:
                    # Process image
                    img = self.current_image
                    
                    # Convert to RGB if needed
                    if len(img.shape) == 3 and img.shape[2] >= 3:
                        img = img[:, :, :3]  # Take only RGB channels
                    
                    # Ensure correct data type
                    if img.dtype != np.uint8:
                        if img.max() <= 1.0:
                            img = (img * 255).astype(np.uint8)
                        else:
                            img = np.clip(img, 0, 255).astype(np.uint8)
                    
                    # Create pygame surface
                    h, w = img.shape[:2]
                    
                    # Use OpenCV to resize if needed (more reliable than pygame)
                    target_h = min(h, self.height - 100)  # Leave space for HUD
                    target_w = min(w, self.width)
                    
                    if h != target_h or w != target_w:
                        img = cv2.resize(img, (target_w, target_h))
                        h, w = target_h, target_w
                    
                    # Convert to pygame surface
                    try:
                        # Convert BGR to RGB if needed (OpenCV uses BGR)
                        if len(img.shape) == 3:
                            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        else:
                            img_rgb = img
                            
                        surface = pygame.surfarray.make_surface(img_rgb.swapaxes(0, 1))
                        
                        # Center the image
                        x = (self.width - w) // 2
                        y = max(10, (self.height - h - 100) // 2)
                        
                        self.screen.blit(surface, (x, y))
                        
                    except Exception as e:
                        logger.debug(f"Surface creation error: {e}")
                        # Fallback: show a colored rectangle indicating we're receiving data
                        pygame.draw.rect(self.screen, (0, 100, 0), 
                                       (self.width//4, self.height//4, self.width//2, self.height//2))
                        text = self.font.render("Receiving NUREC data...", True, (255, 255, 255))
                        self.screen.blit(text, (self.width//2 - 100, self.height//2))
                        
                except Exception as e:
                    logger.debug(f"Image processing error: {e}")
                    # Show error indicator
                    pygame.draw.rect(self.screen, (100, 0, 0), 
                                   (self.width//4, self.height//4, self.width//2, self.height//2))
                    text = self.font.render("Display Error", True, (255, 255, 255))
                    self.screen.blit(text, (self.width//2 - 60, self.height//2))
            else:
                # Show waiting message
                text = self.font.render("Waiting for NUREC camera...", True, (255, 255, 255))
                self.screen.blit(text, (self.width//2 - 120, self.height//2))
            
            # Always render HUD
            self.render_hud(self.screen)
            
            # Update display
            pygame.display.flip()
            
        except Exception as e:
            logger.error(f"Display update error: {e}")

class VehicleController:
    """Enhanced vehicle controller with smoother controls"""
    
    def __init__(self):
        self.throttle = 0.0
        self.brake = 0.0
        self.steer = 0.0
        self.handbrake = False
        
        # Control smoothing
        self.throttle_increment = 0.05
        self.brake_increment = 0.1
        self.steer_increment = 0.05
        
    def update(self, keys_pressed):
        """Update controls based on pressed keys"""
        # Reset controls
        target_throttle = 0.0
        target_brake = 0.0
        target_steer = 0.0
        self.handbrake = False
        
        # Throttle
        if pygame.K_w in keys_pressed or pygame.K_UP in keys_pressed:
            target_throttle = 0.8
        
        # Brake/Reverse
        if pygame.K_s in keys_pressed or pygame.K_DOWN in keys_pressed:
            target_brake = 0.8
            
        # Steering
        if pygame.K_a in keys_pressed or pygame.K_LEFT in keys_pressed:
            target_steer = -0.6
        if pygame.K_d in keys_pressed or pygame.K_RIGHT in keys_pressed:
            target_steer = 0.6
            
        # Handbrake
        if pygame.K_SPACE in keys_pressed:
            self.handbrake = True
            
        # Smooth control changes
        self.throttle = self._smooth_change(self.throttle, target_throttle, self.throttle_increment)
        self.brake = self._smooth_change(self.brake, target_brake, self.brake_increment)
        self.steer = self._smooth_change(self.steer, target_steer, self.steer_increment)
        
    def _smooth_change(self, current, target, increment):
        """Smoothly change a control value"""
        if abs(target - current) < increment:
            return target
        elif target > current:
            return current + increment
        else:
            return current - increment
            
    def get_control(self):
        """Get CARLA VehicleControl object"""
        control = carla.VehicleControl()
        control.throttle = max(0, min(1, self.throttle))
        control.brake = max(0, min(1, self.brake))
        control.steer = max(-1, min(1, self.steer))
        control.hand_brake = self.handbrake
        return control

def main():
    parser = argparse.ArgumentParser(description="NUREC Manual Driving")
    parser.add_argument('-u', '--usdz-filename', required=True, help="Path to USDZ file")
    parser.add_argument('--host', default='127.0.0.1', help="CARLA host")
    parser.add_argument('-p', '--port', default=2000, type=int, help="CARLA port")
    parser.add_argument('-np', '--nurec-port', default=46435, type=int, help="NUREC port")
    parser.add_argument('--fps', default=20, type=int, help="Target FPS")
    parser.add_argument('--camera-fps', default=10, type=int, help="Camera FPS")
    parser.add_argument('--resolution', default=0.5, type=float, help="Camera resolution ratio")
    args = parser.parse_args()
    
    # Connect to CARLA
    client = carla.Client(args.host, args.port)
    client.set_timeout(60.0)
    
    # Initialize display
    try:
        display = ImprovedDisplay(1280, 720)
        logger.info("✅ Display initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize display: {e}")
        return
    
    # Initialize controller
    controller = VehicleController()
    
    try:
        with NurecScenario(client, args.usdz_filename, port=args.nurec_port, fps=args.fps) as scenario:
            logger.info("✅ NUREC Scenario loaded")
            
            # Start scenario replay
            scenario.start_replay(synchronous_mode=True)
            logger.info("✅ Scenario replay started")
            
            # Add ego vehicle
            ego = scenario.add_ego("vehicle.tesla.model3", enable_physics=True, move_spectator=True)
            logger.info("✅ Ego vehicle added")
            
            # Get available cameras
            cameras = scenario.get_available_cameras()
            logger.info(f"Available cameras: {cameras}")
            
            # Use the first available camera (or find a preferred one)
            camera_name = cameras[0]
            for cam in cameras:
                if any(keyword in cam.lower() for keyword in ['front', 'wide', 'main']):
                    camera_name = cam
                    break
            
            logger.info(f"Using camera: {camera_name}")
            
            # Add camera with callback
            def camera_callback(img):
                display.show_image(img)
            
            scenario.add_camera(
                camera_name, 
                camera_callback, 
                framerate=args.camera_fps, 
                resolution_ratio=args.resolution
            )
            logger.info("✅ Camera added")
            
            # Initialize pygame for controls
            clock = pygame.time.Clock()
            
            print("\n" + "="*60)
            print("🚗 NUREC MANUAL DRIVING")
            print("  W/↑         : Throttle")
            print("  S/↓         : Brake/Reverse") 
            print("  A/←, D/→    : Steering")
            print("  SPACE       : Handbrake")
            print("  R           : Reset to original path")
            print("  ESC/Q       : Quit")
            print("="*60)
            
            # Warm up
            for _ in range(10):
                scenario.tick()
                display.update()
            
            # Main driving loop
            running = True
            keys_pressed = set()
            
            logger.info("🚗 Starting manual driving session...")
            
            while running and scenario.is_running():
                # Handle pygame events
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        running = False
                    elif event.type == pygame.KEYDOWN:
                        keys_pressed.add(event.key)
                        if event.key in [pygame.K_ESCAPE, pygame.K_q]:
                            running = False
                        elif event.key == pygame.K_r:
                            # Reset to original path (you can implement this feature)
                            logger.info("Reset requested (feature not implemented)")
                    elif event.type == pygame.KEYUP:
                        keys_pressed.discard(event.key)
                
                # Update vehicle controls
                controller.update(keys_pressed)
                ego.apply_control(controller.get_control())
                
                # Update display info
                display.update_vehicle_info(ego)
                
                # Tick scenario (progress time and render)
                scenario.tick()
                
                # Update display
                display.update()
                
                # Maintain target FPS
                clock.tick(args.fps)
                
            logger.info("🏁 Driving session ended")
            
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error during execution: {e}")
        handle_exception(e)
        import traceback
        traceback.print_exc()
    finally:
        try:
            pygame.quit()
            logger.info("✅ Pygame cleaned up")
        except:
            pass

if __name__ == '__main__':
    main()