#!/usr/bin/env python3

"""
Physics-Fixed Manual Control in NUREC Reconstructed Environment

This version fixes common physics issues:
- Vehicle falling through road
- Unstable vehicle behavior
- Collision detection problems
- Physics synchronization issues

Controls:
    W/S         : Throttle/Brake
    A/D         : Steer left/right
    Q           : Toggle reverse
    Space       : Hand brake
    P           : Toggle autopilot
    R           : Restart vehicle
    T           : Teleport to safe position
    Esc         : Quit

Usage:
    python3 manual_control_nurec_physics_fixed.py --usdz-filename /path/to/scenario.usdz
"""

import carla
import argparse
import logging
import sys
import pygame
import numpy as np
import time
import math
from typing import Optional

# Configure logging
logging.basicConfig(
    format="%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger("ManualControlNurecPhysicsFixed")

try:
    from nurec_integration import NurecScenario
    from utils import handle_exception
    from pygame_display import PygameDisplay
    from constants import EGO_TRACK_ID
    logger.info("Successfully imported NUREC components.")
except ImportError as e:
    logger.error(f"Failed to import a required module: {e}")
    sys.exit(1)

class PhysicsFixedManualControlNurec:
    """
    Physics-fixed manual control with proper vehicle handling
    """
    
    def __init__(self, args):
        self.args = args
        self.client = None
        self.world = None
        self.scenario = None
        self.display = None
        self.vehicle = None
        
        # Control state
        self.control = carla.VehicleControl()
        self.autopilot_enabled = False
        
        # Physics monitoring
        self.last_valid_transform = None
        self.stuck_counter = 0
        self.fall_detection_threshold = -10.0  # Z coordinate threshold
        self.recovery_cooldown = 0  # Prevent rapid recovery attempts
        self.recovery_cooldown_time = 60  # 3 seconds at 20 FPS
        
        # Initialize pygame for input handling only
        pygame.init()
        pygame.display.set_mode((1, 1))
        self.clock = pygame.time.Clock()
        
        # Control parameters - tuned for stability
        self.throttle_increment = 0.015
        self.brake_increment = 0.25
        self.steer_increment = 0.012
        self.steer_decay = 0.92
        self.max_steer_speed = 0.8  # Reduce max steering for stability
        
        logger.info("Physics-fixed manual control initialized")
        
    def connect_to_carla(self):
        """Connect to CARLA server"""
        try:
            logger.info(f"Connecting to CARLA at {self.args.host}:{self.args.port}")
            self.client = carla.Client(self.args.host, self.args.port)
            self.client.set_timeout(60.0)
            self.world = self.client.get_world()
            
            # Set synchronous mode for better physics stability
            settings = self.world.get_settings()
            settings.synchronous_mode = True
            settings.fixed_delta_seconds = 0.05  # 20 FPS for stable physics
            self.world.apply_settings(settings)
            
            logger.info(f"Connected to CARLA: {self.client.get_server_version()}")
            logger.info("Synchronous mode enabled for stable physics")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to CARLA: {e}")
            return False
    
    def load_nurec_scenario(self):
        """Load NUREC scenario"""
        try:
            logger.info(f"Loading NUREC scenario: {self.args.usdz_filename}")
            self.scenario = NurecScenario(
                self.client, 
                self.args.usdz_filename, 
                port=self.args.nurec_port
            )
            self.scenario.__enter__()
            logger.info("NUREC scenario loaded successfully")
            
            # Setup NUREC display
            self.display = PygameDisplay()
            self.scenario.add_camera(
                "camera_front_wide_120fov",
                self.on_nurec_image,
                framerate=20,  # Match physics framerate
                resolution_ratio=0.75
            )
            
            logger.info("NUREC camera setup complete")
            return True
        except Exception as e:
            logger.error(f"Failed to load NUREC scenario: {e}")
            return False
    
    def on_nurec_image(self, image):
        """Handle NUREC image"""
        self.display.setImage(image, (1, 1), (0, 0))
    
    def setup_vehicle(self):
        """Setup the ego vehicle with proper physics"""
        try:
            # Get the ego vehicle from NUREC scenario
            if EGO_TRACK_ID in self.scenario.actor_mapping:
                self.vehicle = self.scenario.actor_mapping[EGO_TRACK_ID].actor_inst
                logger.info(f"Found ego vehicle: {self.vehicle.id}")
                
                # Configure vehicle physics for stability
                self.configure_vehicle_physics()
                
                # Enable physics for manual control
                self.scenario.actor_mapping[EGO_TRACK_ID].set_physics(True, self.scenario.get_sim_time())
                logger.info("Vehicle physics enabled for manual control")
                
                # Get initial position and ensure it's valid
                initial_transform = self.vehicle.get_transform()
                self.last_valid_transform = initial_transform
                logger.info(f"Initial vehicle position: ({initial_transform.location.x:.1f}, {initial_transform.location.y:.1f}, {initial_transform.location.z:.1f})")
                
                # Ensure vehicle is on the ground
                self.ensure_vehicle_on_ground()
                
                # Set initial control
                self.vehicle.apply_control(self.control)
                
                # Start the scenario
                self.scenario.start_replay()
                logger.info("Scenario replay started")
                
                return True
            else:
                logger.error("Ego vehicle not found in scenario")
                return False
        except Exception as e:
            logger.error(f"Failed to setup vehicle: {e}")
            return False
    
    def configure_vehicle_physics(self):
        """Configure vehicle physics for better stability"""
        try:
            physics_control = self.vehicle.get_physics_control()
            
            # Improve wheel physics
            for wheel in physics_control.wheels:
                wheel.tire_friction = 3.5  # Increase tire friction
                wheel.damping_rate = 1.5   # Increase damping
                wheel.max_steer_angle = 35.0  # Limit steering angle
                wheel.radius = max(wheel.radius, 0.3)  # Ensure reasonable wheel size
            
            # Improve vehicle stability
            physics_control.use_gear_autobox = True
            physics_control.gear_switch_time = 0.5
            physics_control.clutch_strength = 10.0
            physics_control.final_ratio = 4.0
            physics_control.mass = max(physics_control.mass, 1500.0)  # Ensure reasonable mass
            
            # Improve center of mass for stability
            physics_control.center_of_mass.z = -0.5  # Lower center of mass
            
            # Apply physics control
            self.vehicle.apply_physics_control(physics_control)
            logger.info("Vehicle physics configured for stability")
            
        except Exception as e:
            logger.warning(f"Failed to configure vehicle physics: {e}")
    
    def ensure_vehicle_on_ground(self):
        """Ensure vehicle is properly positioned on the ground"""
        try:
            current_transform = self.vehicle.get_transform()
            
            # Raycast down to find ground level
            start_location = current_transform.location
            start_location.z += 5.0  # Start raycast from above vehicle
            
            end_location = carla.Location(
                start_location.x,
                start_location.y,
                start_location.z - 20.0  # Cast ray 20 meters down
            )
            
            # Perform raycast to find ground
            world_snapshot = self.world.get_snapshot()
            if world_snapshot:
                # Simple approach: adjust Z position slightly above current position
                adjusted_transform = carla.Transform(
                    carla.Location(
                        current_transform.location.x,
                        current_transform.location.y,
                        current_transform.location.z + 1.0  # Lift slightly above ground
                    ),
                    current_transform.rotation
                )
                
                self.vehicle.set_transform(adjusted_transform)
                logger.info("Vehicle position adjusted for ground contact")
                
        except Exception as e:
            logger.warning(f"Failed to adjust vehicle position: {e}")
    
    def handle_input(self):
        """Handle keyboard input for vehicle control"""
        # Process pygame events for key presses
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_r:
                    self.restart_vehicle()
                elif event.key == pygame.K_t:
                    self.teleport_to_safe_position()
                elif event.key == pygame.K_p:
                    self.toggle_autopilot()
                elif event.key == pygame.K_q:
                    # Toggle reverse
                    self.control.gear = 1 if self.control.reverse else -1
                    logger.info(f"Gear: {'Reverse' if self.control.gear < 0 else 'Forward'}")
        
        # Get current key states for continuous input
        keys = pygame.key.get_pressed()
        
        # Only apply manual control if autopilot is off
        if not self.autopilot_enabled:
            # Throttle/Brake with smoother control
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                self.control.throttle = min(self.control.throttle + self.throttle_increment, 1.0)
            else:
                self.control.throttle = max(self.control.throttle - 0.02, 0.0)  # Gradual release
            
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                self.control.brake = min(self.control.brake + self.brake_increment, 1.0)
            else:
                self.control.brake = max(self.control.brake - 0.05, 0.0)  # Gradual release
            
            # Steering with speed-dependent sensitivity
            current_speed = self.get_vehicle_speed()
            speed_factor = max(0.3, 1.0 - (current_speed / 50.0))  # Reduce sensitivity at high speed
            
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                steer_increment = self.steer_increment * speed_factor
                self.control.steer = max(self.control.steer - steer_increment, -self.max_steer_speed)
            elif keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                steer_increment = self.steer_increment * speed_factor
                self.control.steer = min(self.control.steer + steer_increment, self.max_steer_speed)
            else:
                # Gradual return to center
                self.control.steer *= self.steer_decay
                if abs(self.control.steer) < 0.01:
                    self.control.steer = 0.0
            
            # Hand brake
            self.control.hand_brake = keys[pygame.K_SPACE]
            
            # Set reverse flag
            self.control.reverse = self.control.gear < 0
            
            # Apply control to vehicle
            if self.vehicle:
                self.vehicle.apply_control(self.control)
        
        return True
    
    def get_vehicle_speed(self):
        """Get current vehicle speed in km/h"""
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            return 3.6 * math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
        return 0.0
    
    def check_vehicle_physics(self):
        """Check and fix vehicle physics issues"""
        if not self.vehicle:
            return

        # Update recovery cooldown
        if self.recovery_cooldown > 0:
            self.recovery_cooldown -= 1
            return  # Skip physics checks during cooldown

        current_transform = self.vehicle.get_transform()
        current_location = current_transform.location

        # Check if vehicle has fallen through the world
        if current_location.z < self.fall_detection_threshold:
            logger.warning("Vehicle fell through world, teleporting to safe position")
            self.teleport_to_safe_position()
            self.recovery_cooldown = self.recovery_cooldown_time  # Set cooldown
            return
        
        # Check if vehicle is stuck (not moving for extended period)
        current_speed = self.get_vehicle_speed()
        if current_speed < 0.1 and (self.control.throttle > 0.1 or self.control.brake > 0.1):
            self.stuck_counter += 1
            if self.stuck_counter > 100:  # Stuck for ~5 seconds at 20 FPS
                logger.warning("Vehicle appears stuck, attempting recovery")
                self.recover_stuck_vehicle()
                self.stuck_counter = 0
                self.recovery_cooldown = self.recovery_cooldown_time  # Set cooldown
        else:
            self.stuck_counter = 0
            # Update last valid position
            if current_location.z > self.fall_detection_threshold:
                self.last_valid_transform = current_transform
    
    def recover_stuck_vehicle(self):
        """Attempt to recover a stuck vehicle"""
        if not self.vehicle or not self.last_valid_transform:
            return
        
        try:
            # Lift vehicle slightly and reset physics
            recovery_transform = carla.Transform(
                carla.Location(
                    self.last_valid_transform.location.x,
                    self.last_valid_transform.location.y,
                    self.last_valid_transform.location.z + 2.0
                ),
                self.last_valid_transform.rotation
            )
            
            self.vehicle.set_transform(recovery_transform)
            
            # Reset control
            self.control = carla.VehicleControl()
            self.vehicle.apply_control(self.control)
            
            logger.info("Vehicle recovery attempted")
            
        except Exception as e:
            logger.warning(f"Failed to recover stuck vehicle: {e}")
    
    def teleport_to_safe_position(self):
        """Teleport vehicle to a safe position"""
        if not self.vehicle:
            return

        try:
            # First try to use CARLA spawn points
            spawn_points = self.world.get_map().get_spawn_points()
            if spawn_points:
                import random
                safe_spawn = random.choice(spawn_points)
                # Lift the spawn point to ensure it's above ground
                safe_transform = carla.Transform(
                    carla.Location(
                        safe_spawn.location.x,
                        safe_spawn.location.y,
                        safe_spawn.location.z + 2.0
                    ),
                    safe_spawn.rotation
                )

                self.vehicle.set_transform(safe_transform)
                self.control = carla.VehicleControl()
                self.vehicle.apply_control(self.control)
                self.last_valid_transform = safe_transform

                logger.info(f"Vehicle teleported to CARLA spawn point: ({safe_transform.location.x:.1f}, {safe_transform.location.y:.1f}, {safe_transform.location.z:.1f})")
                return

            # Fallback: use last valid position if available
            if self.last_valid_transform and self.last_valid_transform.location.z > self.fall_detection_threshold:
                safe_transform = carla.Transform(
                    carla.Location(
                        self.last_valid_transform.location.x,
                        self.last_valid_transform.location.y,
                        self.last_valid_transform.location.z + 5.0
                    ),
                    self.last_valid_transform.rotation
                )

                self.vehicle.set_transform(safe_transform)
                self.control = carla.VehicleControl()
                self.vehicle.apply_control(self.control)

                logger.info("Vehicle teleported to last valid position (lifted)")
                return

            # Last resort: use a high position above origin
            safe_transform = carla.Transform(
                carla.Location(0.0, 0.0, 50.0),  # High above origin
                carla.Rotation(0.0, 0.0, 0.0)
            )

            self.vehicle.set_transform(safe_transform)
            self.control = carla.VehicleControl()
            self.vehicle.apply_control(self.control)
            self.last_valid_transform = safe_transform

            logger.info("Vehicle teleported to high position above origin")

        except Exception as e:
            logger.warning(f"Failed to teleport vehicle: {e}")
    
    def toggle_autopilot(self):
        """Toggle autopilot mode"""
        self.autopilot_enabled = not self.autopilot_enabled
        if self.vehicle:
            self.vehicle.set_autopilot(self.autopilot_enabled)
        logger.info(f"Autopilot {'ON' if self.autopilot_enabled else 'OFF'}")
    
    def restart_vehicle(self):
        """Restart vehicle at original position"""
        logger.info("Restarting vehicle...")
        self.teleport_to_safe_position()
    
    def print_status(self):
        """Print current status to console"""
        if self.vehicle:
            velocity = self.vehicle.get_velocity()
            speed_kmh = 3.6 * math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
            transform = self.vehicle.get_transform()
            
            status = (
                f"Speed: {speed_kmh:6.1f} km/h | "
                f"Throttle: {self.control.throttle:4.2f} | "
                f"Brake: {self.control.brake:4.2f} | "
                f"Steer: {self.control.steer:5.2f} | "
                f"Autopilot: {'ON ' if self.autopilot_enabled else 'OFF'} | "
                f"Z: {transform.location.z:5.1f}m | "
                f"Stuck: {self.stuck_counter}"
            )
            print(f"\r{status}", end="", flush=True)
    
    def run(self):
        """Main execution loop"""
        if not self.connect_to_carla():
            return False
        
        if not self.load_nurec_scenario():
            return False
        
        if not self.setup_vehicle():
            return False
        
        logger.info("Starting physics-fixed manual control loop...")
        logger.info("Controls: WASD=Drive, P=Autopilot, R=Restart, T=Teleport, ESC=Quit")
        logger.info("Watch the NUREC window for the reconstructed environment")
        logger.info("Vehicle status will be printed below:")
        
        running = True
        status_counter = 0
        
        try:
            while running:
                # Handle input
                running = self.handle_input()
                
                # Check vehicle physics
                self.check_vehicle_physics()
                
                # Tick the scenario and world (synchronous mode)
                if self.scenario:
                    self.scenario.tick()
                if self.world:
                    self.world.tick()
                
                # Print status every 20 frames
                status_counter += 1
                if status_counter >= 20:
                    self.print_status()
                    status_counter = 0
                
                # Control frame rate (20 FPS for stable physics)
                self.clock.tick(20)
                
        except KeyboardInterrupt:
            print("\nInterrupted by user")
            logger.info("Interrupted by user")
        except Exception as e:
            print(f"\nError in main loop: {e}")
            logger.error(f"Error in main loop: {e}")
            handle_exception(e)
        finally:
            print("\nCleaning up...")
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")
        
        # Reset synchronous mode
        if self.world:
            try:
                settings = self.world.get_settings()
                settings.synchronous_mode = False
                self.world.apply_settings(settings)
            except:
                pass
        
        if self.display:
            self.display.destroy()
        
        if self.scenario:
            try:
                self.scenario.__exit__(None, None, None)
            except:
                pass
        
        pygame.quit()
        logger.info("Cleanup complete")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Physics-Fixed Manual Control in NUREC Environment")
    parser.add_argument('--usdz-filename', required=True, help='Path to USDZ scenario file')
    parser.add_argument('--host', default='127.0.0.1', help='CARLA server IP')
    parser.add_argument('--port', default=2000, type=int, help='CARLA server port')
    parser.add_argument('--nurec-port', default=46435, type=int, help='NUREC service port')
    
    args = parser.parse_args()
    
    # Create and run the manual control
    manual_control = PhysicsFixedManualControlNurec(args)
    success = manual_control.run()
    
    if success:
        logger.info("Manual control session completed successfully")
    else:
        logger.error("Manual control session failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
