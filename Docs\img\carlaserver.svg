<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="537px" height="510px" viewBox="0 0 537 510" style="overflow: hidden; display: block; width: 537px; height: 510px;"><defs><path fill="none" stroke="rgb(0,0,0)" d="M 5,-5 L 0,0 L 5,5" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" id="ygc265_0"/></defs><g style="pointer-events:visiblePainted" transform="translate(156.92567760386044 20.50618007381408)" image-rendering="auto" shape-rendering="auto"><g><path fill="none" stroke="rgb(0,0,0)" d="M 86.98756170888696,61.71527054346981 L 23.15804408404972,102.76215598924884" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0.841097 -0.540885 0.540885 0.841097 23.158 102.762)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 22.316947280923344,103.30304060519813 L 86.14646490576058,62.25615515941911" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(-0.841097 0.540885 -0.540885 -0.841097 86.1465 62.2562)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 128.1064511553787,61.71527054346981 L 168.87284849501148,102.59495338143181" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(-0.706125 -0.708087 0.708087 -0.706125 168.873 102.595)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 169.57897347229164,103.30304060519813 L 128.81257613265885,62.42335776723614" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0.706125 0.708087 -0.708087 0.706125 128.813 62.4234)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 170.5665198328339,135.52449122248203 L 124.910417877685,184.3059553399262" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0.683331 -0.730108 0.730108 0.683331 124.91 184.306)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 253.33624708235868,185.32547302751453 L 202.90719756391522,136.2221180119001" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0.716461 0.697627 -0.697627 0.716461 202.907 136.222)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 112.04037177885584,213.05701303154947 L 112.04037177885584,254.0731738683125" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 -1 1 0 112.04 254.073)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 112.04037177885584,287.2946244855964 L 112.04037177885584,306.29462448559667" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 -1 1 0 112.04 306.295)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 112.04037177885584,339.51607510288056 L 112.04037177885584,358.5160751028805" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 -1 1 0 112.04 358.516)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 266.4115137541647,359.5160751028804 L 266.4115137541647,340.51607510288045" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 266.411 340.516)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 266.4115137541647,307.29462448559656 L 266.4115137541647,288.2946244855963" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 266.411 288.295)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M 266.4115137541647,255.07317386831238 L 266.4115137541647,214.05701303154947" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 266.411 214.057)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -42.33077019645299,287.2946244855965 L -42.33077019645299,306.29462448559656" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 -1 1 0 -42.3308 306.295)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -42.33077019645299,307.29462448559656 L -42.33077019645299,288.2946244855965" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 -42.3308 288.295)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -42.33077019645299,339.51607510288045 L -42.33077019645299,358.5160751028804" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 -1 1 0 -42.3308 358.516)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -42.33077019645299,359.5160751028804 L -42.33077019645299,340.51607510288045" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 -42.3308 340.516)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -18.95855705056482,135.52449122248203 L -68.3330646995495,184.55821627203107" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0.709551 -0.704654 0.704654 0.709551 -68.3331 184.558)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -74.23634519262218,212.2582568931679 L -51.53013973700557,254.19380390839885" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(-0.476139 -0.87937 0.87937 -0.476139 -51.5301 254.194)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -33.60753978850903,255.0731738683126 L -10.901334332892377,213.13762685308149" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(-0.476139 0.87937 -0.87937 -0.476139 -10.9013 213.138)" style="pointer-events: none;"/></g><g><path fill="none" stroke="rgb(0,0,0)" d="M -2.735862789045541,183.05701303154936 L -2.735862789045541,136.52449122248203" stroke-opacity="1" stroke-width="0.75" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"/><use xlink:href="#ygc265_0" transform="matrix(0 1 -1 0 -2.73586 136.524)" style="pointer-events: none;"/></g><g><path d="M -86.76937402018405,307.29462448559656 L 2.107833627278069,307.29462448559656 L 2.107833627278069,339.51607510288045 L -86.76937402018405,339.51607510288045 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M -86.76937402018405,255.0731738683126 L 2.107833627278069,255.0731738683126 L 2.107833627278069,287.2946244855965 L -86.76937402018405,287.2946244855965 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M -86.76937402018405,359.5160751028804 L 2.107833627278069,359.5160751028804 L 2.107833627278069,391.7375257201643 L -86.76937402018405,391.7375257201643 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M -47.1744666127766,103.30304060519813 L 41.702741034685516,103.30304060519813 L 41.702741034685516,135.52449122248203 L -47.1744666127766,135.52449122248203 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 141.20644902508343,103.30304060519813 L 230.08365667254554,103.30304060519813 L 230.08365667254554,135.52449122248203 L 141.20644902508343,135.52449122248203 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 112.04037177885584,213.05701303154947 C 98.23537177885584,213.05701303154947 87.04037177885584,206.34001303154946 87.04037177885584,198.05701303154947 C 87.04037177885584,189.7740130315495 98.23537177885584,183.05701303154947 112.04037177885584,183.05701303154947 C 125.84537177885585,183.05701303154947 137.04037177885584,189.7740130315495 137.04037177885584,198.05701303154947 C 137.04037177885584,206.34001303154946 125.84537177885585,213.05701303154947 112.04037177885584,213.05701303154947 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 266.4115137541647,213.05701303154947 C 252.60651375416467,213.05701303154947 241.41151375416467,206.34001303154946 241.41151375416467,198.05701303154947 C 241.41151375416467,189.7740130315495 252.60651375416467,183.05701303154947 266.4115137541647,183.05701303154947 C 280.2165137541647,183.05701303154947 291.4115137541647,189.7740130315495 291.4115137541647,198.05701303154947 C 291.4115137541647,206.34001303154946 280.2165137541647,213.05701303154947 266.4115137541647,213.05701303154947 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 67.60176795512479,29.49381992618592 L 156.4789756025869,29.49381992618592 L 156.4789756025869,61.71527054346981 L 67.60176795512479,61.71527054346981 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><rect fill="rgb(255,255,255)" stroke="rgb(0,0,0)" fill-opacity="0" stroke-opacity="1" stroke-width="1" stroke-dasharray="2,2,0.01,2" stroke-dashoffset="1" stroke-linecap="round" stroke-linejoin="miter" stroke-miterlimit="10" height="179.84" rx="3" ry="3" y="260.59" x="-106.926" width="129.19"/><g><path d="M 67.60176795512479,307.29462448559667 L 156.4789756025869,307.29462448559667 L 156.4789756025869,339.51607510288056 L 67.60176795512479,339.51607510288056 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 67.60176795512479,255.0731738683125 L 156.4789756025869,255.0731738683125 L 156.4789756025869,287.2946244855964 L 67.60176795512479,287.2946244855964 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 67.60176795512479,359.5160751028805 L 156.4789756025869,359.5160751028805 L 156.4789756025869,391.7375257201644 L 67.60176795512479,391.7375257201644 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><rect fill="rgb(255,255,255)" stroke="rgb(0,0,0)" fill-opacity="0" stroke-opacity="1" stroke-width="1" stroke-dasharray="2,2,0.01,2" stroke-dashoffset="1" stroke-linecap="round" stroke-linejoin="miter" stroke-miterlimit="10" height="179.84" rx="3" ry="3" y="260.59" x="47.4455" width="129.19"/><g><path d="M 221.97290993043362,307.29462448559656 L 310.85011757789573,307.29462448559656 L 310.85011757789573,339.51607510288045 L 221.97290993043362,339.51607510288045 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 221.97290993043362,255.07317386831238 L 310.85011757789573,255.07317386831238 L 310.85011757789573,287.2946244855963 L 221.97290993043362,287.2946244855963 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M 221.97290993043362,359.5160751028804 L 310.85011757789573,359.5160751028804 L 310.85011757789573,391.7375257201643 L 221.97290993043362,391.7375257201643 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><rect fill="rgb(255,255,255)" stroke="rgb(0,0,0)" fill-opacity="0" stroke-opacity="1" stroke-width="1" stroke-dasharray="2,2,0.01,2" stroke-dashoffset="1" stroke-linecap="round" stroke-linejoin="miter" stroke-miterlimit="10" height="179.84" rx="3" ry="3" y="259.596" x="201.817" width="129.19"/><g><path d="M -81.92567760386044,213.05701303154953 C -95.73067760386044,213.05701303154953 -106.92567760386044,206.34001303154952 -106.92567760386044,198.05701303154953 C -106.92567760386044,189.77401303154954 -95.73067760386044,183.05701303154953 -81.92567760386044,183.05701303154953 C -68.12067760386043,183.05701303154953 -56.925677603860436,189.77401303154954 -56.925677603860436,198.05701303154953 C -56.925677603860436,206.34001303154952 -68.12067760386043,213.05701303154953 -81.92567760386044,213.05701303154953 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g><path d="M -2.735862789045541,213.05701303154936 C -16.54086278904554,213.05701303154936 -27.73586278904554,206.34001303154935 -27.73586278904554,198.05701303154936 C -27.73586278904554,189.77401303154937 -16.54086278904554,183.05701303154936 -2.735862789045541,183.05701303154936 C 11.069137210954459,183.05701303154936 22.26413721095446,189.77401303154937 22.26413721095446,198.05701303154936 C 22.26413721095446,206.34001303154935 11.069137210954459,213.05701303154936 -2.735862789045541,213.05701303154936 Z" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" fill="rgb(246,248,250)" fill-opacity="1"/></g><g transform="translate(-86.76937402018405 307.29462448559656)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Encoder</tspan></text></g><g transform="translate(-86.76937402018405 255.0731738683126)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Async Server</tspan></text></g><g transform="translate(-86.76937402018405 359.5160751028804)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">TCP Server</tspan></text></g><g transform="translate(-47.1744666127766 103.30304060519813)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">World Server</tspan></text></g><g transform="translate(141.20644902508343 103.30304060519813)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Agent Server</tspan></text></g><g transform="translate(87.04037177885584 183.05701303154947)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(25 9.5)"><tspan dy="1em" x="0">Buffer</tspan></text></g><g transform="translate(241.41151375416467 183.05701303154947)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(25 9.5)"><tspan dy="1em" x="0">Buffer</tspan></text></g><g transform="translate(67.60176795512479 29.49381992618592)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">C API</tspan></text></g><g transform="translate(-72.60966766935094 421.47360068423836)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(30.278897472897953 0)"><tspan dy="1em" x="0">World Thread</tspan></text></g><g transform="translate(-66.85449845932725 395.5628033591353)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(24.523728262874258 0)"><tspan dy="1em" x="0">(port 2000)</tspan></text></g><g transform="translate(67.60176795512479 307.29462448559667)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Encoder</tspan></text></g><g transform="translate(67.60176795512479 255.0731738683125)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Async Server</tspan></text></g><g transform="translate(67.60176795512479 359.5160751028805)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">TCP Server</tspan></text></g><g transform="translate(61.60491825519859 421.9558537706581)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(50.43545352365725 0)"><tspan dy="1em" x="0">Measurements Thread</tspan></text></g><g transform="translate(87.51664351598158 394.5982971862958)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(24.523728262874258 0)"><tspan dy="1em" x="0">(port 2001)</tspan></text></g><g transform="translate(221.97290993043362 307.29462448559656)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Encoder</tspan></text></g><g transform="translate(221.97290993043362 255.07317386831238)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">Async Server</tspan></text></g><g transform="translate(221.97290993043362 359.5160751028804)"><text font-family="Arial" font-size="12px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(44.43860382373106 9.110725308641946)"><tspan dy="1em" x="0">TCP Server</tspan></text></g><g transform="translate(232.97229154705124 420.4801949588476)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(33.43922220711343 0)"><tspan dy="1em" x="0">Control Thread</tspan></text></g><g transform="translate(241.8877854912904 394.0871445473248)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(24.523728262874258 0)"><tspan dy="1em" x="0">(port 2002)</tspan></text></g><g transform="translate(-106.92567760386044 183.05701303154953)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(25 9.5)"><tspan dy="1em" x="0">Message</tspan></text></g><g transform="translate(-27.73586278904554 183.05701303154936)"><text font-family="Arial" font-size="10px" font-style="normal" font-weight="normal" text-anchor="middle" fill="rgb(0,0,0)" fill-opacity="1" transform="translate(25 9.5)"><tspan dy="1em" x="0">Message</tspan></text></g></g></svg>